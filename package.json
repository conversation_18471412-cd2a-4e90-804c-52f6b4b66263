{"name": "aura-monorepo", "private": true, "workspaces": ["aura", "backend-worker"], "scripts": {"dev": "pnpm -r run dev", "build": "pnpm -r run build", "deploy": "pnpm -r run deploy", "dev:frontend": "pnpm --filter voice-ai-assistant run dev", "dev:backend": "pnpm --filter aura-backend-worker run dev", "build:frontend": "pnpm --filter voice-ai-assistant run build", "build:backend": "pnpm --filter aura-backend-worker run build", "deploy:frontend": "pnpm --filter voice-ai-assistant run deploy", "deploy:backend": "pnpm --filter aura-backend-worker run deploy"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "resolutions": {"@graphql-tools/url-loader": "8.0.16"}, "author": "wlvar", "description": "Monorepo for Voice AI Shopping Assistant (Shopify, Gemini, Cloudflare Workers)", "repository": {"type": "git", "url": "https://github.com/YOUR_GITHUB_ORG/aura-monorepo.git"}, "license": "MIT", "devDependencies": {"wrangler": "^4.20.5"}}