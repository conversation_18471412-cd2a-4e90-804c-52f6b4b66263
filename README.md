# Voice AI Shopping Assistant Mon<PERSON>po (Shopify, Gemini & Cloudflare Workers)

## Project Overview

The Voice AI Shopping Assistant is a next-generation Shopify app that brings conversational, multilingual, and real-time AI voice assistance to e-commerce storefronts. It enables shoppers to:
- Search for products, get recommendations, and navigate the store using natural language voice commands.
- Interact in both **English and Albanian**, with seamless code-switching support.
- Experience instant, context-aware responses powered by Google Gemini 2.5 Pro and real-time streaming via Cloudflare RealtimeKit.

### High-Level Goals
- **Enhance the online shopping experience** with natural, voice-driven interaction.
- **Empower merchants** to offer cutting-edge AI features with minimal setup.
- **Leverage edge computing** for global low-latency and scalability.

---

## Key Features
- **Voice Interaction:** Shoppers can speak naturally to find products, ask questions, and get help.
- **Direct Audio Understanding:** Gemini 2.5 Pro processes audio directly (no separate STT step).
- **Multilingual & Code-Switching:** Supports English, Albanian, and seamless switching between them.
- **Real-time Communication:** Cloudflare RealtimeKit for low-latency audio/video and data messaging.
- **Function Calling:** Gemini interprets intent and triggers backend actions (e.g., product search, inventory check via Shopify APIs).
- **Text-Based Responses:** Assistant responses are displayed as text in the storefront widget.
- **Edge Deployment:** Backend logic runs on Cloudflare Workers for global performance.
- **Shopify Integration:** Easy install via Theme App Extensions, interacts with Shopify APIs.
- **Configurable:** Merchants can configure basic settings via the Shopify Admin.

---

## Technical Architecture

- **Frontend (`aura/`):**
  - Built with Remix, Vite, and React.
  - Captures audio, connects to Cloudflare RealtimeKit, publishes user audio, and displays AI responses.
  - Integrates with Shopify as a Theme App Extension.

- **Backend (`backend-worker/`):**
  - Runs on Cloudflare Workers for edge performance.
  - Hosts API endpoints, manages RealtimeKit tokens, and orchestrates AI agent logic.
  - Uses Google Gemini 2.5 Pro for direct audio understanding and function calling.
  - Handles state and context with Cloudflare KV/Durable Objects.

- **External Services:**
  - **Cloudflare RealtimeKit:** Real-time audio/video/data streaming and bot integration.
  - **Google Gemini 2.5 Pro (Vertex AI):** Multilingual, code-switching AI for audio and intent understanding.
  - **Shopify APIs:** Product, inventory, and order management.

---

## Monorepo Structure

```
/ (root)
├── aura/              # Frontend (Shopify app, Remix, Vite, React)
├── backend-worker/    # Cloudflare Worker backend (Gemini, RealtimeKit, API)
├── scripts/           # Project scripts and automation
├── tasks/             # Task management (Task Master)
├── docs/              # Documentation
├── package.json       # Monorepo scripts & pnpm workspaces
├── pnpm-workspace.yaml
└── README.md          # This file
```

---

## Getting Started

### Prerequisites
- [pnpm](https://pnpm.io/) (>=8.0.0)
- Node.js (>=18.0.0)
- Cloudflare account, Google Cloud project (Vertex AI/Gemini), Shopify dev store, Wrangler CLI, etc. (see `/docs/voice-assistant-setup.md`)

### Installation

```bash
pnpm install
```

### Development

- **Run both front and backend in dev mode:**
  ```bash
  pnpm dev
  ```
- **Run only frontend or backend:**
  ```bash
  pnpm dev:frontend
  pnpm dev:backend
  ```

### Build & Deploy

- **Build all:**
  ```bash
  pnpm build
  ```
- **Type check all workspaces:**
  ```bash
  pnpm -r exec tsc --noEmit
  ```
- **Deploy all:**
  ```bash
  pnpm deploy
  ```
- Or use `pnpm deploy:frontend` / `pnpm deploy:backend` for individual deploys.

### Environment Setup
- Copy `.env.example` to `.env` and fill in required variables for both front and backend.
- Backend secrets: use `wrangler secret put ...` as needed.
- See `/docs/voice-assistant-setup.md` for full details.

---

## Contributing

We welcome contributions! Please see `/docs/technical-guidelines.md` for coding standards and `/docs/implementation-plan.md` for open tasks.

### How to Contribute
1. Fork the repo and create a feature branch.
2. Make your changes (see project structure above).
3. Add/modify tests as needed.
4. Run `pnpm build` and ensure all checks pass.
5. Submit a pull request with a clear description.

### Project Management
- Tasks are managed in `/tasks/` and tracked in `/docs/implementation-plan.md`.
- Use issues and PRs for discussion and code review.

---

## License
MIT

---

*For more, see the detailed docs in `/docs/`.*
