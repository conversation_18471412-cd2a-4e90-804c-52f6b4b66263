/**
 * Voice Assistant UI Manager
 * 
 * This module handles the UI components of the voice assistant including
 * modal controls, message display, and user interactions with enhanced
 * liquid glass effects.
 */

import { LiquidGlassUtils, GlassUIComponents } from './liquid-glass-utils.js';
import { UniversalCardSystem } from './universal-card-system.js';

export class VoiceAssistantUI {
  constructor() {
    // UI elements
    this.container = null;
    this.modal = null;
    this.canvas = null;
    this.visualizer = null;
    this.closeButton = null;
    this.recordButton = null;
    this.loadingElement = null;
    this.messagesContainer = null;
    this.chatBubbleMessage = null;
    this.assistantNameElement = null;
    
    // Glass effects
    this.glassEffects = new Map();
    this.glassComponents = new Map();
    
    // Universal card system
    this.cardSystem = null;
    
    // State
    this.hasInteracted = false;
    this.isLoading = false;
    this.isListening = false;
    
    // Detect iOS Safari for glass effect adjustments
    this.isIOSSafari = this.detectIOSSafari();
    
    // Name cycling
    this.names = [
      '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 
      '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
      '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
      '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
    ];
    this.currentName = 'Shopping Assistant';
    this.nameInterval = null;
  }

  /**
   * Initialize the UI components
   * @param {Object} config - Configuration options
   * @param {HTMLElement} config.container - The main container element
   * @param {Function} config.onRecordStart - Callback when recording starts
   * @param {Function} config.onRecordStop - Callback when recording stops
   */
  init(config) {
    const { container, onRecordStart, onRecordStop } = config;
    
    if (!container) {
      console.error('[VA UI] Container element not provided');
      return false;
    }
    
    this.container = container;
    this.onRecordStart = onRecordStart || (() => {});
    this.onRecordStop = onRecordStop || (() => {});
    
    // Find UI elements
    this.findElements();
    
    // Apply theme color if provided
    this.applyTheme();
    
    // Apply iOS Safari specific fixes
    this.applyIOSSafariFixes();
    
    // Initialize glass effects
    this.initializeGlassEffects();
    
    // Initialize universal card system
    this.initializeCardSystem();
    
    // Initialize adaptive background detection
    this.initializeAdaptiveBackgroundDetection();
    
    // Attach event listeners
    this.attachEventListeners();
    
    // Start name cycling animation
    this.startNameCycling();
    
    // ----- Text input for typed queries -----
    this.textForm = document.createElement('form');
    this.textForm.className = 'va-text-input-form';
    this.textInput = document.createElement('input');
    this.textInput.type = 'text';
    this.textInput.placeholder = 'Type your question…';
    this.textInput.autocomplete = 'off';
    this.textInput.className = 'va-text-input';
    this.textForm.appendChild(this.textInput);
    this.container.appendChild(this.textForm);

    // Submit handler → dispatch same custom event as voice result
    this.textForm.addEventListener('submit', (e) => {
      e.preventDefault();
      const val = this.textInput.value.trim();
      if (!val) return;
      
      // Immediate feedback - disable input and show processing state
      this.textInput.disabled = true;
      this.textInput.placeholder = 'Processing...';
      
      this.addMessage(val, 'user');
      this.textInput.value = '';
      
      document.dispatchEvent(
        new CustomEvent('voice-assistant-text-query', { detail: { text: val } })
      );
      
      // Re-enable input after a short delay
      setTimeout(() => {
        this.textInput.disabled = false;
        this.textInput.placeholder = 'Type your question…';
        this.textInput.focus();
      }, 1000);
    });
    
    console.log('[VA UI] UI initialization complete with glass effects');
    return true;
  }
  
  /**
   * Find all required UI elements in the DOM
   */
  findElements() {
    this.canvas = document.getElementById('voice-assistant-canvas');
    this.visualizer = this.container.querySelector('.voice-assistant-visualizer');
    this.modal = this.container.querySelector('.voice-assistant-modal');
    this.closeButton = this.container.querySelector('.voice-assistant-close');
    this.recordButton = this.container.querySelector('.voice-assistant-record');
    this.messagesContainer = this.container.querySelector('.voice-assistant-messages');
    this.loadingElement = document.getElementById('voice-assistant-loading');
    this.chatBubbleMessage = document.getElementById('voice-assistant-chat-message');
    this.assistantNameElement = document.getElementById('voice-assistant-name');
  }
  
  /**
   * Apply theme color from data attribute
   */
  applyTheme() {
    const color = this.container.dataset.color;
    if (color) {
      document.documentElement.style.setProperty('--assistant-color', color);
    }
  }

  /**
   * Apply iOS Safari specific fixes for glass effects
   */
  applyIOSSafariFixes() {
    if (this.isIOSSafari) {
      console.log('[VA UI] Applying iOS Safari specific fixes for glass effects');
      
      // Add iOS Safari class to container for targeted CSS
      this.container.classList.add('ios-safari');
      document.documentElement.classList.add('ios-safari');
      
      // Force stronger glass effects via inline styles as backup
      setTimeout(() => {
        // Fix chat bubble
        const chatBubble = this.container.querySelector('.voice-assistant-chat-bubble');
        if (chatBubble) {
          chatBubble.style.background = 'rgba(255, 255, 255, 0.3)';
          chatBubble.style.webkitBackdropFilter = 'blur(30px) saturate(250%)';
          chatBubble.style.backdropFilter = 'blur(30px) saturate(250%)';
          chatBubble.style.border = '2px solid rgba(255, 255, 255, 0.5)';
          console.log('[VA UI] Applied inline iOS Safari fixes to chat bubble');
        }
        
        this.applyIOSSafariModalFixes();
      }, 100);
    }
  }

  /**
   * Apply iOS Safari specific fixes to modal elements
   */
  applyIOSSafariModalFixes() {
    if (!this.isIOSSafari) return;
    
    console.log('[VA UI] Applying iOS Safari modal fixes');
    
    // Fix modal
    const modal = this.container.querySelector('.voice-assistant-modal');
    if (modal) {
      modal.style.background = 'rgba(255, 255, 255, 0.25)';
      modal.style.webkitBackdropFilter = 'blur(40px) saturate(300%)';
      modal.style.backdropFilter = 'blur(40px) saturate(300%)';
      modal.style.border = '2px solid rgba(255, 255, 255, 0.4)';
      modal.style.boxShadow = '0 0 0 1px rgba(255, 255, 255, 0.25) inset, 0 32px 120px rgba(0, 0, 0, 0.6), 0 16px 60px rgba(0, 0, 0, 0.4)';
      console.log('[VA UI] Applied inline iOS Safari fixes to modal');
    }
    
    // Fix modal header
    const header = this.container.querySelector('.voice-assistant-header');
    if (header) {
      header.style.background = 'rgba(255, 255, 255, 0.15)';
      header.style.webkitBackdropFilter = 'blur(20px) saturate(200%)';
      header.style.backdropFilter = 'blur(20px) saturate(200%)';
      header.style.borderBottom = '1px solid rgba(255, 255, 255, 0.25)';
    }
    
    // Fix modal controls
    const controls = this.container.querySelector('.voice-assistant-controls');
    if (controls) {
      controls.style.background = 'rgba(255, 255, 255, 0.15)';
      controls.style.webkitBackdropFilter = 'blur(20px) saturate(200%)';
      controls.style.backdropFilter = 'blur(20px) saturate(200%)';
      controls.style.borderTop = '1px solid rgba(255, 255, 255, 0.25)';
    }
    
    // Fix message bubbles in modal
    const messageBubbles = this.container.querySelectorAll('.message');
    messageBubbles.forEach(bubble => {
      if (bubble.classList.contains('assistant')) {
        bubble.style.background = 'rgba(255, 255, 255, 0.2)';
        bubble.style.webkitBackdropFilter = 'blur(15px) saturate(200%)';
        bubble.style.backdropFilter = 'blur(15px) saturate(200%)';
        bubble.style.border = '1px solid rgba(255, 255, 255, 0.3)';
      } else if (bubble.classList.contains('user')) {
        bubble.style.background = 'rgba(139, 92, 246, 0.3)';
        bubble.style.webkitBackdropFilter = 'blur(15px) saturate(200%)';
        bubble.style.backdropFilter = 'blur(15px) saturate(200%)';
        bubble.style.border = '1px solid rgba(139, 92, 246, 0.4)';
      }
    });
  }

  /**
   * Initialize glass effects for existing UI elements
   */
  initializeGlassEffects() {
    try {
      // Apply glass effect to chat bubble (enhanced version of existing element)
      const chatBubble = this.container.querySelector('.voice-assistant-chat-bubble');
      if (chatBubble) {
        const glassEffect = LiquidGlassUtils.createGlassComponent(chatBubble, {
          cornerRadius: 18,
          blurAmount: 0.05,
          saturation: 120,
          overLight: false
        });
        this.glassEffects.set('chatBubble', glassEffect);
      }

      // Apply glass effect to modal (enhanced version of existing element)
      if (this.modal) {
        const glassEffect = LiquidGlassUtils.createGlassComponent(this.modal, {
          cornerRadius: 20,
          blurAmount: 0.1,
          saturation: 120,
          overLight: false
        });
        this.glassEffects.set('modal', glassEffect);
      }

      // Apply glass effect to floating mic button
      const floatingMic = this.container.querySelector('.voice-assistant-floating-mic');
      if (floatingMic) {
        const glassEffect = LiquidGlassUtils.createGlassComponent(floatingMic, {
          cornerRadius: 999,
          blurAmount: 0.05,
          saturation: 120,
          overLight: false
        });
        this.glassEffects.set('floatingMic', glassEffect);
      }

      console.log('[VA UI] Glass effects initialized successfully');
    } catch (error) {
      console.warn('[VA UI] Failed to initialize some glass effects:', error);
    }
  }

  /**
   * Initialize Universal Card System
   */
  initializeCardSystem() {
    try {
      if (!this.cardSystem) {
        this.cardSystem = new UniversalCardSystem({
          container: this.container,
          glassMorphism: true,
          animations: true
        });
        console.log('[VA UI] Universal Card System initialized');
      }
    } catch (error) {
      console.warn('[VA UI] Failed to initialize Universal Card System:', error);
      this.cardSystem = null;
    }
  }

  /**
   * Initialize adaptive background detection
   */
  initializeAdaptiveBackgroundDetection() {
    const adaptiveBackgroundClass = 'adaptive-background';
    const adaptiveBackgroundDarkClass = 'adaptive-background-dark';

    // Check if the background is light or dark
    const isBackgroundLight = this.isBackgroundLight();

    // Apply adaptive background class
    if (isBackgroundLight) {
      this.container.classList.add(adaptiveBackgroundClass);
      this.container.classList.remove(adaptiveBackgroundDarkClass);
    } else {
      this.container.classList.add(adaptiveBackgroundDarkClass);
      this.container.classList.remove(adaptiveBackgroundClass);
    }

    // Update adaptive background on theme change
    document.addEventListener('theme-change', () => {
      const isBackgroundLight = this.isBackgroundLight();
      if (isBackgroundLight) {
        this.container.classList.add(adaptiveBackgroundClass);
        this.container.classList.remove(adaptiveBackgroundDarkClass);
      } else {
        this.container.classList.add(adaptiveBackgroundDarkClass);
        this.container.classList.remove(adaptiveBackgroundClass);
      }
    });
  }

  /**
   * Check if the background is light or dark
   * @returns {boolean} Whether the background is light
   */
  isBackgroundLight() {
    const backgroundColor = getComputedStyle(this.container).backgroundColor;
    const rgb = backgroundColor.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
    if (!rgb) return false;

    const r = parseInt(rgb[1]);
    const g = parseInt(rgb[2]);
    const b = parseInt(rgb[3]);

    // Calculate the relative luminance of the background color
    const luminance = (0.2126 * r + 0.7152 * g + 0.0722 * b) / 255;

    // If the luminance is greater than 0.5, the background is considered light
    return luminance > 0.5;
  }

  /**
   * Create glass product cards for shopping results
   */
  createProductCards(products) {
    const productCards = [];
    
    products.forEach(product => {
      const card = GlassUIComponents.createProductCard({
        title: product.title,
        price: product.price,
        image: product.image,
        overLight: false
      });
      
      // Add click handler
      card.card.addEventListener('click', () => {
        if (product.handle) {
          window.location.href = `/products/${product.handle}`;
        }
      });
      
      productCards.push(card);
      this.glassComponents.set(`product-${product.id}`, card);
    });
    
    return productCards;
  }

  /**
   * Create glass chat messages
   */
  createGlassChatMessage(text, sender) {
    const chatBubble = GlassUIComponents.createChatBubble({
      message: text,
      sender: sender,
      overLight: false
    });
    
    this.glassComponents.set(`message-${Date.now()}`, chatBubble);
    return chatBubble;
  }

  /**
   * Create glass recording indicator
   */
  createRecordingIndicator() {
    const indicator = GlassUIComponents.createRecordingIndicator({
      overLight: false
    });
    
    this.glassComponents.set('recordingIndicator', indicator);
    return indicator;
  }
  
  /**
   * Attach event listeners to UI elements
   */
  attachEventListeners() {
    // Make visualizer clickable
    this.visualizer.addEventListener('click', () => {
      this.handleVisualizerClick();
    });
    
    // Close modal when close button is clicked
    this.closeButton.addEventListener('click', () => {
      this.closeModal();
    });
    
    // Start/stop listening when record button is clicked
    this.recordButton.addEventListener('click', () => {
      console.log(`[VA UI] Record button clicked. Current state: isListening = ${this.isListening}`);
      if (this.isListening) {
        this.stopListening();
      } else {
        this.startListening();
      }
    });
    
    // Close modal when clicking outside
    document.addEventListener('click', (event) => {
      if (this.modal.classList.contains('open') && 
          !this.container.contains(event.target)) {
        this.closeModal();
      }
    });
    
    // Listen for voice assistant errors
    document.addEventListener('voice-assistant-error', (event) => {
      console.log('[VA UI] Voice assistant error event received:', event.detail);
      this.handleError(event.detail.message || 'Sorry, there was a problem with the voice service.');
    });
    
    // Listen for voice assistant responses
    document.addEventListener('voice-assistant-response', (event) => {
      console.log('[VA UI] Voice assistant response event received:', event.detail);
      this.handleFinalResponse(event.detail);
    });
  }
  
  /**
   * Start cycling through random names in the assistant name element
   */
  startNameCycling() {
    this.nameInterval = setInterval(() => {
      if (!this.hasInteracted) {
        const randomIndex = Math.floor(Math.random() * this.names.length);
        
        // Fade out then in for smooth name transition
        this.assistantNameElement.style.opacity = '0';
        this.assistantNameElement.style.transform = 'translateY(10px)';
        
        setTimeout(() => {
          this.assistantNameElement.textContent = this.names[randomIndex];
          this.assistantNameElement.style.opacity = '1';
          this.assistantNameElement.style.transform = 'translateY(0)';
        }, 200);
      } else {
        clearInterval(this.nameInterval);
        
        // Fade out then in for final name
        this.assistantNameElement.style.opacity = '0';
        this.assistantNameElement.style.transform = 'translateY(10px)';
        
        setTimeout(() => {
          this.assistantNameElement.textContent = 'Shopping Assistant';
          this.assistantNameElement.style.opacity = '1';
          this.assistantNameElement.style.transform = 'translateY(0)';
        }, 200);
      }
    }, 2000);
  }
  
  /**
   * Stop name cycling
   */
  stopNameCycling() {
    if (this.nameInterval) {
      clearInterval(this.nameInterval);
      this.nameInterval = null;
    }
    
    // Set final name
    this.assistantNameElement.textContent = 'Shopping Assistant';
  }
  
  /**
   * Handle click on the visualizer
   */
  handleVisualizerClick() {
    if (!this.hasInteracted) {
      this.hasInteracted = true;
      this.setLoading(true);
      
      // Stop name cycling and set to Shopping Assistant
      this.stopNameCycling();
      
      // Clear message and show welcome
      setTimeout(() => {
        this.updateChatBubbleMessage('How can I help you shop today?');
        this.setLoading(false);
        this.showExpandedBubble();
      }, 1000);
      
      return;
    }
    
    // If already interacted, toggle expanded bubble
    if (this.chatBubble && this.chatBubble.classList.contains('expanded')) {
      this.hideExpandedBubble();
    } else {
      this.showExpandedBubble();
    }
  }
  
  /**
   * Set the loading state
   * @param {boolean} isLoading - Whether the assistant is in loading state
   */
  setLoading(isLoading) {
    this.isLoading = isLoading;

    // Lazily resolve loading element if it became null (e.g., after hot-reload)
    if (!this.loadingElement) {
      this.loadingElement = document.getElementById('voice-assistant-loading');
    }

    // Toggle container-level thinking class for enhanced loading visuals
    if (this.container) {
      this.container.classList.toggle('va-thinking', isLoading);
    }

    if (this.loadingElement) {
      if (isLoading) {
        this.loadingElement.classList.remove('hidden');
        // Add pulse animation for better visual feedback
        this.loadingElement.style.animation = 'pulse 1.5s ease-in-out infinite';
      } else {
        this.loadingElement.classList.add('hidden');
        this.loadingElement.style.animation = '';
      }
    }

    // Update record button state to show processing
    if (this.recordButton) {
      if (isLoading) {
        this.recordButton.classList.add('processing');
        this.recordButton.disabled = true;
      } else {
        this.recordButton.classList.remove('processing');
        this.recordButton.disabled = false;
      }
    }
  }
  
  /**
   * Update the chat bubble message with animation
   * @param {string} text - The new message text
   */
  updateChatBubbleMessage(text) {
    if (this.chatBubbleMessage) {
      // Get the parent bubble for additional animations
      const bubble = this.chatBubbleMessage.closest('.voice-assistant-chat-bubble');
      
      // Apply fade and transform for transition
      if (bubble) {
        bubble.style.opacity = '0';
        bubble.style.transform = 'translateY(10px)';
      } else {
        this.chatBubbleMessage.style.opacity = '0';
      }
      
      setTimeout(() => {
        // Clear existing content and add text
        this.chatBubbleMessage.innerHTML = '';
        const textNode = document.createElement('div');
        textNode.className = 'chat-bubble-text';
        textNode.textContent = text;
        this.chatBubbleMessage.appendChild(textNode);
        
        if (bubble) {
          bubble.style.opacity = '1';
          bubble.style.transform = 'translateY(0)';
        } else {
          this.chatBubbleMessage.style.opacity = '1';
        }
      }, 300);
    }
  }

  /**
   * Display integrated content in chat bubble using Universal Card System
   * @param {Object} response - Response object with message, cards, and/or products
   */
  displayChatBubbleContent(response) {
    if (!this.chatBubbleMessage) {
      console.warn('[VA UI] Chat bubble message element not found');
      return;
    }
    
    const bubble = this.chatBubbleMessage.closest('.voice-assistant-chat-bubble');
    
    // Apply fade and transform for transition
    if (bubble) {
      bubble.style.opacity = '0';
      bubble.style.transform = 'translateY(10px)';
    } else {
      this.chatBubbleMessage.style.opacity = '0';
    }
    
    setTimeout(() => {
      // PRESERVE EXISTING CONTENT: Don't clear everything, just add new content
      // Check if there's already content in the bubble
      let existingContent = this.chatBubbleMessage.innerHTML;
      let hasExistingText = existingContent.includes('chat-bubble-text') || existingContent.includes('chat-bubble-products');
      
      // Only clear if there's no meaningful existing content OR if it's just "Processing..." or similar temporary messages
      const tempMessages = ['Processing...', "I'm listening...", 'How can I help you shop today?'];
      const existingText = this.chatBubbleMessage.textContent || '';
      const shouldClear = !hasExistingText || tempMessages.some(msg => existingText.includes(msg));
      
      if (shouldClear) {
        this.chatBubbleMessage.innerHTML = '';
      }
      
      // Create main content container for better structure
      const contentContainer = document.createElement('div');
      contentContainer.className = 'chat-bubble-content-container';
      
      // Add text message if provided
      if (response.message && response.message.trim()) {
        const textNode = document.createElement('div');
        textNode.className = 'chat-bubble-text';
        textNode.textContent = response.message;
        contentContainer.appendChild(textNode);
      }
      
      // Use Universal Card System for structured content
      if (response.cards && Array.isArray(response.cards) && response.cards.length > 0) {
        console.log('[VA UI] Rendering cards using Universal Card System:', response.cards);
        
        if (this.cardSystem) {
          const cardsElement = this.cardSystem.renderCards(response.cards);
          if (cardsElement) {
            // Add separator if there's both text and cards
            if (response.message && response.message.trim()) {
              cardsElement.classList.add('has-preceding-text');
            }
            contentContainer.appendChild(cardsElement);
          }
        } else {
          console.warn('[VA UI] Card system not initialized, falling back to legacy product display');
          this.fallbackToLegacyDisplay(response, contentContainer);
        }
      } 
      // Fallback for legacy products array (backward compatibility)
      else if (response.products && Array.isArray(response.products) && response.products.length > 0) {
        console.log('[VA UI] Using legacy product display for backward compatibility');
        this.addProductsToChatBubble(response.products, contentContainer);
      }
      // Fallback to text-only display
      else if (!response.message && response.text) {
        const textNode = document.createElement('div');
        textNode.className = 'chat-bubble-text';
        textNode.textContent = response.text;
        contentContainer.appendChild(textNode);
      }
      
      // Add the content container to the chat bubble (append, don't replace)
      this.chatBubbleMessage.appendChild(contentContainer);
      
      // Restore visibility with improved animation
      if (bubble) {
        bubble.style.opacity = '1';
        bubble.style.transform = 'translateY(0)';
        
        // Add a subtle pulse effect to highlight new content
        bubble.classList.add('content-updated');
        setTimeout(() => {
          bubble.classList.remove('content-updated');
        }, 600);
      } else {
        this.chatBubbleMessage.style.opacity = '1';
      }
    }, 300);
  }

  /**
   * Fallback method for legacy response format
   * @param {Object} response - Response with legacy format
   */
  fallbackToLegacyDisplay(response) {
    if (response.products && response.products.length > 0) {
      this.addProductsToChatBubble(response.products);
    }
  }

  /**
   * Add product cards to chat bubble
   * @param {Array} products - Array of product objects
   */
  addProductsToChatBubble(products) {
    if (!products.length) return;
    
    const productContainer = document.createElement('div');
    productContainer.className = 'chat-bubble-products';
    productContainer.style.cssText = `
      display: flex;
      gap: 12px;
      overflow-x: auto;
      padding: 12px 0;
      margin-top: 8px;
    `;
    
    products.slice(0, 3).forEach((product, index) => {
      const card = document.createElement('div');
      card.className = 'chat-bubble-product-card';
      card.setAttribute('aria-label', `Product: ${product.title || 'Unknown'}`);
      card.setAttribute('tabindex', '0');
      card.style.cssText = `
        min-width: 140px;
        max-width: 160px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 8px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        flex-direction: column;
        gap: 6px;
      `;
      
      // Add hover effects
      card.addEventListener('mouseenter', () => {
        card.style.transform = 'translateY(-2px)';
        card.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.15)';
      });
      
      card.addEventListener('mouseleave', () => {
        card.style.transform = 'translateY(0)';
        card.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
      });
      
      // Product image container
      const imageContainer = document.createElement('div');
      imageContainer.className = 'product-image-container';
      imageContainer.style.cssText = `
        width: 100%;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        background: #f8f9fa;
        display: flex;
        align-items: center;
        justify-content: center;
      `;
      
      if (product.image) {
        const img = document.createElement('img');
        img.src = product.image;
        img.alt = product.title || 'Product image';
        img.loading = 'lazy';
        img.className = 'product-image';
        img.style.cssText = `
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 8px;
        `;
        imageContainer.appendChild(img);
      } else {
        // Fallback for products without images
        const placeholder = document.createElement('div');
        placeholder.className = 'product-image-placeholder';
        placeholder.innerHTML = '<svg width="32" height="32" viewBox="0 0 24 24" fill="none"><rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/><circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21h14a1 1 0 0 1 1 1v1" stroke="currentColor" stroke-width="2"/></svg>';
        imageContainer.appendChild(placeholder);
      }
      
      card.appendChild(imageContainer);
      
      // Product details container
      const detailsContainer = document.createElement('div');
      detailsContainer.className = 'product-details';
      detailsContainer.style.cssText = `
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 4px;
      `;
      
      // Product title
      if (product.title) {
        const title = document.createElement('div');
        title.className = 'product-title';
        title.textContent = product.title;
        title.title = product.title; // Tooltip for full title
        title.style.cssText = `
          font-size: 12px;
          font-weight: 500;
          color: #333;
          line-height: 1.2;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        `;
        detailsContainer.appendChild(title);
      }
      
      // Price and compare price
      const priceContainer = document.createElement('div');
      priceContainer.className = 'product-price-container';
      priceContainer.style.cssText = `
        display: flex;
        align-items: center;
        gap: 6px;
        flex-wrap: wrap;
      `;
      
      if (product.price) {
        const price = document.createElement('div');
        price.className = 'product-price';
        price.textContent = product.price;
        price.style.cssText = `
          font-size: 13px;
          font-weight: 600;
          color: #1a73e8;
        `;
        priceContainer.appendChild(price);
      }
      
      if (product.compareAtPrice && product.compareAtPrice !== product.price) {
        const comparePrice = document.createElement('div');
        comparePrice.className = 'product-compare-price';
        comparePrice.textContent = product.compareAtPrice;
        priceContainer.appendChild(comparePrice);
        
        // Add sale badge
        const saleBadge = document.createElement('div');
        saleBadge.className = 'product-sale-badge';
        saleBadge.textContent = 'SALE';
        imageContainer.appendChild(saleBadge);
      }
      
      detailsContainer.appendChild(priceContainer);
      
      // Variant info (if available)
      if (product.variants && product.variants.length > 1) {
        const variantInfo = document.createElement('div');
        variantInfo.className = 'product-variant-info';
        variantInfo.textContent = `${product.variants.length} variants`;
        detailsContainer.appendChild(variantInfo);
      }
      
      // Rating (if available)
      if (product.rating) {
        const ratingContainer = document.createElement('div');
        ratingContainer.className = 'product-rating';
        
        const stars = document.createElement('div');
        stars.className = 'product-stars';
        const fullStars = Math.floor(product.rating);
        const hasHalfStar = product.rating % 1 >= 0.5;
        
        for (let i = 0; i < 5; i++) {
          const star = document.createElement('span');
          if (i < fullStars) {
            star.innerHTML = '★';
            star.className = 'star-filled';
          } else if (i === fullStars && hasHalfStar) {
            star.innerHTML = '☆';
            star.className = 'star-half';
          } else {
            star.innerHTML = '☆';
            star.className = 'star-empty';
          }
          stars.appendChild(star);
        }
        
        ratingContainer.appendChild(stars);
        
        if (product.reviewCount) {
          const reviewCount = document.createElement('span');
          reviewCount.className = 'review-count';
          reviewCount.textContent = `(${product.reviewCount})`;
          ratingContainer.appendChild(reviewCount);
        }
        
        detailsContainer.appendChild(ratingContainer);
      }
      
      card.appendChild(detailsContainer);
      
      // Action buttons container
      const actionsContainer = document.createElement('div');
      actionsContainer.className = 'product-actions';
      actionsContainer.style.cssText = `
        display: flex;
        gap: 6px;
        margin-top: 6px;
      `;
      
      // Add to cart button
      const addToCartBtn = document.createElement('button');
      addToCartBtn.className = 'product-add-to-cart';
      addToCartBtn.innerHTML = '<svg width="14" height="14" viewBox="0 0 24 24" fill="none"><path d="M3 3h2l.4 2M7 13h10l4-8H5.4m1.6 8L6 21h14a1 1 0 0 1 1 1v1" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><circle cx="9" cy="20" r="1" stroke="currentColor" stroke-width="2"/><circle cx="20" cy="20" r="1" stroke="currentColor" stroke-width="2"/></svg>';
      addToCartBtn.setAttribute('aria-label', `Add ${product.title || 'product'} to cart`);
      addToCartBtn.title = 'Add to cart';
      addToCartBtn.style.cssText = `
        flex: 1;
        padding: 6px 8px;
        background: #1a73e8;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 11px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        transition: all 0.2s ease;
      `;
      
      addToCartBtn.addEventListener('mouseenter', () => {
        addToCartBtn.style.background = '#1557b0';
      });
      
      addToCartBtn.addEventListener('mouseleave', () => {
        addToCartBtn.style.background = '#1a73e8';
      });
      
      // Add to cart click handler
      addToCartBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.addProductToCart(product);
      });
      
      actionsContainer.appendChild(addToCartBtn);
      
      // Quick view button
      const quickViewBtn = document.createElement('button');
      quickViewBtn.className = 'product-quick-view';
      quickViewBtn.innerHTML = '<svg width="14" height="14" viewBox="0 0 24 24" fill="none"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" stroke="currentColor" stroke-width="2"/><circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/></svg>';
      quickViewBtn.setAttribute('aria-label', `Quick view ${product.title || 'product'}`);
      quickViewBtn.title = 'Quick view';
      quickViewBtn.style.cssText = `
        padding: 6px 8px;
        background: rgba(0, 0, 0, 0.05);
        color: #333;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 6px;
        font-size: 11px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
      `;
      
      quickViewBtn.addEventListener('mouseenter', () => {
        quickViewBtn.style.background = 'rgba(0, 0, 0, 0.1)';
      });
      
      quickViewBtn.addEventListener('mouseleave', () => {
        quickViewBtn.style.background = 'rgba(0, 0, 0, 0.05)';
      });
      
      // Quick view click handler
      quickViewBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        this.showProductQuickView(product);
      });
      
      actionsContainer.appendChild(quickViewBtn);
      
      card.appendChild(actionsContainer);
      
      // Main click handler for product navigation
      card.addEventListener('click', () => {
        if (product.handle) {
          window.location.href = `/products/${product.handle}`;
        }
      });
      
      // Keyboard accessibility
      card.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          if (product.handle) {
            window.location.href = `/products/${product.handle}`;
          }
        }
      });
      
      productContainer.appendChild(card);
    });
    
    this.chatBubbleMessage.appendChild(productContainer);
  }

  /**
   * Add product to cart
   * @param {Object} product - Product object
   */
  async addProductToCart(product) {
    try {
      console.log('[VA UI] Adding product to cart:', product);
      
      // Show loading state
      const addToCartBtns = document.querySelectorAll('.product-add-to-cart');
      addToCartBtns.forEach(btn => {
        btn.classList.add('loading');
        btn.disabled = true;
      });
      
      // Get the first available variant ID
      const variantId = product.variants?.[0]?.id || product.variantId || product.id;
      
      if (!variantId) {
        throw new Error('No variant ID available for this product');
      }
      
      // Add to cart via Shopify Cart API
      const response = await fetch('/cart/add.js', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: variantId,
          quantity: 1
        })
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add to cart');
      }
      
      const cartData = await response.json();
      console.log('[VA UI] Product added to cart:', cartData);
      
      // Show success feedback
      this.showCartSuccessMessage(product);
      
      // Update cart count if cart drawer exists
      this.updateCartCount();
      
    } catch (error) {
      console.error('[VA UI] Error adding to cart:', error);
      this.showCartErrorMessage(error.message);
    } finally {
      // Reset button states
      setTimeout(() => {
        const addToCartBtns = document.querySelectorAll('.product-add-to-cart');
        addToCartBtns.forEach(btn => {
          btn.classList.remove('loading');
          btn.disabled = false;
        });
      }, 1000);
    }
  }

  /**
   * Show product quick view
   * @param {Object} product - Product object  
   */
  showProductQuickView(product) {
    console.log('[VA UI] Showing quick view for:', product);
    
    // Create quick view overlay
    const overlay = document.createElement('div');
    overlay.className = 'product-quick-view-overlay';
    
    const quickViewModal = document.createElement('div');
    quickViewModal.className = 'product-quick-view-modal';
    
    quickViewModal.innerHTML = `
      <div class="quick-view-header">
        <h3>${product.title || 'Product Details'}</h3>
        <button class="quick-view-close" aria-label="Close quick view">×</button>
      </div>
      <div class="quick-view-content">
        <div class="quick-view-image">
          ${product.image ? `<img src="${product.image}" alt="${product.title || 'Product image'}" />` : '<div class="image-placeholder">No image available</div>'}
        </div>
        <div class="quick-view-details">
          <div class="quick-view-price">
            ${product.price ? `<span class="price">${product.price}</span>` : ''}
            ${product.compareAtPrice && product.compareAtPrice !== product.price ? `<span class="compare-price">${product.compareAtPrice}</span>` : ''}
          </div>
          ${product.description ? `<div class="quick-view-description">${product.description}</div>` : ''}
          <div class="quick-view-actions">
            <button class="quick-view-add-to-cart">Add to Cart</button>
            <a href="/products/${product.handle}" class="quick-view-full-details">View Full Details</a>
          </div>
        </div>
      </div>
    `;
    
    overlay.appendChild(quickViewModal);
    document.body.appendChild(overlay);
    
    // Event listeners
    const closeBtn = quickViewModal.querySelector('.quick-view-close');
    const addToCartBtn = quickViewModal.querySelector('.quick-view-add-to-cart');
    
    closeBtn.addEventListener('click', () => {
      document.body.removeChild(overlay);
    });
    
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        document.body.removeChild(overlay);
      }
    });
    
    addToCartBtn.addEventListener('click', () => {
      this.addProductToCart(product);
      document.body.removeChild(overlay);
    });
    
    // Add animation
    requestAnimationFrame(() => {
      overlay.classList.add('visible');
    });
  }

  /**
   * Show cart success message
   * @param {Object} product - Product that was added
   */
  showCartSuccessMessage(product) {
    this.updateChatBubbleMessage(`✅ Added "${product.title}" to cart!`);
    
    // Reset message after delay
    setTimeout(() => {
      this.updateChatBubbleMessage('How can I help you shop today?');
    }, 3000);
  }

  /**
   * Show cart error message  
   * @param {string} errorMessage - Error message to display
   */
  showCartErrorMessage(errorMessage) {
    this.updateChatBubbleMessage(`❌ ${errorMessage}`);
    
    // Reset message after delay
    setTimeout(() => {
      this.updateChatBubbleMessage('How can I help you shop today?');
    }, 3000);
  }

  /**
   * Update cart count indicator
   */
  updateCartCount() {
    // Try to update Shopify's cart count if available
    fetch('/cart.js')
      .then(response => response.json())
      .then(cart => {
        const cartCountElements = document.querySelectorAll('[data-cart-count], .cart-count, #cart-count');
        cartCountElements.forEach(element => {
          element.textContent = cart.item_count;
        });
        
        // Dispatch cart update event for themes that use it
        document.dispatchEvent(new CustomEvent('cart:updated', {
          detail: { cart }
        }));
      })
      .catch(error => {
        console.warn('[VA UI] Could not update cart count:', error);
      });
  }
  
  /**
   * Show expanded bubble interface with inline controls
   */
  showExpandedBubble() {
    if (!this.chatBubble) return;
    
    // Add expanded class for enhanced bubble interface
    this.chatBubble.classList.add('expanded');
    
    // Create inline controls within the bubble
    this.createInlineBubbleControls();
    
    // Show welcome message if no existing conversation
    if (!this.bubbleMessagesContainer.children.length) {
      this.addBubbleMessage('assistant', 'How can I help you shop today?');
    }
    
    // Apply iOS Safari fixes
    if (this.isIOSSafari) {
      setTimeout(() => {
        this.applyIOSSafariFixes();
      }, 50);
    }
  }
  
  /**
   * Hide expanded bubble interface
   */
  hideExpandedBubble() {
    if (!this.chatBubble) return;
    
    this.chatBubble.classList.remove('expanded');
    
    // Remove inline controls
    const existingControls = this.chatBubble.querySelector('.bubble-inline-controls');
    if (existingControls) {
      existingControls.remove();
    }
  }
  
  /**
   * Create inline controls within the message bubble
   */
  createInlineBubbleControls() {
    if (!this.chatBubble) return;
    
    // Remove existing controls if any
    const existingControls = this.chatBubble.querySelector('.bubble-inline-controls');
    if (existingControls) {
      existingControls.remove();
    }
    
    // Create controls container
    const controlsContainer = document.createElement('div');
    controlsContainer.className = 'bubble-inline-controls';
    controlsContainer.innerHTML = `
      <div class="bubble-messages-container">
        <div class="bubble-messages-list"></div>
        <div class="bubble-loading-status">
          <div class="bubble-loading-spinner">
            <div class="vasp-dot"></div>
            <div class="vasp-dot"></div>
            <div class="vasp-dot"></div>
          </div>
          <div class="bubble-loading-text">Processing...</div>
          <div class="bubble-loading-dots">
            <div class="bubble-loading-dot"></div>
            <div class="bubble-loading-dot"></div>
            <div class="bubble-loading-dot"></div>
          </div>
        </div>
      </div>
      <div class="bubble-control-row">
        <button class="bubble-record-btn" title="Voice Input">
          <svg viewBox="0 0 24 24" width="16" height="16">
            <path fill="currentColor" d="M12 2C10.34 2 9 3.34 9 5v6c0 1.66 1.34 3 3 3s3-1.34 3-3V5c0-1.66-1.34-3-3-3zm5.3 6c0 3-2.54 5.1-5.3 5.1S6.7 11 6.7 8H5c0 3.41 2.72 6.23 6 6.72V17h2v-2.28c3.28-.49 6-3.31 6-6.72h-1.7z"/>
          </svg>
        </button>
        <div class="bubble-text-input-container">
          <input type="text" class="bubble-text-input" placeholder="Type message..." />
          <button class="bubble-send-btn" title="Send">
            <svg viewBox="0 0 24 24" width="16" height="16">
              <path fill="currentColor" d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
            </svg>
          </button>
        </div>
      </div>
      <div class="bubble-status-indicator"></div>
    `;
    
    // Add controls to bubble
    this.chatBubble.appendChild(controlsContainer);
    
    // Initialize bubble messages container and loading status
    this.bubbleMessagesContainer = controlsContainer.querySelector('.bubble-messages-list');
    this.bubbleLoadingStatus = controlsContainer.querySelector('.bubble-loading-status');
    this.bubbleLoadingText = controlsContainer.querySelector('.bubble-loading-text');
    
    // Copy existing messages from modal to bubble if any
    this.copyMessagesToBubble();
    
    // Initialize with current bubble message if exists and no messages copied
    const currentBubbleText = this.chatBubble?.querySelector('.voice-assistant-chat-bubble-message')?.textContent;
    if (currentBubbleText && currentBubbleText.trim() && !this.bubbleMessagesContainer.children.length) {
      this.addBubbleMessage('assistant', currentBubbleText.trim());
    }
    
    // Attach event listeners to controls
    this.attachBubbleControlListeners(controlsContainer);
  }
  
  /**
   * Attach event listeners to bubble controls
   */
  attachBubbleControlListeners(controlsContainer) {
    const recordBtn = controlsContainer.querySelector('.bubble-record-btn');
    const textInput = controlsContainer.querySelector('.bubble-text-input');
    const sendBtn = controlsContainer.querySelector('.bubble-send-btn');
    
    // Voice recording
    recordBtn.addEventListener('click', () => {
      if (this.isListening) {
        this.stopListening();
      } else {
        this.startListening();
      }
    });
    
    // Text input
    textInput.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        this.sendBubbleTextMessage(textInput.value.trim());
      }
    });
    
    // Send button
    sendBtn.addEventListener('click', () => {
      this.sendBubbleTextMessage(textInput.value.trim());
    });
  }
  
  /**
   * Send text message from bubble interface
   */
  sendBubbleTextMessage(message) {
    if (!message) return;
    
    // Add user message to bubble
    this.addBubbleMessage('user', message);
    
    // Clear input
    const textInput = this.chatBubble.querySelector('.bubble-text-input');
    if (textInput) {
      textInput.value = '';
    }
    
    // Dispatch text query event
    document.dispatchEvent(new CustomEvent('voice-assistant-text-query', {
      detail: { query: message }
    }));
    
    // Re-enable input after a short delay
    setTimeout(() => {
      this.textInput.disabled = false;
      this.textInput.placeholder = 'Type your question…';
      this.textInput.focus();
    }, 1000);
  }
  
  /**
   * Copy existing messages from modal to bubble
   */
  copyMessagesToBubble() {
    if (!this.messagesContainer || !this.bubbleMessagesContainer) return;
    
    // Copy all modal messages to bubble with proper formatting
    const modalMessages = this.messagesContainer.querySelectorAll('.message, .glass-message');
    modalMessages.forEach((message, index) => {
      const messageText = message.textContent || message.innerText || '';
      const isAssistant = message.classList.contains('assistant-message') || 
                         message.classList.contains('glass-message-assistant') ||
                         index % 2 === 1; // Fallback: assume alternating pattern
      
      if (messageText.trim() && messageText !== 'Processing...') {
        this.addBubbleMessage(isAssistant ? 'assistant' : 'user', messageText.trim());
      }
    });
    
    // Scroll to bottom
    this.scrollBubbleMessages();
  }
  
  /**
   * Add message to bubble messages container
   */
  addBubbleMessage(type, content, products = null) {
    if (!this.bubbleMessagesContainer) return;
    
    const messageDiv = document.createElement('div');
    messageDiv.classList.add('bubble-message', `bubble-message-${type}`);
    
    if (type === 'user') {
      messageDiv.innerHTML = `
        <div class="bubble-message-content user-message">
          <span class="bubble-message-text">${content}</span>
        </div>
      `;
    } else {
      messageDiv.innerHTML = `
        <div class="bubble-message-content assistant-message">
          <span class="bubble-message-text">${content}</span>
        </div>
      `;
      
      // Add products if provided
      if (products && products.length > 0) {
        const productsContainer = document.createElement('div');
        productsContainer.className = 'bubble-products-container';
        
        const productCards = this.createProductCards(products);
        productCards.forEach(card => {
          const miniCard = card.card.cloneNode(true);
          miniCard.classList.add('bubble-mini-product-card');
          productsContainer.appendChild(miniCard);
        });
        
        messageDiv.appendChild(productsContainer);
      }
    }
    
    this.bubbleMessagesContainer.appendChild(messageDiv);
    this.scrollBubbleMessages();
  }
  
  /**
   * Scroll bubble messages to bottom
   */
  scrollBubbleMessages() {
    if (this.bubbleMessagesContainer) {
      this.bubbleMessagesContainer.scrollTop = this.bubbleMessagesContainer.scrollHeight;
    }
  }
  
  /**
   * Show loading status in bubble with custom message
   */
  showBubbleLoadingStatus(message = 'Processing...') {
    if (!this.bubbleLoadingStatus || !this.bubbleLoadingText) return;
    
    this.bubbleLoadingText.textContent = message;
    this.bubbleLoadingStatus.classList.add('active');
    
    // Show status indicator as well
    const statusIndicator = this.chatBubble?.querySelector('.bubble-status-indicator');
    if (statusIndicator) {
      statusIndicator.classList.add('active');
    }
  }
  
  /**
   * Hide loading status in bubble
   */
  hideBubbleLoadingStatus() {
    if (this.bubbleLoadingStatus) {
      this.bubbleLoadingStatus.classList.remove('active');
    }
    
    // Hide status indicator as well
    const statusIndicator = this.chatBubble?.querySelector('.bubble-status-indicator');
    if (statusIndicator) {
      statusIndicator.classList.remove('active');
    }
  }
  
  /**
   * Show tool execution indicator in bubble
   */
  showBubbleToolIndicator(toolName, status = 'executing') {
    if (!this.bubbleMessagesContainer) return;
    
    const toolIndicator = document.createElement('div');
    toolIndicator.className = 'bubble-tool-indicator';
    toolIndicator.innerHTML = `
      <div class="bubble-tool-icon">🔧</div>
      <div class="bubble-tool-text">${status === 'executing' ? 'Executing' : 'Completed'} ${toolName}</div>
    `;
    
    // Add unique ID for removal
    toolIndicator.dataset.toolId = `tool-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    this.bubbleMessagesContainer.appendChild(toolIndicator);
    this.scrollBubbleMessages();
    
    return toolIndicator.dataset.toolId;
  }
  
  /**
   * Update tool indicator status
   */
  updateBubbleToolIndicator(toolId, status, result = '') {
    if (!this.bubbleMessagesContainer) return;
    
    const toolIndicator = this.bubbleMessagesContainer.querySelector(`[data-tool-id="${toolId}"]`);
    if (toolIndicator) {
      const toolText = toolIndicator.querySelector('.bubble-tool-text');
      const toolIcon = toolIndicator.querySelector('.bubble-tool-icon');
      
      if (status === 'completed') {
        toolIcon.textContent = '✅';
        toolText.textContent = `Completed: ${result || 'Tool execution finished'}`;
        toolIndicator.style.background = 'rgba(34, 197, 94, 0.15)';
        toolIndicator.style.borderColor = 'rgba(34, 197, 94, 0.3)';
      } else if (status === 'error') {
        toolIcon.textContent = '❌';
        toolText.textContent = `Error: ${result || 'Tool execution failed'}`;
        toolIndicator.style.background = 'rgba(239, 68, 68, 0.15)';
        toolIndicator.style.borderColor = 'rgba(239, 68, 68, 0.3)';
        toolIndicator.style.color = 'rgba(239, 68, 68, 0.9)';
      }
    }
  }
  
  /**
   * Start listening for voice input
   */
  startListening() {
    console.log('[VA UI] startListening called.');
    
    // DISPATCH EVENT TO STOP ANY PLAYING AUDIO BEFORE RECORDING
    document.dispatchEvent(new CustomEvent('voice-assistant-stop-audio'));
    
    // Show listening state immediately for better UX
    this.isListening = true;
    
    // Update bubble controls if expanded
    const bubbleRecordBtn = this.chatBubble?.querySelector('.bubble-record-btn');
    if (bubbleRecordBtn) {
      bubbleRecordBtn.classList.add('recording');
    }
    
    // Update modal record button if present
    if (this.recordButton) {
      this.recordButton.classList.add('recording');
      this.recordButton.querySelector('span').textContent = 'Listening...';
    }
    
    this.updateChatBubbleMessage('I\'m listening...');
    
    // Show listening status in bubble if expanded
    if (this.chatBubble && this.chatBubble.classList.contains('expanded')) {
      this.showBubbleLoadingStatus('Listening for your voice...');
    }
    
    // Show glass recording indicator
    const recordingIndicator = this.createRecordingIndicator();
    this.visualizer.appendChild(recordingIndicator.indicator);
    recordingIndicator.show();
    
    // Call the provided callback
    this.onRecordStart();
  }
  
  /**
   * Stop listening for voice input
   */
  stopListening() {
    if (!this.isListening) {
      console.log('[VA UI] stopListening called but already stopped.');
      return;
    }
    
    console.log('[VA UI] stopListening called.');
    
    // Update UI state
    this.isListening = false;
    
    // Update bubble controls if expanded
    const bubbleRecordBtn = this.chatBubble?.querySelector('.bubble-record-btn');
    if (bubbleRecordBtn) {
      bubbleRecordBtn.classList.remove('recording');
    }
    
    // Update modal record button if present
    if (this.recordButton) {
      this.recordButton.classList.remove('recording');
      this.recordButton.querySelector('span').textContent = 'Tap to speak';
    }
    
    // Hide recording indicator
    const recordingIndicator = this.glassComponents.get('recordingIndicator');
    if (recordingIndicator) {
      recordingIndicator.hide();
      setTimeout(() => {
        recordingIndicator.destroy();
        this.glassComponents.delete('recordingIndicator');
      }, 300);
    }
    
    // Add a loading message to indicate processing
    this.addMessage('Processing...', 'assistant');
    this.updateChatBubbleMessage('Processing...');
    this.setLoading(true);
    
    // Show enhanced loading status in bubble if expanded
    this.showBubbleLoadingStatus('Processing your request...');
    
    // Show tool execution indicators for common operations
    if (this.chatBubble && this.chatBubble.classList.contains('expanded')) {
      // Simulate tool execution indicators (these would be triggered by actual tool calls)
      setTimeout(() => {
        const speechToolId = this.showBubbleToolIndicator('Speech Recognition');
        setTimeout(() => {
          this.updateBubbleToolIndicator(speechToolId, 'completed', 'Voice transcribed successfully');
          const nlpToolId = this.showBubbleToolIndicator('Natural Language Processing');
          setTimeout(() => {
            this.updateBubbleToolIndicator(nlpToolId, 'completed', 'Intent analyzed');
          }, 800);
        }, 600);
      }, 300);
    }
    
    // Call the provided callback
    this.onRecordStop();
  }
  
  /**
   * Add a message to the conversation
   * @param {string} text - Message text
   * @param {string} sender - Sender ('user' or 'assistant')
   */
  addMessage(text, sender) {
    // Add to bubble messages if expanded
    if (this.chatBubble && this.chatBubble.classList.contains('expanded')) {
      this.addBubbleMessage(sender, text);
    }
    
    // Create glass message bubble for modal (if modal exists)
    if (this.messagesContainer) {
      const glassBubble = this.createGlassChatMessage(text, sender);
      this.messagesContainer.appendChild(glassBubble.bubble);
      this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
    }
    
    // Update chat bubble if it's from assistant
    if (sender === 'assistant') {
      this.updateChatBubbleMessage(text);
    }
  }
  
  /**
   * Handle error messages
   * @param {string} errorMessage - Error message to display
   */
  handleError(errorMessage) {
    console.log(`[VA UI] handleError called with message: "${errorMessage}"`);
    // Stop listening if active
    if (this.isListening) {
      console.log('[VA UI] Stopping listening due to error.');
      this.stopListening();
    }
    
    // Remove loading state if active
    this.setLoading(false);
    
    // Show user-friendly error message
    const friendlyMessage = this.getFriendlyErrorMessage(errorMessage);
    this.addMessage(friendlyMessage, 'assistant');
    this.updateChatBubbleMessage(friendlyMessage);
    
    // Auto-recover after error
    setTimeout(() => {
      this.updateChatBubbleMessage('How can I help you shop today?');
    }, 4000);
  }

  /**
   * Convert technical error messages to user-friendly ones
   * @param {string} errorMessage - Technical error message
   * @returns {string} User-friendly error message
   */
  getFriendlyErrorMessage(errorMessage) {
    if (errorMessage.includes('microphone') || errorMessage.includes('permission')) {
      return 'Please allow microphone access to use voice features. You can also type your question below.';
    }
    if (errorMessage.includes('network') || errorMessage.includes('failed to fetch')) {
      return 'Connection issue. Please check your internet and try again.';
    }
    if (errorMessage.includes('Admin API credentials')) {
      return 'Product search is temporarily unavailable. Please try browsing our categories.';
    }
    if (errorMessage.includes('timeout') || errorMessage.includes('Processing failed')) {
      return 'Request timed out. Please try asking your question again.';
    }
    // Default friendly message
    return 'Something went wrong. Please try again or type your question below.';
  }
  
  /**
   * Handle final response from voice assistant
   * @param {Object} response - Response object from backend
   */
  handleFinalResponse(response) {
    
    // Remove the loading message ("Processing...") from modal
    if (this.messagesContainer && this.messagesContainer.lastChild && this.messagesContainer.lastChild.textContent === 'Processing...') {
      this.messagesContainer.removeChild(this.messagesContainer.lastChild);
    }
    
    // Remove loading message from bubble if expanded
    if (this.bubbleMessagesContainer && this.bubbleMessagesContainer.lastChild) {
      const lastBubbleMessage = this.bubbleMessagesContainer.lastChild.querySelector('.bubble-message-text');
      if (lastBubbleMessage && lastBubbleMessage.textContent === 'Processing...') {
        this.bubbleMessagesContainer.removeChild(this.bubbleMessagesContainer.lastChild);
      }
    }
    
    // Hide loading status in bubble
    this.hideBubbleLoadingStatus();
    
    // Add response message with products to bubble
    if (this.chatBubble && this.chatBubble.classList.contains('expanded')) {
      this.addBubbleMessage('assistant', response.message || "I received a response, but it was empty.", response.products);
    }
    
    this.addMessage(response.message || "I received a response, but it was empty.", 'assistant');
    this.setLoading(false);
    
    // Handle any actions returned from the AI
    if (response.action === 'search' && response.query) {
      console.log(`[VA UI] Executing search action for query: "${response.query}"`);
      window.location.href = `/search?q=${encodeURIComponent(response.query)}`;
    } else if (response.action === 'product' && response.handle) {
       console.log(`[VA UI] Executing product navigation action for handle: "${response.handle}"`);
      window.location.href = `/products/${response.handle}`;
    } else if (response.action === 'collection' && response.handle) {
       console.log(`[VA UI] Executing collection navigation action for handle: "${response.handle}"`);
      window.location.href = `/collections/${response.handle}`;
    } else if (response.action && response.action !== 'none') {
        console.warn(`[VA UI] Received unknown action: "${response.action}"`);
    }

    // Show product recommendations ONLY in chat bubble (modal is disabled for floating interface)
    // Note: showProductRecommendations adds to modal, which we don't want for floating UI
    // Instead, use unified bubble content display which handles both text and products
    this.displayChatBubbleContent({
      text: response.message || "I received a response, but it was empty.",
      products: response.products
    });
    
    // Add products to bubble message if expanded
    if (this.chatBubble && this.chatBubble.classList.contains('expanded') && response.products) {
      // The addMessage method already handles products, so this is handled above
    }
  }

  /**
   * Show product recommendations with glass cards
   */
  showProductRecommendations(products) {
    if (!products.length) return;
    if (!this.messagesContainer) {
      console.warn('[VA UI] messagesContainer is null; cannot render product recommendations inside modal. Falling back to bubble only.');
      return;
    }

    const productContainer = document.createElement('div');
    productContainer.className = 'glass-product-recommendations';
    productContainer.style.cssText = `
      display: flex;
      gap: 12px;
      overflow-x: auto;
      padding: 16px;
      margin-top: 12px;
    `;

    const productCards = this.createProductCards(products);
    productCards.forEach(card => {
      productContainer.appendChild(card.card);
    });

    this.messagesContainer.appendChild(productContainer);
    this.messagesContainer.scrollTop = this.messagesContainer.scrollHeight;
  }


  
  /**
   * Detect iOS Safari specifically
   * @returns {boolean} Whether the user is on iOS Safari
   */
  detectIOSSafari() {
    const userAgent = navigator.userAgent;
    const isIOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream;
    const isSafari = /Safari/.test(userAgent) && !/Chrome|CriOS|FxiOS/.test(userAgent);
    return isIOS && isSafari;
  }

  /**
   * Detect if user is on a mobile device
   * @returns {boolean} Whether the user is on a mobile device
   */
  detectMobileDevice() {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    
    // Check for common mobile patterns
    if (/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase())) {
      return true;
    }
    
    // iOS detection needs special handling
    if (/iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream) {
      return true;
    }
    
    // Check screen size as a fallback
    if (window.innerWidth <= 800 && window.innerHeight <= 900) {
      return true;
    }
    
    return false;
  }
  
  /**
   * Clean up resources
   */
  destroy() {
    console.log('[VA UI] Destroying VoiceAssistantUI...');
    
    if (this.nameInterval) {
      clearInterval(this.nameInterval);
      this.nameInterval = null;
    }

    // Clean up glass effects
    this.glassEffects.forEach(effect => {
      try {
        effect.destroy();
      } catch (error) {
        console.warn('[VA UI] Error destroying glass effect:', error);
      }
    });
    this.glassEffects.clear();

    // Clean up glass components
    this.glassComponents.forEach(component => {
      try {
        component.destroy();
      } catch (error) {
        console.warn('[VA UI] Error destroying glass component:', error);
      }
    });
    this.glassComponents.clear();
    
    console.log('[VA UI] Voice assistant UI resources cleaned up.');
  }

  /**
   * Sets the visual state of the UI to reflect listening status.
   * @param {boolean} isListening - Whether the assistant is listening.
   */
  setListeningState(isListening) {
    this.isListening = isListening;

    if (isListening) {
      // Update button in the main modal if it exists
      if (this.recordButton) {
        this.recordButton.classList.add('recording');
        this.recordButton.querySelector('span').textContent = 'Listening...';
      }
      // Update floating chat bubble
      this.updateChatBubbleMessage("I'm listening...");
    } else {
      // Update button in the main modal
      if (this.recordButton) {
        this.recordButton.classList.remove('recording');
        this.recordButton.querySelector('span').textContent = 'Tap to speak';
      }
      // Note: The main controller handles the "Processing..." message.
    }
  }

  /**
   * Set thinking state during automation steps
   * @param {boolean} isThinking - Whether assistant is currently thinking/automating
   * @param {string} message - Optional message to display
   */
  setThinkingState(isThinking, message = '') {
    if (isThinking) {
      this.setLoading(true);
      if (message) this.updateChatBubbleMessage(message);

      if (this.recordButton) {
        this.recordButton.disabled = true;
        this.recordButton.classList.add('disabled');
      }
    } else {
      this.setLoading(false);
      if (message) this.updateChatBubbleMessage(message);

      if (this.recordButton) {
        this.recordButton.disabled = false;
        this.recordButton.classList.remove('disabled');
      }
    }
  }
}
