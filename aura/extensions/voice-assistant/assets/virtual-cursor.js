'use strict';

/**
 * Manages a virtual cursor on the screen to show automation activity.
 */
export class VirtualCursor {
  constructor() {
    this.cursorElement = null;
    this.fadeTimeout = null;
    this._createCursorElement();
  }

  /**
   * Creates the cursor div and appends it to the body.
   * @private
   */
  _createCursorElement() {
    if (document.querySelector('.voice-assistant-virtual-cursor')) return;

    this.cursorElement = document.createElement('div');
    this.cursorElement.className = 'voice-assistant-virtual-cursor';
    document.body.appendChild(this.cursorElement);
  }

  /**
   * Moves the cursor to a specific set of coordinates.
   * @param {number} x - The x-coordinate.
   * @param {number} y - The y-coordinate.
   */
  moveTo(x, y) {
    if (!this.cursorElement) return;
    this.cursorElement.style.transform = `translate(${x}px, ${y}px)`;
  }
  
  /**
   * Moves the cursor to the center of a given HTML element.
   * @param {HTMLElement} element - The target element.
   */
  async moveToElement(element) {
    if (!element || !this.cursorElement) return;

    const rect = element.getBoundingClientRect();
    const x = rect.left + window.scrollX + rect.width / 2;
    const y = rect.top + window.scrollY + rect.height / 2;

    this.show();
    this.moveTo(x, y);

    // Brief pause to make the movement visible
    await new Promise(resolve => setTimeout(resolve, 300));
  }

  /**
   * Makes the cursor visible and handles fade-out.
   */
  show() {
    if (!this.cursorElement) return;
    
    clearTimeout(this.fadeTimeout);
    this.cursorElement.style.opacity = '1';

    this.fadeTimeout = setTimeout(() => {
      if (this.cursorElement) {
        this.cursorElement.style.opacity = '0';
      }
    }, 2000); // Fade out after 2 seconds of inactivity
  }

  /**
   * Hides the cursor immediately.
   */
  hide() {
    if (!this.cursorElement) return;
    this.cursorElement.style.opacity = '0';
  }

  /**
   * Clicks and shows a ripple effect.
   */
  click() {
    if (!this.cursorElement) return;
    
    this.show(); // Ensure it's visible for the click
    const ripple = document.createElement('div');
    ripple.className = 'ripple';
    this.cursorElement.appendChild(ripple);
    
    setTimeout(() => ripple.remove(), 500);
  }
  
  /**
   * Removes the cursor element from the DOM.
   */
  destroy() {
    clearTimeout(this.fadeTimeout);
    if (this.cursorElement) {
      this.cursorElement.remove();
      this.cursorElement = null;
    }
  }
} 