!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).LivekitClient={})}(this,(function(e){"use strict";function t(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(n){if("default"!==n&&!(n in e)){var i=Object.getOwnPropertyDescriptor(t,n);Object.defineProperty(e,n,i.get?i:{enumerable:!0,get:function(){return t[n]}})}}))})),Object.freeze(e)}var n=Object.defineProperty,i=(e,t,i)=>((e,t,i)=>t in e?n(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i)(e,"symbol"!=typeof t?t+"":t,i);class s{constructor(){i(this,"_locking"),i(this,"_locks"),this._locking=Promise.resolve(),this._locks=0}isLocked(){return this._locks>0}lock(){let e;this._locks+=1;const t=new Promise((t=>e=()=>{this._locks-=1,t()})),n=this._locking.then((()=>e));return this._locking=this._locking.then((()=>t)),n}}function o(e,t){if(!e)throw new Error(t)}const r=34028234663852886e22,a=-34028234663852886e22,c=4294967295,d=2147483647,l=-2147483648;function u(e){if("number"!=typeof e)throw new Error("invalid int 32: "+typeof e);if(!Number.isInteger(e)||e>d||e<l)throw new Error("invalid int 32: "+e)}function h(e){if("number"!=typeof e)throw new Error("invalid uint 32: "+typeof e);if(!Number.isInteger(e)||e>c||e<0)throw new Error("invalid uint 32: "+e)}function p(e){if("number"!=typeof e)throw new Error("invalid float 32: "+typeof e);if(Number.isFinite(e)&&(e>r||e<a))throw new Error("invalid float 32: "+e)}const m=Symbol("@bufbuild/protobuf/enum-type");function g(e){const t=e[m];return o(t,"missing enum type on enum object"),t}function v(e,t,n,i){e[m]=f(t,n.map((t=>({no:t.no,name:t.name,localName:e[t.no]}))))}function f(e,t,n){const i=Object.create(null),s=Object.create(null),o=[];for(const e of t){const t=b(e);o.push(t),i[e.name]=t,s[e.no]=t}return{typeName:e,values:o,findName:e=>i[e],findNumber:e=>s[e]}}function k(e,t,n){const i={};for(const e of t){const t=b(e);i[t.localName]=t.no,i[t.no]=t.localName}return v(i,e,t),i}function b(e){return"localName"in e?e:Object.assign(Object.assign({},e),{localName:e.name})}class y{equals(e){return this.getType().runtime.util.equals(this.getType(),this,e)}clone(){return this.getType().runtime.util.clone(this)}fromBinary(e,t){const n=this.getType().runtime.bin,i=n.makeReadOptions(t);return n.readMessage(this,i.readerFactory(e),e.byteLength,i),this}fromJson(e,t){const n=this.getType(),i=n.runtime.json,s=i.makeReadOptions(t);return i.readMessage(n,e,s,this),this}fromJsonString(e,t){let n;try{n=JSON.parse(e)}catch(e){throw new Error("cannot decode ".concat(this.getType().typeName," from JSON: ").concat(e instanceof Error?e.message:String(e)))}return this.fromJson(n,t)}toBinary(e){const t=this.getType().runtime.bin,n=t.makeWriteOptions(e),i=n.writerFactory();return t.writeMessage(this,i,n),i.finish()}toJson(e){const t=this.getType().runtime.json,n=t.makeWriteOptions(e);return t.writeMessage(this,n)}toJsonString(e){var t;const n=this.toJson(e);return JSON.stringify(n,null,null!==(t=null==e?void 0:e.prettySpaces)&&void 0!==t?t:0)}toJSON(){return this.toJson({emitDefaultValues:!0})}getType(){return Object.getPrototypeOf(this).constructor}}function T(){let e=0,t=0;for(let n=0;n<28;n+=7){let i=this.buf[this.pos++];if(e|=(127&i)<<n,0==(128&i))return this.assertBounds(),[e,t]}let n=this.buf[this.pos++];if(e|=(15&n)<<28,t=(112&n)>>4,0==(128&n))return this.assertBounds(),[e,t];for(let n=3;n<=31;n+=7){let i=this.buf[this.pos++];if(t|=(127&i)<<n,0==(128&i))return this.assertBounds(),[e,t]}throw new Error("invalid varint")}function C(e,t,n){for(let i=0;i<28;i+=7){const s=e>>>i,o=!(s>>>7==0&&0==t),r=255&(o?128|s:s);if(n.push(r),!o)return}const i=e>>>28&15|(7&t)<<4,s=!(t>>3==0);if(n.push(255&(s?128|i:i)),s){for(let e=3;e<31;e+=7){const i=t>>>e,s=!(i>>>7==0),o=255&(s?128|i:i);if(n.push(o),!s)return}n.push(t>>>31&1)}}const S=4294967296;function E(e){const t="-"===e[0];t&&(e=e.slice(1));const n=1e6;let i=0,s=0;function o(t,o){const r=Number(e.slice(t,o));s*=n,i=i*n+r,i>=S&&(s+=i/S|0,i%=S)}return o(-24,-18),o(-18,-12),o(-12,-6),o(-6),t?R(i,s):P(i,s)}function w(e,t){if(({lo:e,hi:t}=function(e,t){return{lo:e>>>0,hi:t>>>0}}(e,t)),t<=2097151)return String(S*t+e);const n=16777215&(e>>>24|t<<8),i=t>>16&65535;let s=(16777215&e)+6777216*n+6710656*i,o=n+8147497*i,r=2*i;const a=1e7;return s>=a&&(o+=Math.floor(s/a),s%=a),o>=a&&(r+=Math.floor(o/a),o%=a),r.toString()+I(o)+I(s)}function P(e,t){return{lo:0|e,hi:0|t}}function R(e,t){return t=~t,e?e=1+~e:t+=1,P(e,t)}const I=e=>{const t=String(e);return"0000000".slice(t.length)+t};function O(e,t){if(e>=0){for(;e>127;)t.push(127&e|128),e>>>=7;t.push(e)}else{for(let n=0;n<9;n++)t.push(127&e|128),e>>=7;t.push(1)}}function D(){let e=this.buf[this.pos++],t=127&e;if(0==(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<7,0==(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<14,0==(128&e))return this.assertBounds(),t;if(e=this.buf[this.pos++],t|=(127&e)<<21,0==(128&e))return this.assertBounds(),t;e=this.buf[this.pos++],t|=(15&e)<<28;for(let t=5;0!=(128&e)&&t<10;t++)e=this.buf[this.pos++];if(0!=(128&e))throw new Error("invalid varint");return this.assertBounds(),t>>>0}const x=function(){const e=new DataView(new ArrayBuffer(8));if("function"==typeof BigInt&&"function"==typeof e.getBigInt64&&"function"==typeof e.getBigUint64&&"function"==typeof e.setBigInt64&&"function"==typeof e.setBigUint64&&("object"!=typeof process||"object"!=typeof process.env||"1"!==process.env.BUF_BIGINT_DISABLE)){const t=BigInt("-9223372036854775808"),n=BigInt("9223372036854775807"),i=BigInt("0"),s=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(e){const i="bigint"==typeof e?e:BigInt(e);if(i>n||i<t)throw new Error("int64 invalid: ".concat(e));return i},uParse(e){const t="bigint"==typeof e?e:BigInt(e);if(t>s||t<i)throw new Error("uint64 invalid: ".concat(e));return t},enc(t){return e.setBigInt64(0,this.parse(t),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},uEnc(t){return e.setBigInt64(0,this.uParse(t),!0),{lo:e.getInt32(0,!0),hi:e.getInt32(4,!0)}},dec:(t,n)=>(e.setInt32(0,t,!0),e.setInt32(4,n,!0),e.getBigInt64(0,!0)),uDec:(t,n)=>(e.setInt32(0,t,!0),e.setInt32(4,n,!0),e.getBigUint64(0,!0))}}const t=e=>o(/^-?[0-9]+$/.test(e),"int64 invalid: ".concat(e)),n=e=>o(/^[0-9]+$/.test(e),"uint64 invalid: ".concat(e));return{zero:"0",supported:!1,parse:e=>("string"!=typeof e&&(e=e.toString()),t(e),e),uParse:e=>("string"!=typeof e&&(e=e.toString()),n(e),e),enc:e=>("string"!=typeof e&&(e=e.toString()),t(e),E(e)),uEnc:e=>("string"!=typeof e&&(e=e.toString()),n(e),E(e)),dec:(e,t)=>function(e,t){let n=P(e,t);const i=2147483648&n.hi;i&&(n=R(n.lo,n.hi));const s=w(n.lo,n.hi);return i?"-"+s:s}(e,t),uDec:(e,t)=>w(e,t)}}();var M,N,_;function A(e,t,n){if(t===n)return!0;if(e==M.BYTES){if(!(t instanceof Uint8Array&&n instanceof Uint8Array))return!1;if(t.length!==n.length)return!1;for(let e=0;e<t.length;e++)if(t[e]!==n[e])return!1;return!0}switch(e){case M.UINT64:case M.FIXED64:case M.INT64:case M.SFIXED64:case M.SINT64:return t==n}return!1}function L(e,t){switch(e){case M.BOOL:return!1;case M.UINT64:case M.FIXED64:case M.INT64:case M.SFIXED64:case M.SINT64:return 0==t?x.zero:"0";case M.DOUBLE:case M.FLOAT:return 0;case M.BYTES:return new Uint8Array(0);case M.STRING:return"";default:return 0}}function U(e,t){switch(e){case M.BOOL:return!1===t;case M.STRING:return""===t;case M.BYTES:return t instanceof Uint8Array&&!t.byteLength;default:return 0==t}}!function(e){e[e.DOUBLE=1]="DOUBLE",e[e.FLOAT=2]="FLOAT",e[e.INT64=3]="INT64",e[e.UINT64=4]="UINT64",e[e.INT32=5]="INT32",e[e.FIXED64=6]="FIXED64",e[e.FIXED32=7]="FIXED32",e[e.BOOL=8]="BOOL",e[e.STRING=9]="STRING",e[e.BYTES=12]="BYTES",e[e.UINT32=13]="UINT32",e[e.SFIXED32=15]="SFIXED32",e[e.SFIXED64=16]="SFIXED64",e[e.SINT32=17]="SINT32",e[e.SINT64=18]="SINT64"}(M||(M={})),function(e){e[e.BIGINT=0]="BIGINT",e[e.STRING=1]="STRING"}(N||(N={})),function(e){e[e.Varint=0]="Varint",e[e.Bit64=1]="Bit64",e[e.LengthDelimited=2]="LengthDelimited",e[e.StartGroup=3]="StartGroup",e[e.EndGroup=4]="EndGroup",e[e.Bit32=5]="Bit32"}(_||(_={}));class j{constructor(e){this.stack=[],this.textEncoder=null!=e?e:new TextEncoder,this.chunks=[],this.buf=[]}finish(){this.chunks.push(new Uint8Array(this.buf));let e=0;for(let t=0;t<this.chunks.length;t++)e+=this.chunks[t].length;let t=new Uint8Array(e),n=0;for(let e=0;e<this.chunks.length;e++)t.set(this.chunks[e],n),n+=this.chunks[e].length;return this.chunks=[],t}fork(){return this.stack.push({chunks:this.chunks,buf:this.buf}),this.chunks=[],this.buf=[],this}join(){let e=this.finish(),t=this.stack.pop();if(!t)throw new Error("invalid state, fork stack empty");return this.chunks=t.chunks,this.buf=t.buf,this.uint32(e.byteLength),this.raw(e)}tag(e,t){return this.uint32((e<<3|t)>>>0)}raw(e){return this.buf.length&&(this.chunks.push(new Uint8Array(this.buf)),this.buf=[]),this.chunks.push(e),this}uint32(e){for(h(e);e>127;)this.buf.push(127&e|128),e>>>=7;return this.buf.push(e),this}int32(e){return u(e),O(e,this.buf),this}bool(e){return this.buf.push(e?1:0),this}bytes(e){return this.uint32(e.byteLength),this.raw(e)}string(e){let t=this.textEncoder.encode(e);return this.uint32(t.byteLength),this.raw(t)}float(e){p(e);let t=new Uint8Array(4);return new DataView(t.buffer).setFloat32(0,e,!0),this.raw(t)}double(e){let t=new Uint8Array(8);return new DataView(t.buffer).setFloat64(0,e,!0),this.raw(t)}fixed32(e){h(e);let t=new Uint8Array(4);return new DataView(t.buffer).setUint32(0,e,!0),this.raw(t)}sfixed32(e){u(e);let t=new Uint8Array(4);return new DataView(t.buffer).setInt32(0,e,!0),this.raw(t)}sint32(e){return u(e),O(e=(e<<1^e>>31)>>>0,this.buf),this}sfixed64(e){let t=new Uint8Array(8),n=new DataView(t.buffer),i=x.enc(e);return n.setInt32(0,i.lo,!0),n.setInt32(4,i.hi,!0),this.raw(t)}fixed64(e){let t=new Uint8Array(8),n=new DataView(t.buffer),i=x.uEnc(e);return n.setInt32(0,i.lo,!0),n.setInt32(4,i.hi,!0),this.raw(t)}int64(e){let t=x.enc(e);return C(t.lo,t.hi,this.buf),this}sint64(e){let t=x.enc(e),n=t.hi>>31;return C(t.lo<<1^n,(t.hi<<1|t.lo>>>31)^n,this.buf),this}uint64(e){let t=x.uEnc(e);return C(t.lo,t.hi,this.buf),this}}class F{constructor(e,t){this.varint64=T,this.uint32=D,this.buf=e,this.len=e.length,this.pos=0,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength),this.textDecoder=null!=t?t:new TextDecoder}tag(){let e=this.uint32(),t=e>>>3,n=7&e;if(t<=0||n<0||n>5)throw new Error("illegal tag: field no "+t+" wire type "+n);return[t,n]}skip(e,t){let n=this.pos;switch(e){case _.Varint:for(;128&this.buf[this.pos++];);break;case _.Bit64:this.pos+=4;case _.Bit32:this.pos+=4;break;case _.LengthDelimited:let n=this.uint32();this.pos+=n;break;case _.StartGroup:for(;;){const[e,n]=this.tag();if(n===_.EndGroup){if(void 0!==t&&e!==t)throw new Error("invalid end group tag");break}this.skip(n,e)}break;default:throw new Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(n,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let e=this.uint32();return e>>>1^-(1&e)}int64(){return x.dec(...this.varint64())}uint64(){return x.uDec(...this.varint64())}sint64(){let[e,t]=this.varint64(),n=-(1&e);return e=(e>>>1|(1&t)<<31)^n,t=t>>>1^n,x.dec(e,t)}bool(){let[e,t]=this.varint64();return 0!==e||0!==t}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return x.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return x.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let e=this.uint32(),t=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(t,t+e)}string(){return this.textDecoder.decode(this.bytes())}}function B(e){const t=e.field.localName,n=Object.create(null);return n[t]=function(e){const t=e.field;if(t.repeated)return[];if(void 0!==t.default)return t.default;switch(t.kind){case"enum":return t.T.values[0].no;case"scalar":return L(t.T,t.L);case"message":const e=t.T,n=new e;return e.fieldWrapper?e.fieldWrapper.unwrapField(n):n;case"map":throw"map fields are not allowed to be extensions"}}(e),[n,()=>n[t]]}let V="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split(""),q=[];for(let e=0;e<V.length;e++)q[V[e].charCodeAt(0)]=e;q["-".charCodeAt(0)]=V.indexOf("+"),q["_".charCodeAt(0)]=V.indexOf("/");const K={dec(e){let t=3*e.length/4;"="==e[e.length-2]?t-=2:"="==e[e.length-1]&&(t-=1);let n,i=new Uint8Array(t),s=0,o=0,r=0;for(let t=0;t<e.length;t++){if(n=q[e.charCodeAt(t)],void 0===n)switch(e[t]){case"=":o=0;case"\n":case"\r":case"\t":case" ":continue;default:throw Error("invalid base64 string.")}switch(o){case 0:r=n,o=1;break;case 1:i[s++]=r<<2|(48&n)>>4,r=n,o=2;break;case 2:i[s++]=(15&r)<<4|(60&n)>>2,r=n,o=3;break;case 3:i[s++]=(3&r)<<6|n,o=0}}if(1==o)throw Error("invalid base64 string.");return i.subarray(0,s)},enc(e){let t,n="",i=0,s=0;for(let o=0;o<e.length;o++)switch(t=e[o],i){case 0:n+=V[t>>2],s=(3&t)<<4,i=1;break;case 1:n+=V[s|t>>4],s=(15&t)<<2,i=2;break;case 2:n+=V[s|t>>6],n+=V[63&t],i=0}return i&&(n+=V[s],n+="=",1==i&&(n+="=")),n}};function W(e,t,n){z(t,e);const i=t.runtime.bin.makeReadOptions(n),s=function(e,t){if(!t.repeated&&("enum"==t.kind||"scalar"==t.kind)){for(let n=e.length-1;n>=0;--n)if(e[n].no==t.no)return[e[n]];return[]}return e.filter((e=>e.no===t.no))}(e.getType().runtime.bin.listUnknownFields(e),t.field),[o,r]=B(t);for(const e of s)t.runtime.bin.readField(o,i.readerFactory(e.data),t.field,e.wireType,i);return r()}function H(e,t,n,i){z(t,e);const s=t.runtime.bin.makeReadOptions(i),o=t.runtime.bin.makeWriteOptions(i);if(G(e,t)){const n=e.getType().runtime.bin.listUnknownFields(e).filter((e=>e.no!=t.field.no));e.getType().runtime.bin.discardUnknownFields(e);for(const t of n)e.getType().runtime.bin.onUnknownField(e,t.no,t.wireType,t.data)}const r=o.writerFactory();let a=t.field;a.opt||a.repeated||"enum"!=a.kind&&"scalar"!=a.kind||(a=Object.assign(Object.assign({},t.field),{opt:!0})),t.runtime.bin.writeField(a,n,r,o);const c=s.readerFactory(r.finish());for(;c.pos<c.len;){const[t,n]=c.tag(),i=c.skip(n,t);e.getType().runtime.bin.onUnknownField(e,t,n,i)}}function G(e,t){const n=e.getType();return t.extendee.typeName===n.typeName&&!!n.runtime.bin.listUnknownFields(e).find((e=>e.no==t.field.no))}function z(e,t){o(e.extendee.typeName==t.getType().typeName,"extension ".concat(e.typeName," can only be applied to message ").concat(e.extendee.typeName))}function J(e,t){const n=e.localName;if(e.repeated)return t[n].length>0;if(e.oneof)return t[e.oneof.localName].case===n;switch(e.kind){case"enum":case"scalar":return e.opt||e.req?void 0!==t[n]:"enum"==e.kind?t[n]!==e.T.values[0].no:!U(e.T,t[n]);case"message":return void 0!==t[n];case"map":return Object.keys(t[n]).length>0}}function Q(e,t){const n=e.localName,i=!e.opt&&!e.req;if(e.repeated)t[n]=[];else if(e.oneof)t[e.oneof.localName]={case:void 0};else switch(e.kind){case"map":t[n]={};break;case"enum":t[n]=i?e.T.values[0].no:void 0;break;case"scalar":t[n]=i?L(e.T,e.L):void 0;break;case"message":t[n]=void 0}}function Y(e,t){if(null===e||"object"!=typeof e)return!1;if(!Object.getOwnPropertyNames(y.prototype).every((t=>t in e&&"function"==typeof e[t])))return!1;const n=e.getType();return null!==n&&"function"==typeof n&&"typeName"in n&&"string"==typeof n.typeName&&(void 0===t||n.typeName==t.typeName)}function X(e,t){return Y(t)||!e.fieldWrapper?t:e.fieldWrapper.wrapField(t)}M.DOUBLE,M.FLOAT,M.INT64,M.UINT64,M.INT32,M.UINT32,M.BOOL,M.STRING,M.BYTES;const Z={ignoreUnknownFields:!1},$={emitDefaultValues:!1,enumAsInteger:!1,useProtoFieldName:!1,prettySpaces:0};function ee(e){return e?Object.assign(Object.assign({},Z),e):Z}function te(e){return e?Object.assign(Object.assign({},$),e):$}const ne=Symbol(),ie=Symbol();function se(e){if(null===e)return"null";switch(typeof e){case"object":return Array.isArray(e)?"array":"object";case"string":return e.length>100?"string":'"'.concat(e.split('"').join('\\"'),'"');default:return String(e)}}function oe(e,t,n,i,s){let r=n.localName;if(n.repeated){if(o("map"!=n.kind),null===t)return;if(!Array.isArray(t))throw new Error("cannot decode field ".concat(s.typeName,".").concat(n.name," from JSON: ").concat(se(t)));const a=e[r];for(const e of t){if(null===e)throw new Error("cannot decode field ".concat(s.typeName,".").concat(n.name," from JSON: ").concat(se(e)));switch(n.kind){case"message":a.push(n.T.fromJson(e,i));break;case"enum":const t=ce(n.T,e,i.ignoreUnknownFields,!0);t!==ie&&a.push(t);break;case"scalar":try{a.push(ae(n.T,e,n.L,!0))}catch(t){let i="cannot decode field ".concat(s.typeName,".").concat(n.name," from JSON: ").concat(se(e));throw t instanceof Error&&t.message.length>0&&(i+=": ".concat(t.message)),new Error(i)}}}}else if("map"==n.kind){if(null===t)return;if("object"!=typeof t||Array.isArray(t))throw new Error("cannot decode field ".concat(s.typeName,".").concat(n.name," from JSON: ").concat(se(t)));const o=e[r];for(const[e,r]of Object.entries(t)){if(null===r)throw new Error("cannot decode field ".concat(s.typeName,".").concat(n.name," from JSON: map value null"));let a;try{a=re(n.K,e)}catch(e){let i="cannot decode map key for field ".concat(s.typeName,".").concat(n.name," from JSON: ").concat(se(t));throw e instanceof Error&&e.message.length>0&&(i+=": ".concat(e.message)),new Error(i)}switch(n.V.kind){case"message":o[a]=n.V.T.fromJson(r,i);break;case"enum":const e=ce(n.V.T,r,i.ignoreUnknownFields,!0);e!==ie&&(o[a]=e);break;case"scalar":try{o[a]=ae(n.V.T,r,N.BIGINT,!0)}catch(e){let i="cannot decode map value for field ".concat(s.typeName,".").concat(n.name," from JSON: ").concat(se(t));throw e instanceof Error&&e.message.length>0&&(i+=": ".concat(e.message)),new Error(i)}}}}else switch(n.oneof&&(e=e[n.oneof.localName]={case:r},r="value"),n.kind){case"message":const o=n.T;if(null===t&&"google.protobuf.Value"!=o.typeName)return;let a=e[r];Y(a)?a.fromJson(t,i):(e[r]=a=o.fromJson(t,i),o.fieldWrapper&&!n.oneof&&(e[r]=o.fieldWrapper.unwrapField(a)));break;case"enum":const c=ce(n.T,t,i.ignoreUnknownFields,!1);switch(c){case ne:Q(n,e);break;case ie:break;default:e[r]=c}break;case"scalar":try{const i=ae(n.T,t,n.L,!1);if(i===ne)Q(n,e);else e[r]=i}catch(e){let i="cannot decode field ".concat(s.typeName,".").concat(n.name," from JSON: ").concat(se(t));throw e instanceof Error&&e.message.length>0&&(i+=": ".concat(e.message)),new Error(i)}}}function re(e,t){if(e===M.BOOL)switch(t){case"true":t=!0;break;case"false":t=!1}return ae(e,t,N.BIGINT,!0).toString()}function ae(e,t,n,i){if(null===t)return i?L(e,n):ne;switch(e){case M.DOUBLE:case M.FLOAT:if("NaN"===t)return Number.NaN;if("Infinity"===t)return Number.POSITIVE_INFINITY;if("-Infinity"===t)return Number.NEGATIVE_INFINITY;if(""===t)break;if("string"==typeof t&&t.trim().length!==t.length)break;if("string"!=typeof t&&"number"!=typeof t)break;const i=Number(t);if(Number.isNaN(i))break;if(!Number.isFinite(i))break;return e==M.FLOAT&&p(i),i;case M.INT32:case M.FIXED32:case M.SFIXED32:case M.SINT32:case M.UINT32:let s;if("number"==typeof t?s=t:"string"==typeof t&&t.length>0&&t.trim().length===t.length&&(s=Number(t)),void 0===s)break;return e==M.UINT32||e==M.FIXED32?h(s):u(s),s;case M.INT64:case M.SFIXED64:case M.SINT64:if("number"!=typeof t&&"string"!=typeof t)break;const o=x.parse(t);return n?o.toString():o;case M.FIXED64:case M.UINT64:if("number"!=typeof t&&"string"!=typeof t)break;const r=x.uParse(t);return n?r.toString():r;case M.BOOL:if("boolean"!=typeof t)break;return t;case M.STRING:if("string"!=typeof t)break;try{encodeURIComponent(t)}catch(e){throw new Error("invalid UTF8")}return t;case M.BYTES:if(""===t)return new Uint8Array(0);if("string"!=typeof t)break;return K.dec(t)}throw new Error}function ce(e,t,n,i){if(null===t)return"google.protobuf.NullValue"==e.typeName?0:i?e.values[0].no:ne;switch(typeof t){case"number":if(Number.isInteger(t))return t;break;case"string":const i=e.findName(t);if(void 0!==i)return i.no;if(n)return ie}throw new Error("cannot decode enum ".concat(e.typeName," from JSON: ").concat(se(t)))}function de(e){return!(!e.repeated&&"map"!=e.kind)||!e.oneof&&("message"!=e.kind&&(!e.opt&&!e.req))}function le(e,t,n){if("map"==e.kind){o("object"==typeof t&&null!=t);const i={},s=Object.entries(t);switch(e.V.kind){case"scalar":for(const[t,n]of s)i[t.toString()]=he(e.V.T,n);break;case"message":for(const[e,t]of s)i[e.toString()]=t.toJson(n);break;case"enum":const t=e.V.T;for(const[e,o]of s)i[e.toString()]=ue(t,o,n.enumAsInteger)}return n.emitDefaultValues||s.length>0?i:void 0}if(e.repeated){o(Array.isArray(t));const i=[];switch(e.kind){case"scalar":for(let n=0;n<t.length;n++)i.push(he(e.T,t[n]));break;case"enum":for(let s=0;s<t.length;s++)i.push(ue(e.T,t[s],n.enumAsInteger));break;case"message":for(let e=0;e<t.length;e++)i.push(t[e].toJson(n))}return n.emitDefaultValues||i.length>0?i:void 0}switch(e.kind){case"scalar":return he(e.T,t);case"enum":return ue(e.T,t,n.enumAsInteger);case"message":return X(e.T,t).toJson(n)}}function ue(e,t,n){var i;if(o("number"==typeof t),"google.protobuf.NullValue"==e.typeName)return null;if(n)return t;const s=e.findNumber(t);return null!==(i=null==s?void 0:s.name)&&void 0!==i?i:t}function he(e,t){switch(e){case M.INT32:case M.SFIXED32:case M.SINT32:case M.FIXED32:case M.UINT32:return o("number"==typeof t),t;case M.FLOAT:case M.DOUBLE:return o("number"==typeof t),Number.isNaN(t)?"NaN":t===Number.POSITIVE_INFINITY?"Infinity":t===Number.NEGATIVE_INFINITY?"-Infinity":t;case M.STRING:return o("string"==typeof t),t;case M.BOOL:return o("boolean"==typeof t),t;case M.UINT64:case M.FIXED64:case M.INT64:case M.SFIXED64:case M.SINT64:return o("bigint"==typeof t||"string"==typeof t||"number"==typeof t),t.toString();case M.BYTES:return o(t instanceof Uint8Array),K.enc(t)}}const pe=Symbol("@bufbuild/protobuf/unknown-fields"),me={readUnknownFields:!0,readerFactory:e=>new F(e)},ge={writeUnknownFields:!0,writerFactory:()=>new j};function ve(e){return e?Object.assign(Object.assign({},me),e):me}function fe(e){return e?Object.assign(Object.assign({},ge),e):ge}function ke(e,t,n,i,s){let{repeated:o,localName:r}=n;switch(n.oneof&&((e=e[n.oneof.localName]).case!=r&&delete e.value,e.case=r,r="value"),n.kind){case"scalar":case"enum":const a="enum"==n.kind?M.INT32:n.T;let c=Te;if("scalar"==n.kind&&n.L>0&&(c=ye),o){let n=e[r];if(i==_.LengthDelimited&&a!=M.STRING&&a!=M.BYTES){let e=t.uint32()+t.pos;for(;t.pos<e;)n.push(c(t,a))}else n.push(c(t,a))}else e[r]=c(t,a);break;case"message":const d=n.T;o?e[r].push(be(t,new d,s,n)):Y(e[r])?be(t,e[r],s,n):(e[r]=be(t,new d,s,n),!d.fieldWrapper||n.oneof||n.repeated||(e[r]=d.fieldWrapper.unwrapField(e[r])));break;case"map":let[l,u]=function(e,t,n){const i=t.uint32(),s=t.pos+i;let o,r;for(;t.pos<s;){const[i]=t.tag();switch(i){case 1:o=Te(t,e.K);break;case 2:switch(e.V.kind){case"scalar":r=Te(t,e.V.T);break;case"enum":r=t.int32();break;case"message":r=be(t,new e.V.T,n,void 0)}}}void 0===o&&(o=L(e.K,N.BIGINT));"string"!=typeof o&&"number"!=typeof o&&(o=o.toString());if(void 0===r)switch(e.V.kind){case"scalar":r=L(e.V.T,N.BIGINT);break;case"enum":r=e.V.T.values[0].no;break;case"message":r=new e.V.T}return[o,r]}(n,t,s);e[r][l]=u}}function be(e,t,n,i){const s=t.getType().runtime.bin,o=null==i?void 0:i.delimited;return s.readMessage(t,e,o?i.no:e.uint32(),n,o),t}function ye(e,t){const n=Te(e,t);return"bigint"==typeof n?n.toString():n}function Te(e,t){switch(t){case M.STRING:return e.string();case M.BOOL:return e.bool();case M.DOUBLE:return e.double();case M.FLOAT:return e.float();case M.INT32:return e.int32();case M.INT64:return e.int64();case M.UINT64:return e.uint64();case M.FIXED64:return e.fixed64();case M.BYTES:return e.bytes();case M.FIXED32:return e.fixed32();case M.SFIXED32:return e.sfixed32();case M.SFIXED64:return e.sfixed64();case M.SINT64:return e.sint64();case M.UINT32:return e.uint32();case M.SINT32:return e.sint32()}}function Ce(e,t,n,i){o(void 0!==t);const s=e.repeated;switch(e.kind){case"scalar":case"enum":let r="enum"==e.kind?M.INT32:e.T;if(s)if(o(Array.isArray(t)),e.packed)!function(e,t,n,i){if(!i.length)return;e.tag(n,_.LengthDelimited).fork();let[,s]=Pe(t);for(let t=0;t<i.length;t++)e[s](i[t]);e.join()}(n,r,e.no,t);else for(const i of t)we(n,r,e.no,i);else we(n,r,e.no,t);break;case"message":if(s){o(Array.isArray(t));for(const s of t)Ee(n,i,e,s)}else Ee(n,i,e,t);break;case"map":o("object"==typeof t&&null!=t);for(const[s,o]of Object.entries(t))Se(n,i,e,s,o)}}function Se(e,t,n,i,s){e.tag(n.no,_.LengthDelimited),e.fork();let r=i;switch(n.K){case M.INT32:case M.FIXED32:case M.UINT32:case M.SFIXED32:case M.SINT32:r=Number.parseInt(i);break;case M.BOOL:o("true"==i||"false"==i),r="true"==i}switch(we(e,n.K,1,r),n.V.kind){case"scalar":we(e,n.V.T,2,s);break;case"enum":we(e,M.INT32,2,s);break;case"message":o(void 0!==s),e.tag(2,_.LengthDelimited).bytes(s.toBinary(t))}e.join()}function Ee(e,t,n,i){const s=X(n.T,i);n.delimited?e.tag(n.no,_.StartGroup).raw(s.toBinary(t)).tag(n.no,_.EndGroup):e.tag(n.no,_.LengthDelimited).bytes(s.toBinary(t))}function we(e,t,n,i){o(void 0!==i);let[s,r]=Pe(t);e.tag(n,s)[r](i)}function Pe(e){let t=_.Varint;switch(e){case M.BYTES:case M.STRING:t=_.LengthDelimited;break;case M.DOUBLE:case M.FIXED64:case M.SFIXED64:t=_.Bit64;break;case M.FIXED32:case M.SFIXED32:case M.FLOAT:t=_.Bit32}return[t,M[e].toLowerCase()]}function Re(e){if(void 0===e)return e;if(Y(e))return e.clone();if(e instanceof Uint8Array){const t=new Uint8Array(e.byteLength);return t.set(e),t}return e}function Ie(e){return e instanceof Uint8Array?e:new Uint8Array(e)}class Oe{constructor(e,t){this._fields=e,this._normalizer=t}findJsonName(e){if(!this.jsonNames){const e={};for(const t of this.list())e[t.jsonName]=e[t.name]=t;this.jsonNames=e}return this.jsonNames[e]}find(e){if(!this.numbers){const e={};for(const t of this.list())e[t.no]=t;this.numbers=e}return this.numbers[e]}list(){return this.all||(this.all=this._normalizer(this._fields)),this.all}byNumber(){return this.numbersAsc||(this.numbersAsc=this.list().concat().sort(((e,t)=>e.no-t.no))),this.numbersAsc}byMember(){if(!this.members){this.members=[];const e=this.members;let t;for(const n of this.list())n.oneof?n.oneof!==t&&(t=n.oneof,e.push(t)):e.push(n)}return this.members}}function De(e,t){const n=Me(e);return t?n:Ue(Le(n))}const xe=Me;function Me(e){let t=!1;const n=[];for(let i=0;i<e.length;i++){let s=e.charAt(i);switch(s){case"_":t=!0;break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":n.push(s),t=!1;break;default:t&&(t=!1,s=s.toUpperCase()),n.push(s)}}return n.join("")}const Ne=new Set(["constructor","toString","toJSON","valueOf"]),_e=new Set(["getType","clone","equals","fromBinary","fromJson","fromJsonString","toBinary","toJson","toJsonString","toObject"]),Ae=e=>"".concat(e,"$"),Le=e=>_e.has(e)?Ae(e):e,Ue=e=>Ne.has(e)?Ae(e):e;class je{constructor(e){this.kind="oneof",this.repeated=!1,this.packed=!1,this.opt=!1,this.req=!1,this.default=void 0,this.fields=[],this.name=e,this.localName=De(e,!1)}addField(e){o(e.oneof===this,"field ".concat(e.name," not one of ").concat(this.name)),this.fields.push(e)}findField(e){if(!this._lookup){this._lookup=Object.create(null);for(let e=0;e<this.fields.length;e++)this._lookup[this.fields[e].localName]=this.fields[e]}return this._lookup[e]}}const Fe=(Be=e=>new Oe(e,(e=>function(e,t){var n,i,s,o,r,a;const c=[];let d;for(const t of"function"==typeof e?e():e){const e=t;if(e.localName=De(t.name,void 0!==t.oneof),e.jsonName=null!==(n=t.jsonName)&&void 0!==n?n:xe(t.name),e.repeated=null!==(i=t.repeated)&&void 0!==i&&i,"scalar"==t.kind&&(e.L=null!==(s=t.L)&&void 0!==s?s:N.BIGINT),e.delimited=null!==(o=t.delimited)&&void 0!==o&&o,e.req=null!==(r=t.req)&&void 0!==r&&r,e.opt=null!==(a=t.opt)&&void 0!==a&&a,void 0===t.packed&&(e.packed="enum"==t.kind||"scalar"==t.kind&&t.T!=M.BYTES&&t.T!=M.STRING),void 0!==t.oneof){const n="string"==typeof t.oneof?t.oneof:t.oneof.name;d&&d.name==n||(d=new je(n)),e.oneof=d,d.addField(e)}c.push(e)}return c}(e))),Ve=e=>{for(const t of e.getType().fields.byMember()){if(t.opt)continue;const n=t.localName,i=e;if(t.repeated)i[n]=[];else switch(t.kind){case"oneof":i[n]={case:void 0};break;case"enum":i[n]=0;break;case"map":i[n]={};break;case"scalar":i[n]=L(t.T,t.L)}}},{syntax:"proto3",json:{makeReadOptions:ee,makeWriteOptions:te,readMessage(e,t,n,i){if(null==t||Array.isArray(t)||"object"!=typeof t)throw new Error("cannot decode message ".concat(e.typeName," from JSON: ").concat(se(t)));i=null!=i?i:new e;const s=new Map,o=n.typeRegistry;for(const[r,a]of Object.entries(t)){const t=e.fields.findJsonName(r);if(t){if(t.oneof){if(null===a&&"scalar"==t.kind)continue;const n=s.get(t.oneof);if(void 0!==n)throw new Error("cannot decode message ".concat(e.typeName,' from JSON: multiple keys for oneof "').concat(t.oneof.name,'" present: "').concat(n,'", "').concat(r,'"'));s.set(t.oneof,r)}oe(i,a,t,n,e)}else{let t=!1;if((null==o?void 0:o.findExtension)&&r.startsWith("[")&&r.endsWith("]")){const s=o.findExtension(r.substring(1,r.length-1));if(s&&s.extendee.typeName==e.typeName){t=!0;const[e,o]=B(s);oe(e,a,s.field,n,s),H(i,s,o(),n)}}if(!t&&!n.ignoreUnknownFields)throw new Error("cannot decode message ".concat(e.typeName,' from JSON: key "').concat(r,'" is unknown'))}}return i},writeMessage(e,t){const n=e.getType(),i={};let s;try{for(s of n.fields.byNumber()){if(!J(s,e)){if(s.req)throw"required field not set";if(!t.emitDefaultValues)continue;if(!de(s))continue}const n=le(s,s.oneof?e[s.oneof.localName].value:e[s.localName],t);void 0!==n&&(i[t.useProtoFieldName?s.name:s.jsonName]=n)}const o=t.typeRegistry;if(null==o?void 0:o.findExtensionFor)for(const s of n.runtime.bin.listUnknownFields(e)){const r=o.findExtensionFor(n.typeName,s.no);if(r&&G(e,r)){const n=W(e,r,t),s=le(r.field,n,t);void 0!==s&&(i[r.field.jsonName]=s)}}}catch(e){const t=s?"cannot encode field ".concat(n.typeName,".").concat(s.name," to JSON"):"cannot encode message ".concat(n.typeName," to JSON"),i=e instanceof Error?e.message:String(e);throw new Error(t+(i.length>0?": ".concat(i):""))}return i},readScalar:(e,t,n)=>ae(e,t,null!=n?n:N.BIGINT,!0),writeScalar(e,t,n){if(void 0!==t)return n||U(e,t)?he(e,t):void 0},debug:se},bin:{makeReadOptions:ve,makeWriteOptions:fe,listUnknownFields(e){var t;return null!==(t=e[pe])&&void 0!==t?t:[]},discardUnknownFields(e){delete e[pe]},writeUnknownFields(e,t){const n=e[pe];if(n)for(const e of n)t.tag(e.no,e.wireType).raw(e.data)},onUnknownField(e,t,n,i){const s=e;Array.isArray(s[pe])||(s[pe]=[]),s[pe].push({no:t,wireType:n,data:i})},readMessage(e,t,n,i,s){const o=e.getType(),r=s?t.len:t.pos+n;let a,c;for(;t.pos<r&&([a,c]=t.tag(),!0!==s||c!=_.EndGroup);){const n=o.fields.find(a);if(n)ke(e,t,n,c,i);else{const n=t.skip(c,a);i.readUnknownFields&&this.onUnknownField(e,a,c,n)}}if(s&&(c!=_.EndGroup||a!==n))throw new Error("invalid end group tag")},readField:ke,writeMessage(e,t,n){const i=e.getType();for(const s of i.fields.byNumber())if(J(s,e))Ce(s,s.oneof?e[s.oneof.localName].value:e[s.localName],t,n);else if(s.req)throw new Error("cannot encode field ".concat(i.typeName,".").concat(s.name," to binary: required field not set"));return n.writeUnknownFields&&this.writeUnknownFields(e,t),t},writeField(e,t,n,i){void 0!==t&&Ce(e,t,n,i)}},util:Object.assign(Object.assign({},{setEnumType:v,initPartial(e,t){if(void 0===e)return;const n=t.getType();for(const i of n.fields.byMember()){const n=i.localName,s=t,o=e;if(null!=o[n])switch(i.kind){case"oneof":const e=o[n].case;if(void 0===e)continue;const t=i.findField(e);let r=o[n].value;t&&"message"==t.kind&&!Y(r,t.T)?r=new t.T(r):t&&"scalar"===t.kind&&t.T===M.BYTES&&(r=Ie(r)),s[n]={case:e,value:r};break;case"scalar":case"enum":let a=o[n];i.T===M.BYTES&&(a=i.repeated?a.map(Ie):Ie(a)),s[n]=a;break;case"map":switch(i.V.kind){case"scalar":case"enum":if(i.V.T===M.BYTES)for(const[e,t]of Object.entries(o[n]))s[n][e]=Ie(t);else Object.assign(s[n],o[n]);break;case"message":const e=i.V.T;for(const t of Object.keys(o[n])){let i=o[n][t];e.fieldWrapper||(i=new e(i)),s[n][t]=i}}break;case"message":const c=i.T;if(i.repeated)s[n]=o[n].map((e=>Y(e,c)?e:new c(e)));else{const e=o[n];c.fieldWrapper?"google.protobuf.BytesValue"===c.typeName?s[n]=Ie(e):s[n]=e:s[n]=Y(e,c)?e:new c(e)}}}},equals:(e,t,n)=>t===n||!(!t||!n)&&e.fields.byMember().every((e=>{const i=t[e.localName],s=n[e.localName];if(e.repeated){if(i.length!==s.length)return!1;switch(e.kind){case"message":return i.every(((t,n)=>e.T.equals(t,s[n])));case"scalar":return i.every(((t,n)=>A(e.T,t,s[n])));case"enum":return i.every(((e,t)=>A(M.INT32,e,s[t])))}throw new Error("repeated cannot contain ".concat(e.kind))}switch(e.kind){case"message":return e.T.equals(i,s);case"enum":return A(M.INT32,i,s);case"scalar":return A(e.T,i,s);case"oneof":if(i.case!==s.case)return!1;const t=e.findField(i.case);if(void 0===t)return!0;switch(t.kind){case"message":return t.T.equals(i.value,s.value);case"enum":return A(M.INT32,i.value,s.value);case"scalar":return A(t.T,i.value,s.value)}throw new Error("oneof cannot contain ".concat(t.kind));case"map":const n=Object.keys(i).concat(Object.keys(s));switch(e.V.kind){case"message":const t=e.V.T;return n.every((e=>t.equals(i[e],s[e])));case"enum":return n.every((e=>A(M.INT32,i[e],s[e])));case"scalar":const o=e.V.T;return n.every((e=>A(o,i[e],s[e])))}}})),clone(e){const t=e.getType(),n=new t,i=n;for(const n of t.fields.byMember()){const t=e[n.localName];let s;if(n.repeated)s=t.map(Re);else if("map"==n.kind){s=i[n.localName];for(const[e,n]of Object.entries(t))s[e]=Re(n)}else s="oneof"==n.kind?n.findField(t.case)?{case:t.case,value:Re(t.value)}:{case:void 0}:Re(t);i[n.localName]=s}for(const n of t.runtime.bin.listUnknownFields(e))t.runtime.bin.onUnknownField(i,n.no,n.wireType,n.data);return n}}),{newFieldList:Be,initFields:Ve}),makeMessageType(e,t,n){return function(e,t,n,i){var s;const o=null!==(s=null==i?void 0:i.localName)&&void 0!==s?s:t.substring(t.lastIndexOf(".")+1),r={[o]:function(t){e.util.initFields(this),e.util.initPartial(t,this)}}[o];return Object.setPrototypeOf(r.prototype,new y),Object.assign(r,{runtime:e,typeName:t,fields:e.util.newFieldList(n),fromBinary:(e,t)=>(new r).fromBinary(e,t),fromJson:(e,t)=>(new r).fromJson(e,t),fromJsonString:(e,t)=>(new r).fromJsonString(e,t),equals:(t,n)=>e.util.equals(r,t,n)}),r}(this,e,t,n)},makeEnum:k,makeEnumType:f,getEnumType:g,makeExtension(e,t,n){return function(e,t,n,i){let s;return{typeName:t,extendee:n,get field(){if(!s){const n="function"==typeof i?i():i;n.name=t.split(".").pop(),n.jsonName="[".concat(t,"]"),s=e.util.newFieldList([n]).list()[0]}return s},runtime:e}}(this,e,t,n)}});var Be,Ve;class qe extends y{constructor(e){super(),this.seconds=x.zero,this.nanos=0,Fe.util.initPartial(e,this)}fromJson(e,t){if("string"!=typeof e)throw new Error("cannot decode google.protobuf.Timestamp from JSON: ".concat(Fe.json.debug(e)));const n=e.match(/^([0-9]{4})-([0-9]{2})-([0-9]{2})T([0-9]{2}):([0-9]{2}):([0-9]{2})(?:Z|\.([0-9]{3,9})Z|([+-][0-9][0-9]:[0-9][0-9]))$/);if(!n)throw new Error("cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string");const i=Date.parse(n[1]+"-"+n[2]+"-"+n[3]+"T"+n[4]+":"+n[5]+":"+n[6]+(n[8]?n[8]:"Z"));if(Number.isNaN(i))throw new Error("cannot decode google.protobuf.Timestamp from JSON: invalid RFC 3339 string");if(i<Date.parse("0001-01-01T00:00:00Z")||i>Date.parse("9999-12-31T23:59:59Z"))throw new Error("cannot decode message google.protobuf.Timestamp from JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive");return this.seconds=x.parse(i/1e3),this.nanos=0,n[7]&&(this.nanos=parseInt("1"+n[7]+"0".repeat(9-n[7].length))-1e9),this}toJson(e){const t=1e3*Number(this.seconds);if(t<Date.parse("0001-01-01T00:00:00Z")||t>Date.parse("9999-12-31T23:59:59Z"))throw new Error("cannot encode google.protobuf.Timestamp to JSON: must be from 0001-01-01T00:00:00Z to 9999-12-31T23:59:59Z inclusive");if(this.nanos<0)throw new Error("cannot encode google.protobuf.Timestamp to JSON: nanos must not be negative");let n="Z";if(this.nanos>0){const e=(this.nanos+1e9).toString().substring(1);n="000000"===e.substring(3)?"."+e.substring(0,3)+"Z":"000"===e.substring(6)?"."+e.substring(0,6)+"Z":"."+e+"Z"}return new Date(t).toISOString().replace(".000Z",n)}toDate(){return new Date(1e3*Number(this.seconds)+Math.ceil(this.nanos/1e6))}static now(){return qe.fromDate(new Date)}static fromDate(e){const t=e.getTime();return new qe({seconds:x.parse(Math.floor(t/1e3)),nanos:t%1e3*1e6})}static fromBinary(e,t){return(new qe).fromBinary(e,t)}static fromJson(e,t){return(new qe).fromJson(e,t)}static fromJsonString(e,t){return(new qe).fromJsonString(e,t)}static equals(e,t){return Fe.util.equals(qe,e,t)}}qe.runtime=Fe,qe.typeName="google.protobuf.Timestamp",qe.fields=Fe.util.newFieldList((()=>[{no:1,name:"seconds",kind:"scalar",T:3},{no:2,name:"nanos",kind:"scalar",T:5}]));const Ke=Fe.makeMessageType("livekit.MetricsBatch",(()=>[{no:1,name:"timestamp_ms",kind:"scalar",T:3},{no:2,name:"normalized_timestamp",kind:"message",T:qe},{no:3,name:"str_data",kind:"scalar",T:9,repeated:!0},{no:4,name:"time_series",kind:"message",T:We,repeated:!0},{no:5,name:"events",kind:"message",T:Ge,repeated:!0}])),We=Fe.makeMessageType("livekit.TimeSeriesMetric",(()=>[{no:1,name:"label",kind:"scalar",T:13},{no:2,name:"participant_identity",kind:"scalar",T:13},{no:3,name:"track_sid",kind:"scalar",T:13},{no:4,name:"samples",kind:"message",T:He,repeated:!0},{no:5,name:"rid",kind:"scalar",T:13}])),He=Fe.makeMessageType("livekit.MetricSample",(()=>[{no:1,name:"timestamp_ms",kind:"scalar",T:3},{no:2,name:"normalized_timestamp",kind:"message",T:qe},{no:3,name:"value",kind:"scalar",T:2}])),Ge=Fe.makeMessageType("livekit.EventMetric",(()=>[{no:1,name:"label",kind:"scalar",T:13},{no:2,name:"participant_identity",kind:"scalar",T:13},{no:3,name:"track_sid",kind:"scalar",T:13},{no:4,name:"start_timestamp_ms",kind:"scalar",T:3},{no:5,name:"end_timestamp_ms",kind:"scalar",T:3,opt:!0},{no:6,name:"normalized_start_timestamp",kind:"message",T:qe},{no:7,name:"normalized_end_timestamp",kind:"message",T:qe,opt:!0},{no:8,name:"metadata",kind:"scalar",T:9},{no:9,name:"rid",kind:"scalar",T:13}])),ze=Fe.makeEnum("livekit.BackupCodecPolicy",[{no:0,name:"REGRESSION"},{no:1,name:"SIMULCAST"}]),Je=Fe.makeEnum("livekit.TrackType",[{no:0,name:"AUDIO"},{no:1,name:"VIDEO"},{no:2,name:"DATA"}]),Qe=Fe.makeEnum("livekit.TrackSource",[{no:0,name:"UNKNOWN"},{no:1,name:"CAMERA"},{no:2,name:"MICROPHONE"},{no:3,name:"SCREEN_SHARE"},{no:4,name:"SCREEN_SHARE_AUDIO"}]),Ye=Fe.makeEnum("livekit.VideoQuality",[{no:0,name:"LOW"},{no:1,name:"MEDIUM"},{no:2,name:"HIGH"},{no:3,name:"OFF"}]),Xe=Fe.makeEnum("livekit.ConnectionQuality",[{no:0,name:"POOR"},{no:1,name:"GOOD"},{no:2,name:"EXCELLENT"},{no:3,name:"LOST"}]),Ze=Fe.makeEnum("livekit.ClientConfigSetting",[{no:0,name:"UNSET"},{no:1,name:"DISABLED"},{no:2,name:"ENABLED"}]),$e=Fe.makeEnum("livekit.DisconnectReason",[{no:0,name:"UNKNOWN_REASON"},{no:1,name:"CLIENT_INITIATED"},{no:2,name:"DUPLICATE_IDENTITY"},{no:3,name:"SERVER_SHUTDOWN"},{no:4,name:"PARTICIPANT_REMOVED"},{no:5,name:"ROOM_DELETED"},{no:6,name:"STATE_MISMATCH"},{no:7,name:"JOIN_FAILURE"},{no:8,name:"MIGRATION"},{no:9,name:"SIGNAL_CLOSE"},{no:10,name:"ROOM_CLOSED"},{no:11,name:"USER_UNAVAILABLE"},{no:12,name:"USER_REJECTED"},{no:13,name:"SIP_TRUNK_FAILURE"}]),et=Fe.makeEnum("livekit.ReconnectReason",[{no:0,name:"RR_UNKNOWN"},{no:1,name:"RR_SIGNAL_DISCONNECTED"},{no:2,name:"RR_PUBLISHER_FAILED"},{no:3,name:"RR_SUBSCRIBER_FAILED"},{no:4,name:"RR_SWITCH_CANDIDATE"}]),tt=Fe.makeEnum("livekit.SubscriptionError",[{no:0,name:"SE_UNKNOWN"},{no:1,name:"SE_CODEC_UNSUPPORTED"},{no:2,name:"SE_TRACK_NOTFOUND"}]),nt=Fe.makeEnum("livekit.AudioTrackFeature",[{no:0,name:"TF_STEREO"},{no:1,name:"TF_NO_DTX"},{no:2,name:"TF_AUTO_GAIN_CONTROL"},{no:3,name:"TF_ECHO_CANCELLATION"},{no:4,name:"TF_NOISE_SUPPRESSION"},{no:5,name:"TF_ENHANCED_NOISE_CANCELLATION"}]),it=Fe.makeMessageType("livekit.Room",(()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"empty_timeout",kind:"scalar",T:13},{no:14,name:"departure_timeout",kind:"scalar",T:13},{no:4,name:"max_participants",kind:"scalar",T:13},{no:5,name:"creation_time",kind:"scalar",T:3},{no:15,name:"creation_time_ms",kind:"scalar",T:3},{no:6,name:"turn_password",kind:"scalar",T:9},{no:7,name:"enabled_codecs",kind:"message",T:st,repeated:!0},{no:8,name:"metadata",kind:"scalar",T:9},{no:9,name:"num_participants",kind:"scalar",T:13},{no:11,name:"num_publishers",kind:"scalar",T:13},{no:10,name:"active_recording",kind:"scalar",T:8},{no:13,name:"version",kind:"message",T:_t}])),st=Fe.makeMessageType("livekit.Codec",(()=>[{no:1,name:"mime",kind:"scalar",T:9},{no:2,name:"fmtp_line",kind:"scalar",T:9}])),ot=Fe.makeMessageType("livekit.ParticipantPermission",(()=>[{no:1,name:"can_subscribe",kind:"scalar",T:8},{no:2,name:"can_publish",kind:"scalar",T:8},{no:3,name:"can_publish_data",kind:"scalar",T:8},{no:9,name:"can_publish_sources",kind:"enum",T:Fe.getEnumType(Qe),repeated:!0},{no:7,name:"hidden",kind:"scalar",T:8},{no:8,name:"recorder",kind:"scalar",T:8},{no:10,name:"can_update_metadata",kind:"scalar",T:8},{no:11,name:"agent",kind:"scalar",T:8},{no:12,name:"can_subscribe_metrics",kind:"scalar",T:8}])),rt=Fe.makeMessageType("livekit.ParticipantInfo",(()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"identity",kind:"scalar",T:9},{no:3,name:"state",kind:"enum",T:Fe.getEnumType(at)},{no:4,name:"tracks",kind:"message",T:ut,repeated:!0},{no:5,name:"metadata",kind:"scalar",T:9},{no:6,name:"joined_at",kind:"scalar",T:3},{no:17,name:"joined_at_ms",kind:"scalar",T:3},{no:9,name:"name",kind:"scalar",T:9},{no:10,name:"version",kind:"scalar",T:13},{no:11,name:"permission",kind:"message",T:ot},{no:12,name:"region",kind:"scalar",T:9},{no:13,name:"is_publisher",kind:"scalar",T:8},{no:14,name:"kind",kind:"enum",T:Fe.getEnumType(ct)},{no:15,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}},{no:16,name:"disconnect_reason",kind:"enum",T:Fe.getEnumType($e)}])),at=Fe.makeEnum("livekit.ParticipantInfo.State",[{no:0,name:"JOINING"},{no:1,name:"JOINED"},{no:2,name:"ACTIVE"},{no:3,name:"DISCONNECTED"}]),ct=Fe.makeEnum("livekit.ParticipantInfo.Kind",[{no:0,name:"STANDARD"},{no:1,name:"INGRESS"},{no:2,name:"EGRESS"},{no:3,name:"SIP"},{no:4,name:"AGENT"}]),dt=Fe.makeEnum("livekit.Encryption.Type",[{no:0,name:"NONE"},{no:1,name:"GCM"},{no:2,name:"CUSTOM"}]),lt=Fe.makeMessageType("livekit.SimulcastCodecInfo",(()=>[{no:1,name:"mime_type",kind:"scalar",T:9},{no:2,name:"mid",kind:"scalar",T:9},{no:3,name:"cid",kind:"scalar",T:9},{no:4,name:"layers",kind:"message",T:ht,repeated:!0}])),ut=Fe.makeMessageType("livekit.TrackInfo",(()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"type",kind:"enum",T:Fe.getEnumType(Je)},{no:3,name:"name",kind:"scalar",T:9},{no:4,name:"muted",kind:"scalar",T:8},{no:5,name:"width",kind:"scalar",T:13},{no:6,name:"height",kind:"scalar",T:13},{no:7,name:"simulcast",kind:"scalar",T:8},{no:8,name:"disable_dtx",kind:"scalar",T:8},{no:9,name:"source",kind:"enum",T:Fe.getEnumType(Qe)},{no:10,name:"layers",kind:"message",T:ht,repeated:!0},{no:11,name:"mime_type",kind:"scalar",T:9},{no:12,name:"mid",kind:"scalar",T:9},{no:13,name:"codecs",kind:"message",T:lt,repeated:!0},{no:14,name:"stereo",kind:"scalar",T:8},{no:15,name:"disable_red",kind:"scalar",T:8},{no:16,name:"encryption",kind:"enum",T:Fe.getEnumType(dt)},{no:17,name:"stream",kind:"scalar",T:9},{no:18,name:"version",kind:"message",T:_t},{no:19,name:"audio_features",kind:"enum",T:Fe.getEnumType(nt),repeated:!0},{no:20,name:"backup_codec_policy",kind:"enum",T:Fe.getEnumType(ze)}])),ht=Fe.makeMessageType("livekit.VideoLayer",(()=>[{no:1,name:"quality",kind:"enum",T:Fe.getEnumType(Ye)},{no:2,name:"width",kind:"scalar",T:13},{no:3,name:"height",kind:"scalar",T:13},{no:4,name:"bitrate",kind:"scalar",T:13},{no:5,name:"ssrc",kind:"scalar",T:13}])),pt=Fe.makeMessageType("livekit.DataPacket",(()=>[{no:1,name:"kind",kind:"enum",T:Fe.getEnumType(mt)},{no:4,name:"participant_identity",kind:"scalar",T:9},{no:5,name:"destination_identities",kind:"scalar",T:9,repeated:!0},{no:2,name:"user",kind:"message",T:ft,oneof:"value"},{no:3,name:"speaker",kind:"message",T:gt,oneof:"value"},{no:6,name:"sip_dtmf",kind:"message",T:kt,oneof:"value"},{no:7,name:"transcription",kind:"message",T:bt,oneof:"value"},{no:8,name:"metrics",kind:"message",T:Ke,oneof:"value"},{no:9,name:"chat_message",kind:"message",T:Tt,oneof:"value"},{no:10,name:"rpc_request",kind:"message",T:Ct,oneof:"value"},{no:11,name:"rpc_ack",kind:"message",T:St,oneof:"value"},{no:12,name:"rpc_response",kind:"message",T:Et,oneof:"value"},{no:13,name:"stream_header",kind:"message",T:jt,oneof:"value"},{no:14,name:"stream_chunk",kind:"message",T:Ft,oneof:"value"},{no:15,name:"stream_trailer",kind:"message",T:Bt,oneof:"value"}])),mt=Fe.makeEnum("livekit.DataPacket.Kind",[{no:0,name:"RELIABLE"},{no:1,name:"LOSSY"}]),gt=Fe.makeMessageType("livekit.ActiveSpeakerUpdate",(()=>[{no:1,name:"speakers",kind:"message",T:vt,repeated:!0}])),vt=Fe.makeMessageType("livekit.SpeakerInfo",(()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"level",kind:"scalar",T:2},{no:3,name:"active",kind:"scalar",T:8}])),ft=Fe.makeMessageType("livekit.UserPacket",(()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:5,name:"participant_identity",kind:"scalar",T:9},{no:2,name:"payload",kind:"scalar",T:12},{no:3,name:"destination_sids",kind:"scalar",T:9,repeated:!0},{no:6,name:"destination_identities",kind:"scalar",T:9,repeated:!0},{no:4,name:"topic",kind:"scalar",T:9,opt:!0},{no:8,name:"id",kind:"scalar",T:9,opt:!0},{no:9,name:"start_time",kind:"scalar",T:4,opt:!0},{no:10,name:"end_time",kind:"scalar",T:4,opt:!0},{no:11,name:"nonce",kind:"scalar",T:12}])),kt=Fe.makeMessageType("livekit.SipDTMF",(()=>[{no:3,name:"code",kind:"scalar",T:13},{no:4,name:"digit",kind:"scalar",T:9}])),bt=Fe.makeMessageType("livekit.Transcription",(()=>[{no:2,name:"transcribed_participant_identity",kind:"scalar",T:9},{no:3,name:"track_id",kind:"scalar",T:9},{no:4,name:"segments",kind:"message",T:yt,repeated:!0}])),yt=Fe.makeMessageType("livekit.TranscriptionSegment",(()=>[{no:1,name:"id",kind:"scalar",T:9},{no:2,name:"text",kind:"scalar",T:9},{no:3,name:"start_time",kind:"scalar",T:4},{no:4,name:"end_time",kind:"scalar",T:4},{no:5,name:"final",kind:"scalar",T:8},{no:6,name:"language",kind:"scalar",T:9}])),Tt=Fe.makeMessageType("livekit.ChatMessage",(()=>[{no:1,name:"id",kind:"scalar",T:9},{no:2,name:"timestamp",kind:"scalar",T:3},{no:3,name:"edit_timestamp",kind:"scalar",T:3,opt:!0},{no:4,name:"message",kind:"scalar",T:9},{no:5,name:"deleted",kind:"scalar",T:8},{no:6,name:"generated",kind:"scalar",T:8}])),Ct=Fe.makeMessageType("livekit.RpcRequest",(()=>[{no:1,name:"id",kind:"scalar",T:9},{no:2,name:"method",kind:"scalar",T:9},{no:3,name:"payload",kind:"scalar",T:9},{no:4,name:"response_timeout_ms",kind:"scalar",T:13},{no:5,name:"version",kind:"scalar",T:13}])),St=Fe.makeMessageType("livekit.RpcAck",(()=>[{no:1,name:"request_id",kind:"scalar",T:9}])),Et=Fe.makeMessageType("livekit.RpcResponse",(()=>[{no:1,name:"request_id",kind:"scalar",T:9},{no:2,name:"payload",kind:"scalar",T:9,oneof:"value"},{no:3,name:"error",kind:"message",T:wt,oneof:"value"}])),wt=Fe.makeMessageType("livekit.RpcError",(()=>[{no:1,name:"code",kind:"scalar",T:13},{no:2,name:"message",kind:"scalar",T:9},{no:3,name:"data",kind:"scalar",T:9}])),Pt=Fe.makeMessageType("livekit.ParticipantTracks",(()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sids",kind:"scalar",T:9,repeated:!0}])),Rt=Fe.makeMessageType("livekit.ServerInfo",(()=>[{no:1,name:"edition",kind:"enum",T:Fe.getEnumType(It)},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"protocol",kind:"scalar",T:5},{no:4,name:"region",kind:"scalar",T:9},{no:5,name:"node_id",kind:"scalar",T:9},{no:6,name:"debug_info",kind:"scalar",T:9},{no:7,name:"agent_protocol",kind:"scalar",T:5}])),It=Fe.makeEnum("livekit.ServerInfo.Edition",[{no:0,name:"Standard"},{no:1,name:"Cloud"}]),Ot=Fe.makeMessageType("livekit.ClientInfo",(()=>[{no:1,name:"sdk",kind:"enum",T:Fe.getEnumType(Dt)},{no:2,name:"version",kind:"scalar",T:9},{no:3,name:"protocol",kind:"scalar",T:5},{no:4,name:"os",kind:"scalar",T:9},{no:5,name:"os_version",kind:"scalar",T:9},{no:6,name:"device_model",kind:"scalar",T:9},{no:7,name:"browser",kind:"scalar",T:9},{no:8,name:"browser_version",kind:"scalar",T:9},{no:9,name:"address",kind:"scalar",T:9},{no:10,name:"network",kind:"scalar",T:9},{no:11,name:"other_sdks",kind:"scalar",T:9}])),Dt=Fe.makeEnum("livekit.ClientInfo.SDK",[{no:0,name:"UNKNOWN"},{no:1,name:"JS"},{no:2,name:"SWIFT"},{no:3,name:"ANDROID"},{no:4,name:"FLUTTER"},{no:5,name:"GO"},{no:6,name:"UNITY"},{no:7,name:"REACT_NATIVE"},{no:8,name:"RUST"},{no:9,name:"PYTHON"},{no:10,name:"CPP"},{no:11,name:"UNITY_WEB"},{no:12,name:"NODE"}]),xt=Fe.makeMessageType("livekit.ClientConfiguration",(()=>[{no:1,name:"video",kind:"message",T:Mt},{no:2,name:"screen",kind:"message",T:Mt},{no:3,name:"resume_connection",kind:"enum",T:Fe.getEnumType(Ze)},{no:4,name:"disabled_codecs",kind:"message",T:Nt},{no:5,name:"force_relay",kind:"enum",T:Fe.getEnumType(Ze)}])),Mt=Fe.makeMessageType("livekit.VideoConfiguration",(()=>[{no:1,name:"hardware_encoder",kind:"enum",T:Fe.getEnumType(Ze)}])),Nt=Fe.makeMessageType("livekit.DisabledCodecs",(()=>[{no:1,name:"codecs",kind:"message",T:st,repeated:!0},{no:2,name:"publish",kind:"message",T:st,repeated:!0}])),_t=Fe.makeMessageType("livekit.TimedVersion",(()=>[{no:1,name:"unix_micro",kind:"scalar",T:3},{no:2,name:"ticks",kind:"scalar",T:5}])),At=Fe.makeEnum("livekit.DataStream.OperationType",[{no:0,name:"CREATE"},{no:1,name:"UPDATE"},{no:2,name:"DELETE"},{no:3,name:"REACTION"}]),Lt=Fe.makeMessageType("livekit.DataStream.TextHeader",(()=>[{no:1,name:"operation_type",kind:"enum",T:Fe.getEnumType(At)},{no:2,name:"version",kind:"scalar",T:5},{no:3,name:"reply_to_stream_id",kind:"scalar",T:9},{no:4,name:"attached_stream_ids",kind:"scalar",T:9,repeated:!0},{no:5,name:"generated",kind:"scalar",T:8}]),{localName:"DataStream_TextHeader"}),Ut=Fe.makeMessageType("livekit.DataStream.ByteHeader",(()=>[{no:1,name:"name",kind:"scalar",T:9}]),{localName:"DataStream_ByteHeader"}),jt=Fe.makeMessageType("livekit.DataStream.Header",(()=>[{no:1,name:"stream_id",kind:"scalar",T:9},{no:2,name:"timestamp",kind:"scalar",T:3},{no:3,name:"topic",kind:"scalar",T:9},{no:4,name:"mime_type",kind:"scalar",T:9},{no:5,name:"total_length",kind:"scalar",T:4,opt:!0},{no:7,name:"encryption_type",kind:"enum",T:Fe.getEnumType(dt)},{no:8,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}},{no:9,name:"text_header",kind:"message",T:Lt,oneof:"content_header"},{no:10,name:"byte_header",kind:"message",T:Ut,oneof:"content_header"}]),{localName:"DataStream_Header"}),Ft=Fe.makeMessageType("livekit.DataStream.Chunk",(()=>[{no:1,name:"stream_id",kind:"scalar",T:9},{no:2,name:"chunk_index",kind:"scalar",T:4},{no:3,name:"content",kind:"scalar",T:12},{no:4,name:"version",kind:"scalar",T:5},{no:5,name:"iv",kind:"scalar",T:12,opt:!0}]),{localName:"DataStream_Chunk"}),Bt=Fe.makeMessageType("livekit.DataStream.Trailer",(()=>[{no:1,name:"stream_id",kind:"scalar",T:9},{no:2,name:"reason",kind:"scalar",T:9},{no:3,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}}]),{localName:"DataStream_Trailer"}),Vt=Fe.makeEnum("livekit.SignalTarget",[{no:0,name:"PUBLISHER"},{no:1,name:"SUBSCRIBER"}]),qt=Fe.makeEnum("livekit.StreamState",[{no:0,name:"ACTIVE"},{no:1,name:"PAUSED"}]),Kt=Fe.makeEnum("livekit.CandidateProtocol",[{no:0,name:"UDP"},{no:1,name:"TCP"},{no:2,name:"TLS"}]),Wt=Fe.makeMessageType("livekit.SignalRequest",(()=>[{no:1,name:"offer",kind:"message",T:en,oneof:"message"},{no:2,name:"answer",kind:"message",T:en,oneof:"message"},{no:3,name:"trickle",kind:"message",T:Jt,oneof:"message"},{no:4,name:"add_track",kind:"message",T:zt,oneof:"message"},{no:5,name:"mute",kind:"message",T:Qt,oneof:"message"},{no:6,name:"subscription",kind:"message",T:nn,oneof:"message"},{no:7,name:"track_setting",kind:"message",T:sn,oneof:"message"},{no:8,name:"leave",kind:"message",T:an,oneof:"message"},{no:10,name:"update_layers",kind:"message",T:dn,oneof:"message"},{no:11,name:"subscription_permission",kind:"message",T:Cn,oneof:"message"},{no:12,name:"sync_state",kind:"message",T:En,oneof:"message"},{no:13,name:"simulate",kind:"message",T:Pn,oneof:"message"},{no:14,name:"ping",kind:"scalar",T:3,oneof:"message"},{no:15,name:"update_metadata",kind:"message",T:ln,oneof:"message"},{no:16,name:"ping_req",kind:"message",T:Rn,oneof:"message"},{no:17,name:"update_audio_track",kind:"message",T:on,oneof:"message"},{no:18,name:"update_video_track",kind:"message",T:rn,oneof:"message"}])),Ht=Fe.makeMessageType("livekit.SignalResponse",(()=>[{no:1,name:"join",kind:"message",T:Yt,oneof:"message"},{no:2,name:"answer",kind:"message",T:en,oneof:"message"},{no:3,name:"offer",kind:"message",T:en,oneof:"message"},{no:4,name:"trickle",kind:"message",T:Jt,oneof:"message"},{no:5,name:"update",kind:"message",T:tn,oneof:"message"},{no:6,name:"track_published",kind:"message",T:Zt,oneof:"message"},{no:8,name:"leave",kind:"message",T:an,oneof:"message"},{no:9,name:"mute",kind:"message",T:Qt,oneof:"message"},{no:10,name:"speakers_changed",kind:"message",T:hn,oneof:"message"},{no:11,name:"room_update",kind:"message",T:pn,oneof:"message"},{no:12,name:"connection_quality",kind:"message",T:gn,oneof:"message"},{no:13,name:"stream_state_update",kind:"message",T:fn,oneof:"message"},{no:14,name:"subscribed_quality_update",kind:"message",T:yn,oneof:"message"},{no:15,name:"subscription_permission_update",kind:"message",T:Sn,oneof:"message"},{no:16,name:"refresh_token",kind:"scalar",T:9,oneof:"message"},{no:17,name:"track_unpublished",kind:"message",T:$t,oneof:"message"},{no:18,name:"pong",kind:"scalar",T:3,oneof:"message"},{no:19,name:"reconnect",kind:"message",T:Xt,oneof:"message"},{no:20,name:"pong_resp",kind:"message",T:In,oneof:"message"},{no:21,name:"subscription_response",kind:"message",T:xn,oneof:"message"},{no:22,name:"request_response",kind:"message",T:Mn,oneof:"message"},{no:23,name:"track_subscribed",kind:"message",T:_n,oneof:"message"}])),Gt=Fe.makeMessageType("livekit.SimulcastCodec",(()=>[{no:1,name:"codec",kind:"scalar",T:9},{no:2,name:"cid",kind:"scalar",T:9}])),zt=Fe.makeMessageType("livekit.AddTrackRequest",(()=>[{no:1,name:"cid",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"type",kind:"enum",T:Fe.getEnumType(Je)},{no:4,name:"width",kind:"scalar",T:13},{no:5,name:"height",kind:"scalar",T:13},{no:6,name:"muted",kind:"scalar",T:8},{no:7,name:"disable_dtx",kind:"scalar",T:8},{no:8,name:"source",kind:"enum",T:Fe.getEnumType(Qe)},{no:9,name:"layers",kind:"message",T:ht,repeated:!0},{no:10,name:"simulcast_codecs",kind:"message",T:Gt,repeated:!0},{no:11,name:"sid",kind:"scalar",T:9},{no:12,name:"stereo",kind:"scalar",T:8},{no:13,name:"disable_red",kind:"scalar",T:8},{no:14,name:"encryption",kind:"enum",T:Fe.getEnumType(dt)},{no:15,name:"stream",kind:"scalar",T:9},{no:16,name:"backup_codec_policy",kind:"enum",T:Fe.getEnumType(ze)}])),Jt=Fe.makeMessageType("livekit.TrickleRequest",(()=>[{no:1,name:"candidateInit",kind:"scalar",T:9},{no:2,name:"target",kind:"enum",T:Fe.getEnumType(Vt)},{no:3,name:"final",kind:"scalar",T:8}])),Qt=Fe.makeMessageType("livekit.MuteTrackRequest",(()=>[{no:1,name:"sid",kind:"scalar",T:9},{no:2,name:"muted",kind:"scalar",T:8}])),Yt=Fe.makeMessageType("livekit.JoinResponse",(()=>[{no:1,name:"room",kind:"message",T:it},{no:2,name:"participant",kind:"message",T:rt},{no:3,name:"other_participants",kind:"message",T:rt,repeated:!0},{no:4,name:"server_version",kind:"scalar",T:9},{no:5,name:"ice_servers",kind:"message",T:un,repeated:!0},{no:6,name:"subscriber_primary",kind:"scalar",T:8},{no:7,name:"alternative_url",kind:"scalar",T:9},{no:8,name:"client_configuration",kind:"message",T:xt},{no:9,name:"server_region",kind:"scalar",T:9},{no:10,name:"ping_timeout",kind:"scalar",T:5},{no:11,name:"ping_interval",kind:"scalar",T:5},{no:12,name:"server_info",kind:"message",T:Rt},{no:13,name:"sif_trailer",kind:"scalar",T:12},{no:14,name:"enabled_publish_codecs",kind:"message",T:st,repeated:!0},{no:15,name:"fast_publish",kind:"scalar",T:8}])),Xt=Fe.makeMessageType("livekit.ReconnectResponse",(()=>[{no:1,name:"ice_servers",kind:"message",T:un,repeated:!0},{no:2,name:"client_configuration",kind:"message",T:xt}])),Zt=Fe.makeMessageType("livekit.TrackPublishedResponse",(()=>[{no:1,name:"cid",kind:"scalar",T:9},{no:2,name:"track",kind:"message",T:ut}])),$t=Fe.makeMessageType("livekit.TrackUnpublishedResponse",(()=>[{no:1,name:"track_sid",kind:"scalar",T:9}])),en=Fe.makeMessageType("livekit.SessionDescription",(()=>[{no:1,name:"type",kind:"scalar",T:9},{no:2,name:"sdp",kind:"scalar",T:9}])),tn=Fe.makeMessageType("livekit.ParticipantUpdate",(()=>[{no:1,name:"participants",kind:"message",T:rt,repeated:!0}])),nn=Fe.makeMessageType("livekit.UpdateSubscription",(()=>[{no:1,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:2,name:"subscribe",kind:"scalar",T:8},{no:3,name:"participant_tracks",kind:"message",T:Pt,repeated:!0}])),sn=Fe.makeMessageType("livekit.UpdateTrackSettings",(()=>[{no:1,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:3,name:"disabled",kind:"scalar",T:8},{no:4,name:"quality",kind:"enum",T:Fe.getEnumType(Ye)},{no:5,name:"width",kind:"scalar",T:13},{no:6,name:"height",kind:"scalar",T:13},{no:7,name:"fps",kind:"scalar",T:13},{no:8,name:"priority",kind:"scalar",T:13}])),on=Fe.makeMessageType("livekit.UpdateLocalAudioTrack",(()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"features",kind:"enum",T:Fe.getEnumType(nt),repeated:!0}])),rn=Fe.makeMessageType("livekit.UpdateLocalVideoTrack",(()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"width",kind:"scalar",T:13},{no:3,name:"height",kind:"scalar",T:13}])),an=Fe.makeMessageType("livekit.LeaveRequest",(()=>[{no:1,name:"can_reconnect",kind:"scalar",T:8},{no:2,name:"reason",kind:"enum",T:Fe.getEnumType($e)},{no:3,name:"action",kind:"enum",T:Fe.getEnumType(cn)},{no:4,name:"regions",kind:"message",T:On}])),cn=Fe.makeEnum("livekit.LeaveRequest.Action",[{no:0,name:"DISCONNECT"},{no:1,name:"RESUME"},{no:2,name:"RECONNECT"}]),dn=Fe.makeMessageType("livekit.UpdateVideoLayers",(()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"layers",kind:"message",T:ht,repeated:!0}])),ln=Fe.makeMessageType("livekit.UpdateParticipantMetadata",(()=>[{no:1,name:"metadata",kind:"scalar",T:9},{no:2,name:"name",kind:"scalar",T:9},{no:3,name:"attributes",kind:"map",K:9,V:{kind:"scalar",T:9}},{no:4,name:"request_id",kind:"scalar",T:13}])),un=Fe.makeMessageType("livekit.ICEServer",(()=>[{no:1,name:"urls",kind:"scalar",T:9,repeated:!0},{no:2,name:"username",kind:"scalar",T:9},{no:3,name:"credential",kind:"scalar",T:9}])),hn=Fe.makeMessageType("livekit.SpeakersChanged",(()=>[{no:1,name:"speakers",kind:"message",T:vt,repeated:!0}])),pn=Fe.makeMessageType("livekit.RoomUpdate",(()=>[{no:1,name:"room",kind:"message",T:it}])),mn=Fe.makeMessageType("livekit.ConnectionQualityInfo",(()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"quality",kind:"enum",T:Fe.getEnumType(Xe)},{no:3,name:"score",kind:"scalar",T:2}])),gn=Fe.makeMessageType("livekit.ConnectionQualityUpdate",(()=>[{no:1,name:"updates",kind:"message",T:mn,repeated:!0}])),vn=Fe.makeMessageType("livekit.StreamStateInfo",(()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sid",kind:"scalar",T:9},{no:3,name:"state",kind:"enum",T:Fe.getEnumType(qt)}])),fn=Fe.makeMessageType("livekit.StreamStateUpdate",(()=>[{no:1,name:"stream_states",kind:"message",T:vn,repeated:!0}])),kn=Fe.makeMessageType("livekit.SubscribedQuality",(()=>[{no:1,name:"quality",kind:"enum",T:Fe.getEnumType(Ye)},{no:2,name:"enabled",kind:"scalar",T:8}])),bn=Fe.makeMessageType("livekit.SubscribedCodec",(()=>[{no:1,name:"codec",kind:"scalar",T:9},{no:2,name:"qualities",kind:"message",T:kn,repeated:!0}])),yn=Fe.makeMessageType("livekit.SubscribedQualityUpdate",(()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"subscribed_qualities",kind:"message",T:kn,repeated:!0},{no:3,name:"subscribed_codecs",kind:"message",T:bn,repeated:!0}])),Tn=Fe.makeMessageType("livekit.TrackPermission",(()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"all_tracks",kind:"scalar",T:8},{no:3,name:"track_sids",kind:"scalar",T:9,repeated:!0},{no:4,name:"participant_identity",kind:"scalar",T:9}])),Cn=Fe.makeMessageType("livekit.SubscriptionPermission",(()=>[{no:1,name:"all_participants",kind:"scalar",T:8},{no:2,name:"track_permissions",kind:"message",T:Tn,repeated:!0}])),Sn=Fe.makeMessageType("livekit.SubscriptionPermissionUpdate",(()=>[{no:1,name:"participant_sid",kind:"scalar",T:9},{no:2,name:"track_sid",kind:"scalar",T:9},{no:3,name:"allowed",kind:"scalar",T:8}])),En=Fe.makeMessageType("livekit.SyncState",(()=>[{no:1,name:"answer",kind:"message",T:en},{no:2,name:"subscription",kind:"message",T:nn},{no:3,name:"publish_tracks",kind:"message",T:Zt,repeated:!0},{no:4,name:"data_channels",kind:"message",T:wn,repeated:!0},{no:5,name:"offer",kind:"message",T:en},{no:6,name:"track_sids_disabled",kind:"scalar",T:9,repeated:!0}])),wn=Fe.makeMessageType("livekit.DataChannelInfo",(()=>[{no:1,name:"label",kind:"scalar",T:9},{no:2,name:"id",kind:"scalar",T:13},{no:3,name:"target",kind:"enum",T:Fe.getEnumType(Vt)}])),Pn=Fe.makeMessageType("livekit.SimulateScenario",(()=>[{no:1,name:"speaker_update",kind:"scalar",T:5,oneof:"scenario"},{no:2,name:"node_failure",kind:"scalar",T:8,oneof:"scenario"},{no:3,name:"migration",kind:"scalar",T:8,oneof:"scenario"},{no:4,name:"server_leave",kind:"scalar",T:8,oneof:"scenario"},{no:5,name:"switch_candidate_protocol",kind:"enum",T:Fe.getEnumType(Kt),oneof:"scenario"},{no:6,name:"subscriber_bandwidth",kind:"scalar",T:3,oneof:"scenario"},{no:7,name:"disconnect_signal_on_resume",kind:"scalar",T:8,oneof:"scenario"},{no:8,name:"disconnect_signal_on_resume_no_messages",kind:"scalar",T:8,oneof:"scenario"},{no:9,name:"leave_request_full_reconnect",kind:"scalar",T:8,oneof:"scenario"}])),Rn=Fe.makeMessageType("livekit.Ping",(()=>[{no:1,name:"timestamp",kind:"scalar",T:3},{no:2,name:"rtt",kind:"scalar",T:3}])),In=Fe.makeMessageType("livekit.Pong",(()=>[{no:1,name:"last_ping_timestamp",kind:"scalar",T:3},{no:2,name:"timestamp",kind:"scalar",T:3}])),On=Fe.makeMessageType("livekit.RegionSettings",(()=>[{no:1,name:"regions",kind:"message",T:Dn,repeated:!0}])),Dn=Fe.makeMessageType("livekit.RegionInfo",(()=>[{no:1,name:"region",kind:"scalar",T:9},{no:2,name:"url",kind:"scalar",T:9},{no:3,name:"distance",kind:"scalar",T:3}])),xn=Fe.makeMessageType("livekit.SubscriptionResponse",(()=>[{no:1,name:"track_sid",kind:"scalar",T:9},{no:2,name:"err",kind:"enum",T:Fe.getEnumType(tt)}])),Mn=Fe.makeMessageType("livekit.RequestResponse",(()=>[{no:1,name:"request_id",kind:"scalar",T:13},{no:2,name:"reason",kind:"enum",T:Fe.getEnumType(Nn)},{no:3,name:"message",kind:"scalar",T:9}])),Nn=Fe.makeEnum("livekit.RequestResponse.Reason",[{no:0,name:"OK"},{no:1,name:"NOT_FOUND"},{no:2,name:"NOT_ALLOWED"},{no:3,name:"LIMIT_EXCEEDED"}]),_n=Fe.makeMessageType("livekit.TrackSubscribed",(()=>[{no:1,name:"track_sid",kind:"scalar",T:9}]));function An(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var Ln,Un={exports:{}},jn=Un.exports;var Fn,Bn,Vn=(Ln||(Ln=1,function(e){var t,n;t=jn,n=function(){var e=function(){},t="undefined",n=typeof window!==t&&typeof window.navigator!==t&&/Trident\/|MSIE /.test(window.navigator.userAgent),i=["trace","debug","info","warn","error"],s={},o=null;function r(e,t){var n=e[t];if("function"==typeof n.bind)return n.bind(e);try{return Function.prototype.bind.call(n,e)}catch(t){return function(){return Function.prototype.apply.apply(n,[e,arguments])}}}function a(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function c(){for(var n=this.getLevel(),s=0;s<i.length;s++){var o=i[s];this[o]=s<n?e:this.methodFactory(o,n,this.name)}if(this.log=this.debug,typeof console===t&&n<this.levels.SILENT)return"No console available for logging"}function d(e){return function(){typeof console!==t&&(c.call(this),this[e].apply(this,arguments))}}function l(i,s,o){return function(i){return"debug"===i&&(i="log"),typeof console!==t&&("trace"===i&&n?a:void 0!==console[i]?r(console,i):void 0!==console.log?r(console,"log"):e)}(i)||d.apply(this,arguments)}function u(e,n){var r,a,d,u=this,h="loglevel";function p(){var e;if(typeof window!==t&&h){try{e=window.localStorage[h]}catch(e){}if(typeof e===t)try{var n=window.document.cookie,i=encodeURIComponent(h),s=n.indexOf(i+"=");-1!==s&&(e=/^([^;]+)/.exec(n.slice(s+i.length+1))[1])}catch(e){}return void 0===u.levels[e]&&(e=void 0),e}}function m(e){var t=e;if("string"==typeof t&&void 0!==u.levels[t.toUpperCase()]&&(t=u.levels[t.toUpperCase()]),"number"==typeof t&&t>=0&&t<=u.levels.SILENT)return t;throw new TypeError("log.setLevel() called with invalid level: "+e)}"string"==typeof e?h+=":"+e:"symbol"==typeof e&&(h=void 0),u.name=e,u.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},u.methodFactory=n||l,u.getLevel=function(){return null!=d?d:null!=a?a:r},u.setLevel=function(e,n){return d=m(e),!1!==n&&function(e){var n=(i[e]||"silent").toUpperCase();if(typeof window!==t&&h){try{return void(window.localStorage[h]=n)}catch(e){}try{window.document.cookie=encodeURIComponent(h)+"="+n+";"}catch(e){}}}(d),c.call(u)},u.setDefaultLevel=function(e){a=m(e),p()||u.setLevel(e,!1)},u.resetLevel=function(){d=null,function(){if(typeof window!==t&&h){try{window.localStorage.removeItem(h)}catch(e){}try{window.document.cookie=encodeURIComponent(h)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch(e){}}}(),c.call(u)},u.enableAll=function(e){u.setLevel(u.levels.TRACE,e)},u.disableAll=function(e){u.setLevel(u.levels.SILENT,e)},u.rebuild=function(){if(o!==u&&(r=m(o.getLevel())),c.call(u),o===u)for(var e in s)s[e].rebuild()},r=m(o?o.getLevel():"WARN");var g=p();null!=g&&(d=m(g)),c.call(u)}(o=new u).getLogger=function(e){if("symbol"!=typeof e&&"string"!=typeof e||""===e)throw new TypeError("You must supply a name when creating a logger.");var t=s[e];return t||(t=s[e]=new u(e,o.methodFactory)),t};var h=typeof window!==t?window.log:void 0;return o.noConflict=function(){return typeof window!==t&&window.log===o&&(window.log=h),o},o.getLoggers=function(){return s},o.default=o,o},e.exports?e.exports=n():t.log=n()}(Un)),Un.exports);e.LogLevel=void 0,(Fn=e.LogLevel||(e.LogLevel={}))[Fn.trace=0]="trace",Fn[Fn.debug=1]="debug",Fn[Fn.info=2]="info",Fn[Fn.warn=3]="warn",Fn[Fn.error=4]="error",Fn[Fn.silent=5]="silent",e.LoggerNames=void 0,(Bn=e.LoggerNames||(e.LoggerNames={})).Default="livekit",Bn.Room="livekit-room",Bn.Participant="livekit-participant",Bn.Track="livekit-track",Bn.Publication="livekit-track-publication",Bn.Engine="livekit-engine",Bn.Signal="livekit-signal",Bn.PCManager="livekit-pc-manager",Bn.PCTransport="livekit-pc-transport",Bn.E2EE="lk-e2ee";let qn=Vn.getLogger("livekit");const Kn=Object.values(e.LoggerNames).map((e=>Vn.getLogger(e)));function Wn(e){const t=Vn.getLogger(e);return t.setDefaultLevel(qn.getLevel()),t}qn.setDefaultLevel(e.LogLevel.info);const Hn=Vn.getLogger("lk-e2ee"),Gn=7e3,zn=[0,300,1200,2700,4800,Gn,Gn,Gn,Gn,Gn];class Jn{constructor(e){this._retryDelays=void 0!==e?[...e]:zn}nextRetryDelayInMs(e){if(e.retryCount>=this._retryDelays.length)return null;const t=this._retryDelays[e.retryCount];return e.retryCount<=1?t:t+1e3*Math.random()}}function Qn(e,t,n,i){return new(n||(n=Promise))((function(s,o){function r(e){try{c(i.next(e))}catch(e){o(e)}}function a(e){try{c(i.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?s(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(r,a)}c((i=i.apply(e,t||[])).next())}))}function Yn(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],i=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&i>=e.length&&(e=void 0),{value:e&&e[i++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function Xn(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=Yn(e),t={},i("next"),i("throw"),i("return"),t[Symbol.asyncIterator]=function(){return this},t);function i(n){t[n]=e[n]&&function(t){return new Promise((function(i,s){(function(e,t,n,i){Promise.resolve(i).then((function(t){e({value:t,done:n})}),t)})(i,s,(t=e[n](t)).done,t.value)}))}}}"function"==typeof SuppressedError&&SuppressedError;var Zn,$n={exports:{}};var ei=function(){if(Zn)return $n.exports;Zn=1;var e,t="object"==typeof Reflect?Reflect:null,n=t&&"function"==typeof t.apply?t.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};e=t&&"function"==typeof t.ownKeys?t.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var i=Number.isNaN||function(e){return e!=e};function s(){s.init.call(this)}$n.exports=s,$n.exports.once=function(e,t){return new Promise((function(n,i){function s(n){e.removeListener(t,o),i(n)}function o(){"function"==typeof e.removeListener&&e.removeListener("error",s),n([].slice.call(arguments))}m(e,t,o,{once:!0}),"error"!==t&&function(e,t,n){"function"==typeof e.on&&m(e,"error",t,n)}(e,s,{once:!0})}))},s.EventEmitter=s,s.prototype._events=void 0,s.prototype._eventsCount=0,s.prototype._maxListeners=void 0;var o=10;function r(e){if("function"!=typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function a(e){return void 0===e._maxListeners?s.defaultMaxListeners:e._maxListeners}function c(e,t,n,i){var s,o,c,d;if(r(n),void 0===(o=e._events)?(o=e._events=Object.create(null),e._eventsCount=0):(void 0!==o.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),o=e._events),c=o[t]),void 0===c)c=o[t]=n,++e._eventsCount;else if("function"==typeof c?c=o[t]=i?[n,c]:[c,n]:i?c.unshift(n):c.push(n),(s=a(e))>0&&c.length>s&&!c.warned){c.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+c.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=c.length,d=l,console&&console.warn&&console.warn(d)}return e}function d(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function l(e,t,n){var i={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},s=d.bind(i);return s.listener=n,i.wrapFn=s,s}function u(e,t,n){var i=e._events;if(void 0===i)return[];var s=i[t];return void 0===s?[]:"function"==typeof s?n?[s.listener||s]:[s]:n?function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(s):p(s,s.length)}function h(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"==typeof n)return 1;if(void 0!==n)return n.length}return 0}function p(e,t){for(var n=new Array(t),i=0;i<t;++i)n[i]=e[i];return n}function m(e,t,n,i){if("function"==typeof e.on)i.once?e.once(t,n):e.on(t,n);else{if("function"!=typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function s(o){i.once&&e.removeEventListener(t,s),n(o)}))}}return Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return o},set:function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");o=e}}),s.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},s.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||i(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},s.prototype.getMaxListeners=function(){return a(this)},s.prototype.emit=function(e){for(var t=[],i=1;i<arguments.length;i++)t.push(arguments[i]);var s="error"===e,o=this._events;if(void 0!==o)s=s&&void 0===o.error;else if(!s)return!1;if(s){var r;if(t.length>0&&(r=t[0]),r instanceof Error)throw r;var a=new Error("Unhandled error."+(r?" ("+r.message+")":""));throw a.context=r,a}var c=o[e];if(void 0===c)return!1;if("function"==typeof c)n(c,this,t);else{var d=c.length,l=p(c,d);for(i=0;i<d;++i)n(l[i],this,t)}return!0},s.prototype.addListener=function(e,t){return c(this,e,t,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(e,t){return c(this,e,t,!0)},s.prototype.once=function(e,t){return r(t),this.on(e,l(this,e,t)),this},s.prototype.prependOnceListener=function(e,t){return r(t),this.prependListener(e,l(this,e,t)),this},s.prototype.removeListener=function(e,t){var n,i,s,o,a;if(r(t),void 0===(i=this._events))return this;if(void 0===(n=i[e]))return this;if(n===t||n.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(s=-1,o=n.length-1;o>=0;o--)if(n[o]===t||n[o].listener===t){a=n[o].listener,s=o;break}if(s<0)return this;0===s?n.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(n,s),1===n.length&&(i[e]=n[0]),void 0!==i.removeListener&&this.emit("removeListener",e,a||t)}return this},s.prototype.off=s.prototype.removeListener,s.prototype.removeAllListeners=function(e){var t,n,i;if(void 0===(n=this._events))return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var s,o=Object.keys(n);for(i=0;i<o.length;++i)"removeListener"!==(s=o[i])&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(void 0!==t)for(i=t.length-1;i>=0;i--)this.removeListener(e,t[i]);return this},s.prototype.listeners=function(e){return u(this,e,!0)},s.prototype.rawListeners=function(e){return u(this,e,!1)},s.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):h.call(e,t)},s.prototype.listenerCount=h,s.prototype.eventNames=function(){return this._eventsCount>0?e(this._events):[]},$n.exports}();let ti=!0,ni=!0;function ii(e,t,n){const i=e.match(t);return i&&i.length>=n&&parseInt(i[n],10)}function si(e,t,n){if(!e.RTCPeerConnection)return;const i=e.RTCPeerConnection.prototype,s=i.addEventListener;i.addEventListener=function(e,i){if(e!==t)return s.apply(this,arguments);const o=e=>{const t=n(e);t&&(i.handleEvent?i.handleEvent(t):i(t))};return this._eventMap=this._eventMap||{},this._eventMap[t]||(this._eventMap[t]=new Map),this._eventMap[t].set(i,o),s.apply(this,[e,o])};const o=i.removeEventListener;i.removeEventListener=function(e,n){if(e!==t||!this._eventMap||!this._eventMap[t])return o.apply(this,arguments);if(!this._eventMap[t].has(n))return o.apply(this,arguments);const i=this._eventMap[t].get(n);return this._eventMap[t].delete(n),0===this._eventMap[t].size&&delete this._eventMap[t],0===Object.keys(this._eventMap).length&&delete this._eventMap,o.apply(this,[e,i])},Object.defineProperty(i,"on"+t,{get(){return this["_on"+t]},set(e){this["_on"+t]&&(this.removeEventListener(t,this["_on"+t]),delete this["_on"+t]),e&&this.addEventListener(t,this["_on"+t]=e)},enumerable:!0,configurable:!0})}function oi(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(ti=e,e?"adapter.js logging disabled":"adapter.js logging enabled")}function ri(e){return"boolean"!=typeof e?new Error("Argument type: "+typeof e+". Please use a boolean."):(ni=!e,"adapter.js deprecation warnings "+(e?"disabled":"enabled"))}function ai(){if("object"==typeof window){if(ti)return;"undefined"!=typeof console&&"function"==typeof console.log&&console.log.apply(console,arguments)}}function ci(e,t){ni&&console.warn(e+" is deprecated, please use "+t+" instead.")}function di(e){return"[object Object]"===Object.prototype.toString.call(e)}function li(e){return di(e)?Object.keys(e).reduce((function(t,n){const i=di(e[n]),s=i?li(e[n]):e[n],o=i&&!Object.keys(s).length;return void 0===s||o?t:Object.assign(t,{[n]:s})}),{}):e}function ui(e,t,n){t&&!n.has(t.id)&&(n.set(t.id,t),Object.keys(t).forEach((i=>{i.endsWith("Id")?ui(e,e.get(t[i]),n):i.endsWith("Ids")&&t[i].forEach((t=>{ui(e,e.get(t),n)}))})))}function hi(e,t,n){const i=n?"outbound-rtp":"inbound-rtp",s=new Map;if(null===t)return s;const o=[];return e.forEach((e=>{"track"===e.type&&e.trackIdentifier===t.id&&o.push(e)})),o.forEach((t=>{e.forEach((n=>{n.type===i&&n.trackId===t.id&&ui(e,n,s)}))})),s}const pi=ai;function mi(e,t){const n=e&&e.navigator;if(!n.mediaDevices)return;const i=function(e){if("object"!=typeof e||e.mandatory||e.optional)return e;const t={};return Object.keys(e).forEach((n=>{if("require"===n||"advanced"===n||"mediaSource"===n)return;const i="object"==typeof e[n]?e[n]:{ideal:e[n]};void 0!==i.exact&&"number"==typeof i.exact&&(i.min=i.max=i.exact);const s=function(e,t){return e?e+t.charAt(0).toUpperCase()+t.slice(1):"deviceId"===t?"sourceId":t};if(void 0!==i.ideal){t.optional=t.optional||[];let e={};"number"==typeof i.ideal?(e[s("min",n)]=i.ideal,t.optional.push(e),e={},e[s("max",n)]=i.ideal,t.optional.push(e)):(e[s("",n)]=i.ideal,t.optional.push(e))}void 0!==i.exact&&"number"!=typeof i.exact?(t.mandatory=t.mandatory||{},t.mandatory[s("",n)]=i.exact):["min","max"].forEach((e=>{void 0!==i[e]&&(t.mandatory=t.mandatory||{},t.mandatory[s(e,n)]=i[e])}))})),e.advanced&&(t.optional=(t.optional||[]).concat(e.advanced)),t},s=function(e,s){if(t.version>=61)return s(e);if((e=JSON.parse(JSON.stringify(e)))&&"object"==typeof e.audio){const t=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])};t((e=JSON.parse(JSON.stringify(e))).audio,"autoGainControl","googAutoGainControl"),t(e.audio,"noiseSuppression","googNoiseSuppression"),e.audio=i(e.audio)}if(e&&"object"==typeof e.video){let o=e.video.facingMode;o=o&&("object"==typeof o?o:{ideal:o});const r=t.version<66;if(o&&("user"===o.exact||"environment"===o.exact||"user"===o.ideal||"environment"===o.ideal)&&(!n.mediaDevices.getSupportedConstraints||!n.mediaDevices.getSupportedConstraints().facingMode||r)){let t;if(delete e.video.facingMode,"environment"===o.exact||"environment"===o.ideal?t=["back","rear"]:"user"!==o.exact&&"user"!==o.ideal||(t=["front"]),t)return n.mediaDevices.enumerateDevices().then((n=>{let r=(n=n.filter((e=>"videoinput"===e.kind))).find((e=>t.some((t=>e.label.toLowerCase().includes(t)))));return!r&&n.length&&t.includes("back")&&(r=n[n.length-1]),r&&(e.video.deviceId=o.exact?{exact:r.deviceId}:{ideal:r.deviceId}),e.video=i(e.video),pi("chrome: "+JSON.stringify(e)),s(e)}))}e.video=i(e.video)}return pi("chrome: "+JSON.stringify(e)),s(e)},o=function(e){return t.version>=64?e:{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString(){return this.name+(this.message&&": ")+this.message}}};if(n.getUserMedia=function(e,t,i){s(e,(e=>{n.webkitGetUserMedia(e,t,(e=>{i&&i(o(e))}))}))}.bind(n),n.mediaDevices.getUserMedia){const e=n.mediaDevices.getUserMedia.bind(n.mediaDevices);n.mediaDevices.getUserMedia=function(t){return s(t,(t=>e(t).then((e=>{if(t.audio&&!e.getAudioTracks().length||t.video&&!e.getVideoTracks().length)throw e.getTracks().forEach((e=>{e.stop()})),new DOMException("","NotFoundError");return e}),(e=>Promise.reject(o(e))))))}}}function gi(e){e.MediaStream=e.MediaStream||e.webkitMediaStream}function vi(e){if("object"==typeof e&&e.RTCPeerConnection&&!("ontrack"in e.RTCPeerConnection.prototype)){Object.defineProperty(e.RTCPeerConnection.prototype,"ontrack",{get(){return this._ontrack},set(e){this._ontrack&&this.removeEventListener("track",this._ontrack),this.addEventListener("track",this._ontrack=e)},enumerable:!0,configurable:!0});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){return this._ontrackpoly||(this._ontrackpoly=t=>{t.stream.addEventListener("addtrack",(n=>{let i;i=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===n.track.id)):{track:n.track};const s=new Event("track");s.track=n.track,s.receiver=i,s.transceiver={receiver:i},s.streams=[t.stream],this.dispatchEvent(s)})),t.stream.getTracks().forEach((n=>{let i;i=e.RTCPeerConnection.prototype.getReceivers?this.getReceivers().find((e=>e.track&&e.track.id===n.id)):{track:n};const s=new Event("track");s.track=n,s.receiver=i,s.transceiver={receiver:i},s.streams=[t.stream],this.dispatchEvent(s)}))},this.addEventListener("addstream",this._ontrackpoly)),t.apply(this,arguments)}}else si(e,"track",(e=>(e.transceiver||Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}}),e)))}function fi(e){if("object"==typeof e&&e.RTCPeerConnection&&!("getSenders"in e.RTCPeerConnection.prototype)&&"createDTMFSender"in e.RTCPeerConnection.prototype){const t=function(e,t){return{track:t,get dtmf(){return void 0===this._dtmf&&("audio"===t.kind?this._dtmf=e.createDTMFSender(t):this._dtmf=null),this._dtmf},_pc:e}};if(!e.RTCPeerConnection.prototype.getSenders){e.RTCPeerConnection.prototype.getSenders=function(){return this._senders=this._senders||[],this._senders.slice()};const n=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,i){let s=n.apply(this,arguments);return s||(s=t(this,e),this._senders.push(s)),s};const i=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){i.apply(this,arguments);const t=this._senders.indexOf(e);-1!==t&&this._senders.splice(t,1)}}const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._senders=this._senders||[],n.apply(this,[e]),e.getTracks().forEach((e=>{this._senders.push(t(this,e))}))};const i=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){this._senders=this._senders||[],i.apply(this,[e]),e.getTracks().forEach((e=>{const t=this._senders.find((t=>t.track===e));t&&this._senders.splice(this._senders.indexOf(t),1)}))}}else if("object"==typeof e&&e.RTCPeerConnection&&"getSenders"in e.RTCPeerConnection.prototype&&"createDTMFSender"in e.RTCPeerConnection.prototype&&e.RTCRtpSender&&!("dtmf"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e},Object.defineProperty(e.RTCRtpSender.prototype,"dtmf",{get(){return void 0===this._dtmf&&("audio"===this.track.kind?this._dtmf=this._pc.createDTMFSender(this.track):this._dtmf=null),this._dtmf}})}}function ki(e){if(!("object"==typeof e&&e.RTCPeerConnection&&e.RTCRtpSender&&e.RTCRtpReceiver))return;if(!("getStats"in e.RTCRtpSender.prototype)){const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>hi(t,e.track,!0)))}}if(!("getStats"in e.RTCRtpReceiver.prototype)){const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),si(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){const e=this;return this._pc.getStats().then((t=>hi(t,e.track,!1)))}}if(!("getStats"in e.RTCRtpSender.prototype)||!("getStats"in e.RTCRtpReceiver.prototype))return;const t=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){if(arguments.length>0&&arguments[0]instanceof e.MediaStreamTrack){const e=arguments[0];let t,n,i;return this.getSenders().forEach((n=>{n.track===e&&(t?i=!0:t=n)})),this.getReceivers().forEach((t=>(t.track===e&&(n?i=!0:n=t),t.track===e))),i||t&&n?Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError")):t?t.getStats():n?n.getStats():Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return t.apply(this,arguments)}}function bi(e){e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},Object.keys(this._shimmedLocalStreams).map((e=>this._shimmedLocalStreams[e][0]))};const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addTrack=function(e,n){if(!n)return t.apply(this,arguments);this._shimmedLocalStreams=this._shimmedLocalStreams||{};const i=t.apply(this,arguments);return this._shimmedLocalStreams[n.id]?-1===this._shimmedLocalStreams[n.id].indexOf(i)&&this._shimmedLocalStreams[n.id].push(i):this._shimmedLocalStreams[n.id]=[n,i],i};const n=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(e){this._shimmedLocalStreams=this._shimmedLocalStreams||{},e.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")}));const t=this.getSenders();n.apply(this,arguments);const i=this.getSenders().filter((e=>-1===t.indexOf(e)));this._shimmedLocalStreams[e.id]=[e].concat(i)};const i=e.RTCPeerConnection.prototype.removeStream;e.RTCPeerConnection.prototype.removeStream=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},delete this._shimmedLocalStreams[e.id],i.apply(this,arguments)};const s=e.RTCPeerConnection.prototype.removeTrack;e.RTCPeerConnection.prototype.removeTrack=function(e){return this._shimmedLocalStreams=this._shimmedLocalStreams||{},e&&Object.keys(this._shimmedLocalStreams).forEach((t=>{const n=this._shimmedLocalStreams[t].indexOf(e);-1!==n&&this._shimmedLocalStreams[t].splice(n,1),1===this._shimmedLocalStreams[t].length&&delete this._shimmedLocalStreams[t]})),s.apply(this,arguments)}}function yi(e,t){if(!e.RTCPeerConnection)return;if(e.RTCPeerConnection.prototype.addTrack&&t.version>=65)return bi(e);const n=e.RTCPeerConnection.prototype.getLocalStreams;e.RTCPeerConnection.prototype.getLocalStreams=function(){const e=n.apply(this);return this._reverseStreams=this._reverseStreams||{},e.map((e=>this._reverseStreams[e.id]))};const i=e.RTCPeerConnection.prototype.addStream;e.RTCPeerConnection.prototype.addStream=function(t){if(this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},t.getTracks().forEach((e=>{if(this.getSenders().find((t=>t.track===e)))throw new DOMException("Track already exists.","InvalidAccessError")})),!this._reverseStreams[t.id]){const n=new e.MediaStream(t.getTracks());this._streams[t.id]=n,this._reverseStreams[n.id]=t,t=n}i.apply(this,[t])};const s=e.RTCPeerConnection.prototype.removeStream;function o(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const i=e._reverseStreams[t],s=e._streams[i.id];n=n.replace(new RegExp(s.id,"g"),i.id)})),new RTCSessionDescription({type:t.type,sdp:n})}e.RTCPeerConnection.prototype.removeStream=function(e){this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{},s.apply(this,[this._streams[e.id]||e]),delete this._reverseStreams[this._streams[e.id]?this._streams[e.id].id:e.id],delete this._streams[e.id]},e.RTCPeerConnection.prototype.addTrack=function(t,n){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");const i=[].slice.call(arguments,1);if(1!==i.length||!i[0].getTracks().find((e=>e===t)))throw new DOMException("The adapter.js addTrack polyfill only supports a single  stream which is associated with the specified track.","NotSupportedError");if(this.getSenders().find((e=>e.track===t)))throw new DOMException("Track already exists.","InvalidAccessError");this._streams=this._streams||{},this._reverseStreams=this._reverseStreams||{};const s=this._streams[n.id];if(s)s.addTrack(t),Promise.resolve().then((()=>{this.dispatchEvent(new Event("negotiationneeded"))}));else{const i=new e.MediaStream([t]);this._streams[n.id]=i,this._reverseStreams[i.id]=n,this.addStream(i)}return this.getSenders().find((e=>e.track===t))},["createOffer","createAnswer"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],i={[t](){const e=arguments;return arguments.length&&"function"==typeof arguments[0]?n.apply(this,[t=>{const n=o(this,t);e[0].apply(null,[n])},t=>{e[1]&&e[1].apply(null,t)},arguments[2]]):n.apply(this,arguments).then((e=>o(this,e)))}};e.RTCPeerConnection.prototype[t]=i[t]}));const r=e.RTCPeerConnection.prototype.setLocalDescription;e.RTCPeerConnection.prototype.setLocalDescription=function(){return arguments.length&&arguments[0].type?(arguments[0]=function(e,t){let n=t.sdp;return Object.keys(e._reverseStreams||[]).forEach((t=>{const i=e._reverseStreams[t],s=e._streams[i.id];n=n.replace(new RegExp(i.id,"g"),s.id)})),new RTCSessionDescription({type:t.type,sdp:n})}(this,arguments[0]),r.apply(this,arguments)):r.apply(this,arguments)};const a=Object.getOwnPropertyDescriptor(e.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(e.RTCPeerConnection.prototype,"localDescription",{get(){const e=a.get.apply(this);return""===e.type?e:o(this,e)}}),e.RTCPeerConnection.prototype.removeTrack=function(e){if("closed"===this.signalingState)throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError");if(!e._pc)throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack does not implement interface RTCRtpSender.","TypeError");if(!(e._pc===this))throw new DOMException("Sender was not created by this connection.","InvalidAccessError");let t;this._streams=this._streams||{},Object.keys(this._streams).forEach((n=>{this._streams[n].getTracks().find((t=>e.track===t))&&(t=this._streams[n])})),t&&(1===t.getTracks().length?this.removeStream(this._reverseStreams[t.id]):t.removeTrack(e.track),this.dispatchEvent(new Event("negotiationneeded")))}}function Ti(e,t){!e.RTCPeerConnection&&e.webkitRTCPeerConnection&&(e.RTCPeerConnection=e.webkitRTCPeerConnection),e.RTCPeerConnection&&t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],i={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=i[t]}))}function Ci(e,t){si(e,"negotiationneeded",(e=>{const n=e.target;if(!(t.version<72||n.getConfiguration&&"plan-b"===n.getConfiguration().sdpSemantics)||"stable"===n.signalingState)return e}))}var Si=Object.freeze({__proto__:null,fixNegotiationNeeded:Ci,shimAddTrackRemoveTrack:yi,shimAddTrackRemoveTrackWithNative:bi,shimGetSendersWithDtmf:fi,shimGetUserMedia:mi,shimMediaStream:gi,shimOnTrack:vi,shimPeerConnection:Ti,shimSenderReceiverGetStats:ki});function Ei(e,t){const n=e&&e.navigator,i=e&&e.MediaStreamTrack;if(n.getUserMedia=function(e,t,i){ci("navigator.getUserMedia","navigator.mediaDevices.getUserMedia"),n.mediaDevices.getUserMedia(e).then(t,i)},!(t.version>55&&"autoGainControl"in n.mediaDevices.getSupportedConstraints())){const e=function(e,t,n){t in e&&!(n in e)&&(e[n]=e[t],delete e[t])},t=n.mediaDevices.getUserMedia.bind(n.mediaDevices);if(n.mediaDevices.getUserMedia=function(n){return"object"==typeof n&&"object"==typeof n.audio&&(n=JSON.parse(JSON.stringify(n)),e(n.audio,"autoGainControl","mozAutoGainControl"),e(n.audio,"noiseSuppression","mozNoiseSuppression")),t(n)},i&&i.prototype.getSettings){const t=i.prototype.getSettings;i.prototype.getSettings=function(){const n=t.apply(this,arguments);return e(n,"mozAutoGainControl","autoGainControl"),e(n,"mozNoiseSuppression","noiseSuppression"),n}}if(i&&i.prototype.applyConstraints){const t=i.prototype.applyConstraints;i.prototype.applyConstraints=function(n){return"audio"===this.kind&&"object"==typeof n&&(n=JSON.parse(JSON.stringify(n)),e(n,"autoGainControl","mozAutoGainControl"),e(n,"noiseSuppression","mozNoiseSuppression")),t.apply(this,[n])}}}}function wi(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Pi(e,t){if("object"!=typeof e||!e.RTCPeerConnection&&!e.mozRTCPeerConnection)return;!e.RTCPeerConnection&&e.mozRTCPeerConnection&&(e.RTCPeerConnection=e.mozRTCPeerConnection),t.version<53&&["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach((function(t){const n=e.RTCPeerConnection.prototype[t],i={[t](){return arguments[0]=new("addIceCandidate"===t?e.RTCIceCandidate:e.RTCSessionDescription)(arguments[0]),n.apply(this,arguments)}};e.RTCPeerConnection.prototype[t]=i[t]}));const n={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"},i=e.RTCPeerConnection.prototype.getStats;e.RTCPeerConnection.prototype.getStats=function(){const[e,s,o]=arguments;return i.apply(this,[e||null]).then((e=>{if(t.version<53&&!s)try{e.forEach((e=>{e.type=n[e.type]||e.type}))}catch(t){if("TypeError"!==t.name)throw t;e.forEach(((t,i)=>{e.set(i,Object.assign({},t,{type:n[t.type]||t.type}))}))}return e})).then(s,o)}}function Ri(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpSender.prototype)return;const t=e.RTCPeerConnection.prototype.getSenders;t&&(e.RTCPeerConnection.prototype.getSenders=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e});const n=e.RTCPeerConnection.prototype.addTrack;n&&(e.RTCPeerConnection.prototype.addTrack=function(){const e=n.apply(this,arguments);return e._pc=this,e}),e.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}}function Ii(e){if("object"!=typeof e||!e.RTCPeerConnection||!e.RTCRtpSender)return;if(e.RTCRtpSender&&"getStats"in e.RTCRtpReceiver.prototype)return;const t=e.RTCPeerConnection.prototype.getReceivers;t&&(e.RTCPeerConnection.prototype.getReceivers=function(){const e=t.apply(this,[]);return e.forEach((e=>e._pc=this)),e}),si(e,"track",(e=>(e.receiver._pc=e.srcElement,e))),e.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}}function Oi(e){e.RTCPeerConnection&&!("removeStream"in e.RTCPeerConnection.prototype)&&(e.RTCPeerConnection.prototype.removeStream=function(e){ci("removeStream","removeTrack"),this.getSenders().forEach((t=>{t.track&&e.getTracks().includes(t.track)&&this.removeTrack(t)}))})}function Di(e){e.DataChannel&&!e.RTCDataChannel&&(e.RTCDataChannel=e.DataChannel)}function xi(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.addTransceiver;t&&(e.RTCPeerConnection.prototype.addTransceiver=function(){this.setParametersPromises=[];let e=arguments[1]&&arguments[1].sendEncodings;void 0===e&&(e=[]),e=[...e];const n=e.length>0;n&&e.forEach((e=>{if("rid"in e){if(!/^[a-z0-9]{0,16}$/i.test(e.rid))throw new TypeError("Invalid RID value provided.")}if("scaleResolutionDownBy"in e&&!(parseFloat(e.scaleResolutionDownBy)>=1))throw new RangeError("scale_resolution_down_by must be >= 1.0");if("maxFramerate"in e&&!(parseFloat(e.maxFramerate)>=0))throw new RangeError("max_framerate must be >= 0.0")}));const i=t.apply(this,arguments);if(n){const{sender:t}=i,n=t.getParameters();(!("encodings"in n)||1===n.encodings.length&&0===Object.keys(n.encodings[0]).length)&&(n.encodings=e,t.sendEncodings=e,this.setParametersPromises.push(t.setParameters(n).then((()=>{delete t.sendEncodings})).catch((()=>{delete t.sendEncodings}))))}return i})}function Mi(e){if("object"!=typeof e||!e.RTCRtpSender)return;const t=e.RTCRtpSender.prototype.getParameters;t&&(e.RTCRtpSender.prototype.getParameters=function(){const e=t.apply(this,arguments);return"encodings"in e||(e.encodings=[].concat(this.sendEncodings||[{}])),e})}function Ni(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}function _i(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype.createAnswer;e.RTCPeerConnection.prototype.createAnswer=function(){return this.setParametersPromises&&this.setParametersPromises.length?Promise.all(this.setParametersPromises).then((()=>t.apply(this,arguments))).finally((()=>{this.setParametersPromises=[]})):t.apply(this,arguments)}}var Ai=Object.freeze({__proto__:null,shimAddTransceiver:xi,shimCreateAnswer:_i,shimCreateOffer:Ni,shimGetDisplayMedia:function(e,t){e.navigator.mediaDevices&&"getDisplayMedia"in e.navigator.mediaDevices||e.navigator.mediaDevices&&(e.navigator.mediaDevices.getDisplayMedia=function(n){if(!n||!n.video){const e=new DOMException("getDisplayMedia without video constraints is undefined");return e.name="NotFoundError",e.code=8,Promise.reject(e)}return!0===n.video?n.video={mediaSource:t}:n.video.mediaSource=t,e.navigator.mediaDevices.getUserMedia(n)})},shimGetParameters:Mi,shimGetUserMedia:Ei,shimOnTrack:wi,shimPeerConnection:Pi,shimRTCDataChannel:Di,shimReceiverGetStats:Ii,shimRemoveStream:Oi,shimSenderGetStats:Ri});function Li(e){if("object"==typeof e&&e.RTCPeerConnection){if("getLocalStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getLocalStreams=function(){return this._localStreams||(this._localStreams=[]),this._localStreams}),!("addStream"in e.RTCPeerConnection.prototype)){const t=e.RTCPeerConnection.prototype.addTrack;e.RTCPeerConnection.prototype.addStream=function(e){this._localStreams||(this._localStreams=[]),this._localStreams.includes(e)||this._localStreams.push(e),e.getAudioTracks().forEach((n=>t.call(this,n,e))),e.getVideoTracks().forEach((n=>t.call(this,n,e)))},e.RTCPeerConnection.prototype.addTrack=function(e){for(var n=arguments.length,i=new Array(n>1?n-1:0),s=1;s<n;s++)i[s-1]=arguments[s];return i&&i.forEach((e=>{this._localStreams?this._localStreams.includes(e)||this._localStreams.push(e):this._localStreams=[e]})),t.apply(this,arguments)}}"removeStream"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.removeStream=function(e){this._localStreams||(this._localStreams=[]);const t=this._localStreams.indexOf(e);if(-1===t)return;this._localStreams.splice(t,1);const n=e.getTracks();this.getSenders().forEach((e=>{n.includes(e.track)&&this.removeTrack(e)}))})}}function Ui(e){if("object"==typeof e&&e.RTCPeerConnection&&("getRemoteStreams"in e.RTCPeerConnection.prototype||(e.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}),!("onaddstream"in e.RTCPeerConnection.prototype))){Object.defineProperty(e.RTCPeerConnection.prototype,"onaddstream",{get(){return this._onaddstream},set(e){this._onaddstream&&(this.removeEventListener("addstream",this._onaddstream),this.removeEventListener("track",this._onaddstreampoly)),this.addEventListener("addstream",this._onaddstream=e),this.addEventListener("track",this._onaddstreampoly=e=>{e.streams.forEach((e=>{if(this._remoteStreams||(this._remoteStreams=[]),this._remoteStreams.includes(e))return;this._remoteStreams.push(e);const t=new Event("addstream");t.stream=e,this.dispatchEvent(t)}))})}});const t=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){const e=this;return this._onaddstreampoly||this.addEventListener("track",this._onaddstreampoly=function(t){t.streams.forEach((t=>{if(e._remoteStreams||(e._remoteStreams=[]),e._remoteStreams.indexOf(t)>=0)return;e._remoteStreams.push(t);const n=new Event("addstream");n.stream=t,e.dispatchEvent(n)}))}),t.apply(e,arguments)}}}function ji(e){if("object"!=typeof e||!e.RTCPeerConnection)return;const t=e.RTCPeerConnection.prototype,n=t.createOffer,i=t.createAnswer,s=t.setLocalDescription,o=t.setRemoteDescription,r=t.addIceCandidate;t.createOffer=function(e,t){const i=arguments.length>=2?arguments[2]:arguments[0],s=n.apply(this,[i]);return t?(s.then(e,t),Promise.resolve()):s},t.createAnswer=function(e,t){const n=arguments.length>=2?arguments[2]:arguments[0],s=i.apply(this,[n]);return t?(s.then(e,t),Promise.resolve()):s};let a=function(e,t,n){const i=s.apply(this,[e]);return n?(i.then(t,n),Promise.resolve()):i};t.setLocalDescription=a,a=function(e,t,n){const i=o.apply(this,[e]);return n?(i.then(t,n),Promise.resolve()):i},t.setRemoteDescription=a,a=function(e,t,n){const i=r.apply(this,[e]);return n?(i.then(t,n),Promise.resolve()):i},t.addIceCandidate=a}function Fi(e){const t=e&&e.navigator;if(t.mediaDevices&&t.mediaDevices.getUserMedia){const e=t.mediaDevices,n=e.getUserMedia.bind(e);t.mediaDevices.getUserMedia=e=>n(Bi(e))}!t.getUserMedia&&t.mediaDevices&&t.mediaDevices.getUserMedia&&(t.getUserMedia=function(e,n,i){t.mediaDevices.getUserMedia(e).then(n,i)}.bind(t))}function Bi(e){return e&&void 0!==e.video?Object.assign({},e,{video:li(e.video)}):e}function Vi(e){if(!e.RTCPeerConnection)return;const t=e.RTCPeerConnection;e.RTCPeerConnection=function(e,n){if(e&&e.iceServers){const t=[];for(let n=0;n<e.iceServers.length;n++){let i=e.iceServers[n];void 0===i.urls&&i.url?(ci("RTCIceServer.url","RTCIceServer.urls"),i=JSON.parse(JSON.stringify(i)),i.urls=i.url,delete i.url,t.push(i)):t.push(e.iceServers[n])}e.iceServers=t}return new t(e,n)},e.RTCPeerConnection.prototype=t.prototype,"generateCertificate"in t&&Object.defineProperty(e.RTCPeerConnection,"generateCertificate",{get:()=>t.generateCertificate})}function qi(e){"object"==typeof e&&e.RTCTrackEvent&&"receiver"in e.RTCTrackEvent.prototype&&!("transceiver"in e.RTCTrackEvent.prototype)&&Object.defineProperty(e.RTCTrackEvent.prototype,"transceiver",{get(){return{receiver:this.receiver}}})}function Ki(e){const t=e.RTCPeerConnection.prototype.createOffer;e.RTCPeerConnection.prototype.createOffer=function(e){if(e){void 0!==e.offerToReceiveAudio&&(e.offerToReceiveAudio=!!e.offerToReceiveAudio);const t=this.getTransceivers().find((e=>"audio"===e.receiver.track.kind));!1===e.offerToReceiveAudio&&t?"sendrecv"===t.direction?t.setDirection?t.setDirection("sendonly"):t.direction="sendonly":"recvonly"===t.direction&&(t.setDirection?t.setDirection("inactive"):t.direction="inactive"):!0!==e.offerToReceiveAudio||t||this.addTransceiver("audio",{direction:"recvonly"}),void 0!==e.offerToReceiveVideo&&(e.offerToReceiveVideo=!!e.offerToReceiveVideo);const n=this.getTransceivers().find((e=>"video"===e.receiver.track.kind));!1===e.offerToReceiveVideo&&n?"sendrecv"===n.direction?n.setDirection?n.setDirection("sendonly"):n.direction="sendonly":"recvonly"===n.direction&&(n.setDirection?n.setDirection("inactive"):n.direction="inactive"):!0!==e.offerToReceiveVideo||n||this.addTransceiver("video",{direction:"recvonly"})}return t.apply(this,arguments)}}function Wi(e){"object"!=typeof e||e.AudioContext||(e.AudioContext=e.webkitAudioContext)}var Hi,Gi=Object.freeze({__proto__:null,shimAudioContext:Wi,shimCallbacksAPI:ji,shimConstraints:Bi,shimCreateOfferLegacy:Ki,shimGetUserMedia:Fi,shimLocalStreamsAPI:Li,shimRTCIceServerUrls:Vi,shimRemoteStreamsAPI:Ui,shimTrackEventTransceiver:qi}),zi={exports:{}};var Ji=(Hi||(Hi=1,function(e){const t={generateIdentifier:function(){return Math.random().toString(36).substring(2,12)}};t.localCName=t.generateIdentifier(),t.splitLines=function(e){return e.trim().split("\n").map((e=>e.trim()))},t.splitSections=function(e){return e.split("\nm=").map(((e,t)=>(t>0?"m="+e:e).trim()+"\r\n"))},t.getDescription=function(e){const n=t.splitSections(e);return n&&n[0]},t.getMediaSections=function(e){const n=t.splitSections(e);return n.shift(),n},t.matchPrefix=function(e,n){return t.splitLines(e).filter((e=>0===e.indexOf(n)))},t.parseCandidate=function(e){let t;t=0===e.indexOf("a=candidate:")?e.substring(12).split(" "):e.substring(10).split(" ");const n={foundation:t[0],component:{1:"rtp",2:"rtcp"}[t[1]]||t[1],protocol:t[2].toLowerCase(),priority:parseInt(t[3],10),ip:t[4],address:t[4],port:parseInt(t[5],10),type:t[7]};for(let e=8;e<t.length;e+=2)switch(t[e]){case"raddr":n.relatedAddress=t[e+1];break;case"rport":n.relatedPort=parseInt(t[e+1],10);break;case"tcptype":n.tcpType=t[e+1];break;case"ufrag":n.ufrag=t[e+1],n.usernameFragment=t[e+1];break;default:void 0===n[t[e]]&&(n[t[e]]=t[e+1])}return n},t.writeCandidate=function(e){const t=[];t.push(e.foundation);const n=e.component;"rtp"===n?t.push(1):"rtcp"===n?t.push(2):t.push(n),t.push(e.protocol.toUpperCase()),t.push(e.priority),t.push(e.address||e.ip),t.push(e.port);const i=e.type;return t.push("typ"),t.push(i),"host"!==i&&e.relatedAddress&&e.relatedPort&&(t.push("raddr"),t.push(e.relatedAddress),t.push("rport"),t.push(e.relatedPort)),e.tcpType&&"tcp"===e.protocol.toLowerCase()&&(t.push("tcptype"),t.push(e.tcpType)),(e.usernameFragment||e.ufrag)&&(t.push("ufrag"),t.push(e.usernameFragment||e.ufrag)),"candidate:"+t.join(" ")},t.parseIceOptions=function(e){return e.substring(14).split(" ")},t.parseRtpMap=function(e){let t=e.substring(9).split(" ");const n={payloadType:parseInt(t.shift(),10)};return t=t[0].split("/"),n.name=t[0],n.clockRate=parseInt(t[1],10),n.channels=3===t.length?parseInt(t[2],10):1,n.numChannels=n.channels,n},t.writeRtpMap=function(e){let t=e.payloadType;void 0!==e.preferredPayloadType&&(t=e.preferredPayloadType);const n=e.channels||e.numChannels||1;return"a=rtpmap:"+t+" "+e.name+"/"+e.clockRate+(1!==n?"/"+n:"")+"\r\n"},t.parseExtmap=function(e){const t=e.substring(9).split(" ");return{id:parseInt(t[0],10),direction:t[0].indexOf("/")>0?t[0].split("/")[1]:"sendrecv",uri:t[1],attributes:t.slice(2).join(" ")}},t.writeExtmap=function(e){return"a=extmap:"+(e.id||e.preferredId)+(e.direction&&"sendrecv"!==e.direction?"/"+e.direction:"")+" "+e.uri+(e.attributes?" "+e.attributes:"")+"\r\n"},t.parseFmtp=function(e){const t={};let n;const i=e.substring(e.indexOf(" ")+1).split(";");for(let e=0;e<i.length;e++)n=i[e].trim().split("="),t[n[0].trim()]=n[1];return t},t.writeFmtp=function(e){let t="",n=e.payloadType;if(void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.parameters&&Object.keys(e.parameters).length){const i=[];Object.keys(e.parameters).forEach((t=>{void 0!==e.parameters[t]?i.push(t+"="+e.parameters[t]):i.push(t)})),t+="a=fmtp:"+n+" "+i.join(";")+"\r\n"}return t},t.parseRtcpFb=function(e){const t=e.substring(e.indexOf(" ")+1).split(" ");return{type:t.shift(),parameter:t.join(" ")}},t.writeRtcpFb=function(e){let t="",n=e.payloadType;return void 0!==e.preferredPayloadType&&(n=e.preferredPayloadType),e.rtcpFeedback&&e.rtcpFeedback.length&&e.rtcpFeedback.forEach((e=>{t+="a=rtcp-fb:"+n+" "+e.type+(e.parameter&&e.parameter.length?" "+e.parameter:"")+"\r\n"})),t},t.parseSsrcMedia=function(e){const t=e.indexOf(" "),n={ssrc:parseInt(e.substring(7,t),10)},i=e.indexOf(":",t);return i>-1?(n.attribute=e.substring(t+1,i),n.value=e.substring(i+1)):n.attribute=e.substring(t+1),n},t.parseSsrcGroup=function(e){const t=e.substring(13).split(" ");return{semantics:t.shift(),ssrcs:t.map((e=>parseInt(e,10)))}},t.getMid=function(e){const n=t.matchPrefix(e,"a=mid:")[0];if(n)return n.substring(6)},t.parseFingerprint=function(e){const t=e.substring(14).split(" ");return{algorithm:t[0].toLowerCase(),value:t[1].toUpperCase()}},t.getDtlsParameters=function(e,n){return{role:"auto",fingerprints:t.matchPrefix(e+n,"a=fingerprint:").map(t.parseFingerprint)}},t.writeDtlsParameters=function(e,t){let n="a=setup:"+t+"\r\n";return e.fingerprints.forEach((e=>{n+="a=fingerprint:"+e.algorithm+" "+e.value+"\r\n"})),n},t.parseCryptoLine=function(e){const t=e.substring(9).split(" ");return{tag:parseInt(t[0],10),cryptoSuite:t[1],keyParams:t[2],sessionParams:t.slice(3)}},t.writeCryptoLine=function(e){return"a=crypto:"+e.tag+" "+e.cryptoSuite+" "+("object"==typeof e.keyParams?t.writeCryptoKeyParams(e.keyParams):e.keyParams)+(e.sessionParams?" "+e.sessionParams.join(" "):"")+"\r\n"},t.parseCryptoKeyParams=function(e){if(0!==e.indexOf("inline:"))return null;const t=e.substring(7).split("|");return{keyMethod:"inline",keySalt:t[0],lifeTime:t[1],mkiValue:t[2]?t[2].split(":")[0]:void 0,mkiLength:t[2]?t[2].split(":")[1]:void 0}},t.writeCryptoKeyParams=function(e){return e.keyMethod+":"+e.keySalt+(e.lifeTime?"|"+e.lifeTime:"")+(e.mkiValue&&e.mkiLength?"|"+e.mkiValue+":"+e.mkiLength:"")},t.getCryptoParameters=function(e,n){return t.matchPrefix(e+n,"a=crypto:").map(t.parseCryptoLine)},t.getIceParameters=function(e,n){const i=t.matchPrefix(e+n,"a=ice-ufrag:")[0],s=t.matchPrefix(e+n,"a=ice-pwd:")[0];return i&&s?{usernameFragment:i.substring(12),password:s.substring(10)}:null},t.writeIceParameters=function(e){let t="a=ice-ufrag:"+e.usernameFragment+"\r\na=ice-pwd:"+e.password+"\r\n";return e.iceLite&&(t+="a=ice-lite\r\n"),t},t.parseRtpParameters=function(e){const n={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]},i=t.splitLines(e)[0].split(" ");n.profile=i[2];for(let s=3;s<i.length;s++){const o=i[s],r=t.matchPrefix(e,"a=rtpmap:"+o+" ")[0];if(r){const i=t.parseRtpMap(r),s=t.matchPrefix(e,"a=fmtp:"+o+" ");switch(i.parameters=s.length?t.parseFmtp(s[0]):{},i.rtcpFeedback=t.matchPrefix(e,"a=rtcp-fb:"+o+" ").map(t.parseRtcpFb),n.codecs.push(i),i.name.toUpperCase()){case"RED":case"ULPFEC":n.fecMechanisms.push(i.name.toUpperCase())}}}t.matchPrefix(e,"a=extmap:").forEach((e=>{n.headerExtensions.push(t.parseExtmap(e))}));const s=t.matchPrefix(e,"a=rtcp-fb:* ").map(t.parseRtcpFb);return n.codecs.forEach((e=>{s.forEach((t=>{e.rtcpFeedback.find((e=>e.type===t.type&&e.parameter===t.parameter))||e.rtcpFeedback.push(t)}))})),n},t.writeRtpDescription=function(e,n){let i="";i+="m="+e+" ",i+=n.codecs.length>0?"9":"0",i+=" "+(n.profile||"UDP/TLS/RTP/SAVPF")+" ",i+=n.codecs.map((e=>void 0!==e.preferredPayloadType?e.preferredPayloadType:e.payloadType)).join(" ")+"\r\n",i+="c=IN IP4 0.0.0.0\r\n",i+="a=rtcp:9 IN IP4 0.0.0.0\r\n",n.codecs.forEach((e=>{i+=t.writeRtpMap(e),i+=t.writeFmtp(e),i+=t.writeRtcpFb(e)}));let s=0;return n.codecs.forEach((e=>{e.maxptime>s&&(s=e.maxptime)})),s>0&&(i+="a=maxptime:"+s+"\r\n"),n.headerExtensions&&n.headerExtensions.forEach((e=>{i+=t.writeExtmap(e)})),i},t.parseRtpEncodingParameters=function(e){const n=[],i=t.parseRtpParameters(e),s=-1!==i.fecMechanisms.indexOf("RED"),o=-1!==i.fecMechanisms.indexOf("ULPFEC"),r=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute)),a=r.length>0&&r[0].ssrc;let c;const d=t.matchPrefix(e,"a=ssrc-group:FID").map((e=>e.substring(17).split(" ").map((e=>parseInt(e,10)))));d.length>0&&d[0].length>1&&d[0][0]===a&&(c=d[0][1]),i.codecs.forEach((e=>{if("RTX"===e.name.toUpperCase()&&e.parameters.apt){let t={ssrc:a,codecPayloadType:parseInt(e.parameters.apt,10)};a&&c&&(t.rtx={ssrc:c}),n.push(t),s&&(t=JSON.parse(JSON.stringify(t)),t.fec={ssrc:a,mechanism:o?"red+ulpfec":"red"},n.push(t))}})),0===n.length&&a&&n.push({ssrc:a});let l=t.matchPrefix(e,"b=");return l.length&&(l=0===l[0].indexOf("b=TIAS:")?parseInt(l[0].substring(7),10):0===l[0].indexOf("b=AS:")?1e3*parseInt(l[0].substring(5),10)*.95-16e3:void 0,n.forEach((e=>{e.maxBitrate=l}))),n},t.parseRtcpParameters=function(e){const n={},i=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"cname"===e.attribute))[0];i&&(n.cname=i.value,n.ssrc=i.ssrc);const s=t.matchPrefix(e,"a=rtcp-rsize");n.reducedSize=s.length>0,n.compound=0===s.length;const o=t.matchPrefix(e,"a=rtcp-mux");return n.mux=o.length>0,n},t.writeRtcpParameters=function(e){let t="";return e.reducedSize&&(t+="a=rtcp-rsize\r\n"),e.mux&&(t+="a=rtcp-mux\r\n"),void 0!==e.ssrc&&e.cname&&(t+="a=ssrc:"+e.ssrc+" cname:"+e.cname+"\r\n"),t},t.parseMsid=function(e){let n;const i=t.matchPrefix(e,"a=msid:");if(1===i.length)return n=i[0].substring(7).split(" "),{stream:n[0],track:n[1]};const s=t.matchPrefix(e,"a=ssrc:").map((e=>t.parseSsrcMedia(e))).filter((e=>"msid"===e.attribute));return s.length>0?(n=s[0].value.split(" "),{stream:n[0],track:n[1]}):void 0},t.parseSctpDescription=function(e){const n=t.parseMLine(e),i=t.matchPrefix(e,"a=max-message-size:");let s;i.length>0&&(s=parseInt(i[0].substring(19),10)),isNaN(s)&&(s=65536);const o=t.matchPrefix(e,"a=sctp-port:");if(o.length>0)return{port:parseInt(o[0].substring(12),10),protocol:n.fmt,maxMessageSize:s};const r=t.matchPrefix(e,"a=sctpmap:");if(r.length>0){const e=r[0].substring(10).split(" ");return{port:parseInt(e[0],10),protocol:e[1],maxMessageSize:s}}},t.writeSctpDescription=function(e,t){let n=[];return n="DTLS/SCTP"!==e.protocol?["m="+e.kind+" 9 "+e.protocol+" "+t.protocol+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctp-port:"+t.port+"\r\n"]:["m="+e.kind+" 9 "+e.protocol+" "+t.port+"\r\n","c=IN IP4 0.0.0.0\r\n","a=sctpmap:"+t.port+" "+t.protocol+" 65535\r\n"],void 0!==t.maxMessageSize&&n.push("a=max-message-size:"+t.maxMessageSize+"\r\n"),n.join("")},t.generateSessionId=function(){return Math.random().toString().substr(2,22)},t.writeSessionBoilerplate=function(e,n,i){let s;const o=void 0!==n?n:2;return s=e||t.generateSessionId(),"v=0\r\no="+(i||"thisisadapterortc")+" "+s+" "+o+" IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\n"},t.getDirection=function(e,n){const i=t.splitLines(e);for(let e=0;e<i.length;e++)switch(i[e]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return i[e].substring(2)}return n?t.getDirection(n):"sendrecv"},t.getKind=function(e){return t.splitLines(e)[0].split(" ")[0].substring(2)},t.isRejected=function(e){return"0"===e.split(" ",2)[1]},t.parseMLine=function(e){const n=t.splitLines(e)[0].substring(2).split(" ");return{kind:n[0],port:parseInt(n[1],10),protocol:n[2],fmt:n.slice(3).join(" ")}},t.parseOLine=function(e){const n=t.matchPrefix(e,"o=")[0].substring(2).split(" ");return{username:n[0],sessionId:n[1],sessionVersion:parseInt(n[2],10),netType:n[3],addressType:n[4],address:n[5]}},t.isValidSDP=function(e){if("string"!=typeof e||0===e.length)return!1;const n=t.splitLines(e);for(let e=0;e<n.length;e++)if(n[e].length<2||"="!==n[e].charAt(1))return!1;return!0},e.exports=t}(zi)),zi.exports),Qi=An(Ji),Yi=t({__proto__:null,default:Qi},[Ji]);function Xi(e){if(!e.RTCIceCandidate||e.RTCIceCandidate&&"foundation"in e.RTCIceCandidate.prototype)return;const t=e.RTCIceCandidate;e.RTCIceCandidate=function(e){if("object"==typeof e&&e.candidate&&0===e.candidate.indexOf("a=")&&((e=JSON.parse(JSON.stringify(e))).candidate=e.candidate.substring(2)),e.candidate&&e.candidate.length){const n=new t(e),i=Qi.parseCandidate(e.candidate);for(const e in i)e in n||Object.defineProperty(n,e,{value:i[e]});return n.toJSON=function(){return{candidate:n.candidate,sdpMid:n.sdpMid,sdpMLineIndex:n.sdpMLineIndex,usernameFragment:n.usernameFragment}},n}return new t(e)},e.RTCIceCandidate.prototype=t.prototype,si(e,"icecandidate",(t=>(t.candidate&&Object.defineProperty(t,"candidate",{value:new e.RTCIceCandidate(t.candidate),writable:"false"}),t)))}function Zi(e){!e.RTCIceCandidate||e.RTCIceCandidate&&"relayProtocol"in e.RTCIceCandidate.prototype||si(e,"icecandidate",(e=>{if(e.candidate){const t=Qi.parseCandidate(e.candidate.candidate);"relay"===t.type&&(e.candidate.relayProtocol={0:"tls",1:"tcp",2:"udp"}[t.priority>>24])}return e}))}function $i(e,t){if(!e.RTCPeerConnection)return;"sctp"in e.RTCPeerConnection.prototype||Object.defineProperty(e.RTCPeerConnection.prototype,"sctp",{get(){return void 0===this._sctp?null:this._sctp}});const n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(){if(this._sctp=null,"chrome"===t.browser&&t.version>=76){const{sdpSemantics:e}=this.getConfiguration();"plan-b"===e&&Object.defineProperty(this,"sctp",{get(){return void 0===this._sctp?null:this._sctp},enumerable:!0,configurable:!0})}if(function(e){if(!e||!e.sdp)return!1;const t=Qi.splitSections(e.sdp);return t.shift(),t.some((e=>{const t=Qi.parseMLine(e);return t&&"application"===t.kind&&-1!==t.protocol.indexOf("SCTP")}))}(arguments[0])){const e=function(e){const t=e.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(null===t||t.length<2)return-1;const n=parseInt(t[1],10);return n!=n?-1:n}(arguments[0]),n=function(e){let n=65536;return"firefox"===t.browser&&(n=t.version<57?-1===e?16384:2147483637:t.version<60?57===t.version?65535:65536:2147483637),n}(e),i=function(e,n){let i=65536;"firefox"===t.browser&&57===t.version&&(i=65535);const s=Qi.matchPrefix(e.sdp,"a=max-message-size:");return s.length>0?i=parseInt(s[0].substring(19),10):"firefox"===t.browser&&-1!==n&&(i=2147483637),i}(arguments[0],e);let s;s=0===n&&0===i?Number.POSITIVE_INFINITY:0===n||0===i?Math.max(n,i):Math.min(n,i);const o={};Object.defineProperty(o,"maxMessageSize",{get:()=>s}),this._sctp=o}return n.apply(this,arguments)}}function es(e){if(!e.RTCPeerConnection||!("createDataChannel"in e.RTCPeerConnection.prototype))return;function t(e,t){const n=e.send;e.send=function(){const i=arguments[0],s=i.length||i.size||i.byteLength;if("open"===e.readyState&&t.sctp&&s>t.sctp.maxMessageSize)throw new TypeError("Message too large (can send a maximum of "+t.sctp.maxMessageSize+" bytes)");return n.apply(e,arguments)}}const n=e.RTCPeerConnection.prototype.createDataChannel;e.RTCPeerConnection.prototype.createDataChannel=function(){const e=n.apply(this,arguments);return t(e,this),e},si(e,"datachannel",(e=>(t(e.channel,e.target),e)))}function ts(e){if(!e.RTCPeerConnection||"connectionState"in e.RTCPeerConnection.prototype)return;const t=e.RTCPeerConnection.prototype;Object.defineProperty(t,"connectionState",{get(){return{completed:"connected",checking:"connecting"}[this.iceConnectionState]||this.iceConnectionState},enumerable:!0,configurable:!0}),Object.defineProperty(t,"onconnectionstatechange",{get(){return this._onconnectionstatechange||null},set(e){this._onconnectionstatechange&&(this.removeEventListener("connectionstatechange",this._onconnectionstatechange),delete this._onconnectionstatechange),e&&this.addEventListener("connectionstatechange",this._onconnectionstatechange=e)},enumerable:!0,configurable:!0}),["setLocalDescription","setRemoteDescription"].forEach((e=>{const n=t[e];t[e]=function(){return this._connectionstatechangepoly||(this._connectionstatechangepoly=e=>{const t=e.target;if(t._lastConnectionState!==t.connectionState){t._lastConnectionState=t.connectionState;const n=new Event("connectionstatechange",e);t.dispatchEvent(n)}return e},this.addEventListener("iceconnectionstatechange",this._connectionstatechangepoly)),n.apply(this,arguments)}}))}function ns(e,t){if(!e.RTCPeerConnection)return;if("chrome"===t.browser&&t.version>=71)return;if("safari"===t.browser&&t.version>=605)return;const n=e.RTCPeerConnection.prototype.setRemoteDescription;e.RTCPeerConnection.prototype.setRemoteDescription=function(t){if(t&&t.sdp&&-1!==t.sdp.indexOf("\na=extmap-allow-mixed")){const n=t.sdp.split("\n").filter((e=>"a=extmap-allow-mixed"!==e.trim())).join("\n");e.RTCSessionDescription&&t instanceof e.RTCSessionDescription?arguments[0]=new e.RTCSessionDescription({type:t.type,sdp:n}):t.sdp=n}return n.apply(this,arguments)}}function is(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const n=e.RTCPeerConnection.prototype.addIceCandidate;n&&0!==n.length&&(e.RTCPeerConnection.prototype.addIceCandidate=function(){return arguments[0]?("chrome"===t.browser&&t.version<78||"firefox"===t.browser&&t.version<68||"safari"===t.browser)&&arguments[0]&&""===arguments[0].candidate?Promise.resolve():n.apply(this,arguments):(arguments[1]&&arguments[1].apply(null),Promise.resolve())})}function ss(e,t){if(!e.RTCPeerConnection||!e.RTCPeerConnection.prototype)return;const n=e.RTCPeerConnection.prototype.setLocalDescription;n&&0!==n.length&&(e.RTCPeerConnection.prototype.setLocalDescription=function(){let e=arguments[0]||{};if("object"!=typeof e||e.type&&e.sdp)return n.apply(this,arguments);if(e={type:e.type,sdp:e.sdp},!e.type)switch(this.signalingState){case"stable":case"have-local-offer":case"have-remote-pranswer":e.type="offer";break;default:e.type="answer"}if(e.sdp||"offer"!==e.type&&"answer"!==e.type)return n.apply(this,[e]);return("offer"===e.type?this.createOffer:this.createAnswer).apply(this).then((e=>n.apply(this,[e])))})}var os=Object.freeze({__proto__:null,removeExtmapAllowMixed:ns,shimAddIceCandidateNullOrEmpty:is,shimConnectionState:ts,shimMaxMessageSize:$i,shimParameterlessSetLocalDescription:ss,shimRTCIceCandidate:Xi,shimRTCIceCandidateRelayProtocol:Zi,shimSendThrowTypeError:es});!function(){let{window:e}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{shimChrome:!0,shimFirefox:!0,shimSafari:!0};const n=ai,i=function(e){const t={browser:null,version:null};if(void 0===e||!e.navigator||!e.navigator.userAgent)return t.browser="Not a browser.",t;const{navigator:n}=e;if(n.userAgentData&&n.userAgentData.brands){const e=n.userAgentData.brands.find((e=>"Chromium"===e.brand));if(e)return{browser:"chrome",version:parseInt(e.version,10)}}if(n.mozGetUserMedia)t.browser="firefox",t.version=ii(n.userAgent,/Firefox\/(\d+)\./,1);else if(n.webkitGetUserMedia||!1===e.isSecureContext&&e.webkitRTCPeerConnection)t.browser="chrome",t.version=ii(n.userAgent,/Chrom(e|ium)\/(\d+)\./,2);else{if(!e.RTCPeerConnection||!n.userAgent.match(/AppleWebKit\/(\d+)\./))return t.browser="Not a supported browser.",t;t.browser="safari",t.version=ii(n.userAgent,/AppleWebKit\/(\d+)\./,1),t.supportsUnifiedPlan=e.RTCRtpTransceiver&&"currentDirection"in e.RTCRtpTransceiver.prototype}return t}(e),s={browserDetails:i,commonShim:os,extractVersion:ii,disableLog:oi,disableWarnings:ri,sdp:Yi};switch(i.browser){case"chrome":if(!Si||!Ti||!t.shimChrome)return n("Chrome shim is not included in this adapter release."),s;if(null===i.version)return n("Chrome shim can not determine version, not shimming."),s;n("adapter.js shimming chrome."),s.browserShim=Si,is(e,i),ss(e),mi(e,i),gi(e),Ti(e,i),vi(e),yi(e,i),fi(e),ki(e),Ci(e,i),Xi(e),Zi(e),ts(e),$i(e,i),es(e),ns(e,i);break;case"firefox":if(!Ai||!Pi||!t.shimFirefox)return n("Firefox shim is not included in this adapter release."),s;n("adapter.js shimming firefox."),s.browserShim=Ai,is(e,i),ss(e),Ei(e,i),Pi(e,i),wi(e),Oi(e),Ri(e),Ii(e),Di(e),xi(e),Mi(e),Ni(e),_i(e),Xi(e),ts(e),$i(e,i),es(e);break;case"safari":if(!Gi||!t.shimSafari)return n("Safari shim is not included in this adapter release."),s;n("adapter.js shimming safari."),s.browserShim=Gi,is(e,i),ss(e),Vi(e),Ki(e),ji(e),Li(e),Ui(e),qi(e),Fi(e),Wi(e),Xi(e),Zi(e),$i(e,i),es(e),ns(e,i);break;default:n("Unsupported browser!")}}({window:"undefined"==typeof window?void 0:window});const rs="AES-GCM",as="lk_e2ee",cs={sharedKey:!1,ratchetSalt:"LKFrameEncryptionKey",ratchetWindowSize:8,failureTolerance:10,keyringSize:16};var ds,ls;function us(){return ps()||hs()}function hs(){return void 0!==window.RTCRtpScriptTransform}function ps(){return void 0!==window.RTCRtpSender&&void 0!==window.RTCRtpSender.prototype.createEncodedStreams}function ms(e){return Qn(this,void 0,void 0,(function*(){let t=new TextEncoder;return yield crypto.subtle.importKey("raw",t.encode(e),{name:"PBKDF2"},!1,["deriveBits","deriveKey"])}))}function gs(e){return Qn(this,void 0,void 0,(function*(){return yield crypto.subtle.importKey("raw",e,"HKDF",!1,["deriveBits","deriveKey"])}))}function vs(e,t){const n=(new TextEncoder).encode(t);switch(e){case"HKDF":return{name:"HKDF",salt:n,hash:"SHA-256",info:new ArrayBuffer(128)};case"PBKDF2":return{name:"PBKDF2",salt:n,hash:"SHA-256",iterations:1e5};default:throw new Error("algorithm ".concat(e," is currently unsupported"))}}e.KeyProviderEvent=void 0,(ds=e.KeyProviderEvent||(e.KeyProviderEvent={})).SetKey="setKey",ds.RatchetRequest="ratchetRequest",ds.KeyRatcheted="keyRatcheted",e.KeyHandlerEvent=void 0,(e.KeyHandlerEvent||(e.KeyHandlerEvent={})).KeyRatcheted="keyRatcheted",e.EncryptionEvent=void 0,(ls=e.EncryptionEvent||(e.EncryptionEvent={})).ParticipantEncryptionStatusChanged="participantEncryptionStatusChanged",ls.EncryptionError="encryptionError",e.CryptorEvent=void 0,(e.CryptorEvent||(e.CryptorEvent={})).Error="cryptorError";class fs extends ei.EventEmitter{constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super(),this.onKeyRatcheted=(e,t)=>{qn.debug("key ratcheted event received",{material:e,keyIndex:t})},this.keyInfoMap=new Map,this.options=Object.assign(Object.assign({},cs),t),this.on(e.KeyProviderEvent.KeyRatcheted,this.onKeyRatcheted)}onSetEncryptionKey(t,n,i){const s={key:t,participantIdentity:n,keyIndex:i};if(!this.options.sharedKey&&!n)throw new Error("participant identity needs to be passed for encryption key if sharedKey option is false");this.keyInfoMap.set("".concat(null!=n?n:"shared","-").concat(null!=i?i:0),s),this.emit(e.KeyProviderEvent.SetKey,s)}getKeys(){return Array.from(this.keyInfoMap.values())}getOptions(){return this.options}ratchetKey(t,n){this.emit(e.KeyProviderEvent.RatchetRequest,t,n)}}class ks extends Error{constructor(e,t){super(t||"an error has occured"),this.name="LiveKitError",this.code=e}}var bs,ys,Ts,Cs,Ss,Es,ws;e.ConnectionErrorReason=void 0,(bs=e.ConnectionErrorReason||(e.ConnectionErrorReason={}))[bs.NotAllowed=0]="NotAllowed",bs[bs.ServerUnreachable=1]="ServerUnreachable",bs[bs.InternalError=2]="InternalError",bs[bs.Cancelled=3]="Cancelled",bs[bs.LeaveRequest=4]="LeaveRequest";class Ps extends ks{constructor(t,n,i,s){super(1,t),this.name="ConnectionError",this.status=i,this.reason=n,this.context=s,this.reasonName=e.ConnectionErrorReason[n]}}class Rs extends ks{constructor(e){super(21,null!=e?e:"device is unsupported"),this.name="DeviceUnsupportedError"}}class Is extends ks{constructor(e){super(20,null!=e?e:"track is invalid"),this.name="TrackInvalidError"}}class Os extends ks{constructor(e){super(10,null!=e?e:"unsupported server"),this.name="UnsupportedServer"}}class Ds extends ks{constructor(e){super(12,null!=e?e:"unexpected connection state"),this.name="UnexpectedConnectionState"}}class xs extends ks{constructor(e){super(13,null!=e?e:"unable to negotiate"),this.name="NegotiationError"}}class Ms extends ks{constructor(e,t){super(15,e),this.name="PublishTrackError",this.status=t}}class Ns extends ks{constructor(e,t){super(15,e),this.reason=t,this.reasonName="string"==typeof t?t:Nn[t]}}e.MediaDeviceFailure=void 0,(ys=e.MediaDeviceFailure||(e.MediaDeviceFailure={})).PermissionDenied="PermissionDenied",ys.NotFound="NotFound",ys.DeviceInUse="DeviceInUse",ys.Other="Other",function(e){e.getFailure=function(t){if(t&&"name"in t)return"NotFoundError"===t.name||"DevicesNotFoundError"===t.name?e.NotFound:"NotAllowedError"===t.name||"PermissionDeniedError"===t.name?e.PermissionDenied:"NotReadableError"===t.name||"TrackStartError"===t.name?e.DeviceInUse:e.Other}}(e.MediaDeviceFailure||(e.MediaDeviceFailure={})),e.CryptorErrorReason=void 0,(Ts=e.CryptorErrorReason||(e.CryptorErrorReason={}))[Ts.InvalidKey=0]="InvalidKey",Ts[Ts.MissingKey=1]="MissingKey",Ts[Ts.InternalError=2]="InternalError";e.RoomEvent=void 0,(Cs=e.RoomEvent||(e.RoomEvent={})).Connected="connected",Cs.Reconnecting="reconnecting",Cs.SignalReconnecting="signalReconnecting",Cs.Reconnected="reconnected",Cs.Disconnected="disconnected",Cs.ConnectionStateChanged="connectionStateChanged",Cs.MediaDevicesChanged="mediaDevicesChanged",Cs.ParticipantConnected="participantConnected",Cs.ParticipantDisconnected="participantDisconnected",Cs.TrackPublished="trackPublished",Cs.TrackSubscribed="trackSubscribed",Cs.TrackSubscriptionFailed="trackSubscriptionFailed",Cs.TrackUnpublished="trackUnpublished",Cs.TrackUnsubscribed="trackUnsubscribed",Cs.TrackMuted="trackMuted",Cs.TrackUnmuted="trackUnmuted",Cs.LocalTrackPublished="localTrackPublished",Cs.LocalTrackUnpublished="localTrackUnpublished",Cs.LocalAudioSilenceDetected="localAudioSilenceDetected",Cs.ActiveSpeakersChanged="activeSpeakersChanged",Cs.ParticipantMetadataChanged="participantMetadataChanged",Cs.ParticipantNameChanged="participantNameChanged",Cs.ParticipantAttributesChanged="participantAttributesChanged",Cs.RoomMetadataChanged="roomMetadataChanged",Cs.DataReceived="dataReceived",Cs.SipDTMFReceived="sipDTMFReceived",Cs.TranscriptionReceived="transcriptionReceived",Cs.ConnectionQualityChanged="connectionQualityChanged",Cs.TrackStreamStateChanged="trackStreamStateChanged",Cs.TrackSubscriptionPermissionChanged="trackSubscriptionPermissionChanged",Cs.TrackSubscriptionStatusChanged="trackSubscriptionStatusChanged",Cs.AudioPlaybackStatusChanged="audioPlaybackChanged",Cs.VideoPlaybackStatusChanged="videoPlaybackChanged",Cs.MediaDevicesError="mediaDevicesError",Cs.ParticipantPermissionsChanged="participantPermissionsChanged",Cs.SignalConnected="signalConnected",Cs.RecordingStatusChanged="recordingStatusChanged",Cs.ParticipantEncryptionStatusChanged="participantEncryptionStatusChanged",Cs.EncryptionError="encryptionError",Cs.DCBufferStatusChanged="dcBufferStatusChanged",Cs.ActiveDeviceChanged="activeDeviceChanged",Cs.ChatMessage="chatMessage",Cs.LocalTrackSubscribed="localTrackSubscribed",Cs.MetricsReceived="metricsReceived",e.ParticipantEvent=void 0,(Ss=e.ParticipantEvent||(e.ParticipantEvent={})).TrackPublished="trackPublished",Ss.TrackSubscribed="trackSubscribed",Ss.TrackSubscriptionFailed="trackSubscriptionFailed",Ss.TrackUnpublished="trackUnpublished",Ss.TrackUnsubscribed="trackUnsubscribed",Ss.TrackMuted="trackMuted",Ss.TrackUnmuted="trackUnmuted",Ss.LocalTrackPublished="localTrackPublished",Ss.LocalTrackUnpublished="localTrackUnpublished",Ss.ParticipantMetadataChanged="participantMetadataChanged",Ss.ParticipantNameChanged="participantNameChanged",Ss.DataReceived="dataReceived",Ss.SipDTMFReceived="sipDTMFReceived",Ss.TranscriptionReceived="transcriptionReceived",Ss.IsSpeakingChanged="isSpeakingChanged",Ss.ConnectionQualityChanged="connectionQualityChanged",Ss.TrackStreamStateChanged="trackStreamStateChanged",Ss.TrackSubscriptionPermissionChanged="trackSubscriptionPermissionChanged",Ss.TrackSubscriptionStatusChanged="trackSubscriptionStatusChanged",Ss.MediaDevicesError="mediaDevicesError",Ss.AudioStreamAcquired="audioStreamAcquired",Ss.ParticipantPermissionsChanged="participantPermissionsChanged",Ss.PCTrackAdded="pcTrackAdded",Ss.AttributesChanged="attributesChanged",Ss.LocalTrackSubscribed="localTrackSubscribed",Ss.ChatMessage="chatMessage",e.EngineEvent=void 0,(Es=e.EngineEvent||(e.EngineEvent={})).TransportsCreated="transportsCreated",Es.Connected="connected",Es.Disconnected="disconnected",Es.Resuming="resuming",Es.Resumed="resumed",Es.Restarting="restarting",Es.Restarted="restarted",Es.SignalResumed="signalResumed",Es.SignalRestarted="signalRestarted",Es.Closing="closing",Es.MediaTrackAdded="mediaTrackAdded",Es.ActiveSpeakersUpdate="activeSpeakersUpdate",Es.DataPacketReceived="dataPacketReceived",Es.RTPVideoMapUpdate="rtpVideoMapUpdate",Es.DCBufferStatusChanged="dcBufferStatusChanged",Es.ParticipantUpdate="participantUpdate",Es.RoomUpdate="roomUpdate",Es.SpeakersChanged="speakersChanged",Es.StreamStateChanged="streamStateChanged",Es.ConnectionQualityUpdate="connectionQualityUpdate",Es.SubscriptionError="subscriptionError",Es.SubscriptionPermissionUpdate="subscriptionPermissionUpdate",Es.RemoteMute="remoteMute",Es.SubscribedQualityUpdate="subscribedQualityUpdate",Es.LocalTrackUnpublished="localTrackUnpublished",Es.LocalTrackSubscribed="localTrackSubscribed",Es.Offline="offline",Es.SignalRequestResponse="signalRequestResponse",e.TrackEvent=void 0,(ws=e.TrackEvent||(e.TrackEvent={})).Message="message",ws.Muted="muted",ws.Unmuted="unmuted",ws.Restarted="restarted",ws.Ended="ended",ws.Subscribed="subscribed",ws.Unsubscribed="unsubscribed",ws.UpdateSettings="updateSettings",ws.UpdateSubscription="updateSubscription",ws.AudioPlaybackStarted="audioPlaybackStarted",ws.AudioPlaybackFailed="audioPlaybackFailed",ws.AudioSilenceDetected="audioSilenceDetected",ws.VisibilityChanged="visibilityChanged",ws.VideoDimensionsChanged="videoDimensionsChanged",ws.VideoPlaybackStarted="videoPlaybackStarted",ws.VideoPlaybackFailed="videoPlaybackFailed",ws.ElementAttached="elementAttached",ws.ElementDetached="elementDetached",ws.UpstreamPaused="upstreamPaused",ws.UpstreamResumed="upstreamResumed",ws.SubscriptionPermissionChanged="subscriptionPermissionChanged",ws.SubscriptionStatusChanged="subscriptionStatusChanged",ws.SubscriptionFailed="subscriptionFailed",ws.TrackProcessorUpdate="trackProcessorUpdate",ws.AudioTrackFeatureUpdate="audioTrackFeatureUpdate",ws.TranscriptionReceived="transcriptionReceived",ws.TimeSyncUpdate="timeSyncUpdate";const _s=/version\/(\d+(\.?_?\d+)+)/i;let As;function Ls(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(void 0===e&&"undefined"==typeof navigator)return;const n=(null!=e?e:navigator.userAgent).toLowerCase();if(void 0===As||t){const e=Us.find((e=>{let{test:t}=e;return t.test(n)}));As=null==e?void 0:e.describe(n)}return As}const Us=[{test:/firefox|iceweasel|fxios/i,describe:e=>({name:"Firefox",version:js(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e),os:e.toLowerCase().includes("fxios")?"iOS":void 0,osVersion:Fs(e)})},{test:/chrom|crios|crmo/i,describe:e=>({name:"Chrome",version:js(/(?:chrome|chromium|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e),os:e.toLowerCase().includes("crios")?"iOS":void 0,osVersion:Fs(e)})},{test:/safari|applewebkit/i,describe:e=>({name:"Safari",version:js(_s,e),os:e.includes("mobile/")?"iOS":"macOS",osVersion:Fs(e)})}];function js(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;const i=t.match(e);return i&&i.length>=n&&i[n]||""}function Fs(e){return e.includes("mac os")?js(/\(.+?(\d+_\d+(:?_\d+)?)/,e,1).replace(/_/g,"."):void 0}const Bs="2.9.9";class Vs{}Vs.setTimeout=function(){return setTimeout(...arguments)},Vs.setInterval=function(){return setInterval(...arguments)},Vs.clearTimeout=function(){return clearTimeout(...arguments)},Vs.clearInterval=function(){return clearInterval(...arguments)};const qs=[];e.VideoQuality=void 0,function(e){e[e.LOW=0]="LOW",e[e.MEDIUM=1]="MEDIUM",e[e.HIGH=2]="HIGH"}(e.VideoQuality||(e.VideoQuality={}));class Ks extends ei.EventEmitter{constructor(t,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};var s;super(),this.attachedElements=[],this.isMuted=!1,this.streamState=Ks.StreamState.Active,this.isInBackground=!1,this._currentBitrate=0,this.log=qn,this.appVisibilityChangedListener=()=>{this.backgroundTimeout&&clearTimeout(this.backgroundTimeout),"hidden"===document.visibilityState?this.backgroundTimeout=setTimeout((()=>this.handleAppVisibilityChanged()),5e3):this.handleAppVisibilityChanged()},this.log=Wn(null!==(s=i.loggerName)&&void 0!==s?s:e.LoggerNames.Track),this.loggerContextCb=i.loggerContextCb,this.setMaxListeners(100),this.kind=n,this._mediaStreamTrack=t,this._mediaStreamID=t.id,this.source=Ks.Source.Unknown}get logContext(){var e;return Object.assign(Object.assign({},null===(e=this.loggerContextCb)||void 0===e?void 0:e.call(this)),tr(this))}get currentBitrate(){return this._currentBitrate}get mediaStreamTrack(){return this._mediaStreamTrack}get mediaStreamID(){return this._mediaStreamID}attach(t){let n="audio";this.kind===Ks.Kind.Video&&(n="video"),0===this.attachedElements.length&&this.kind===Ks.Kind.Video&&this.addAppVisibilityListener(),t||("audio"===n&&(qs.forEach((e=>{null!==e.parentElement||t||(t=e)})),t&&qs.splice(qs.indexOf(t),1)),t||(t=document.createElement(n))),this.attachedElements.includes(t)||this.attachedElements.push(t),Ws(this.mediaStreamTrack,t);const i=t.srcObject.getTracks(),s=i.some((e=>"audio"===e.kind));return t.play().then((()=>{this.emit(s?e.TrackEvent.AudioPlaybackStarted:e.TrackEvent.VideoPlaybackStarted)})).catch((n=>{"NotAllowedError"===n.name?this.emit(s?e.TrackEvent.AudioPlaybackFailed:e.TrackEvent.VideoPlaybackFailed,n):"AbortError"===n.name?qn.debug("".concat(s?"audio":"video"," playback aborted, likely due to new play request")):qn.warn("could not playback ".concat(s?"audio":"video"),n),s&&t&&i.some((e=>"video"===e.kind))&&"NotAllowedError"===n.name&&(t.muted=!0,t.play().catch((()=>{})))})),this.emit(e.TrackEvent.ElementAttached,t),t}detach(t){try{if(t){Hs(this.mediaStreamTrack,t);const n=this.attachedElements.indexOf(t);return n>=0&&(this.attachedElements.splice(n,1),this.recycleElement(t),this.emit(e.TrackEvent.ElementDetached,t)),t}const n=[];return this.attachedElements.forEach((t=>{Hs(this.mediaStreamTrack,t),n.push(t),this.recycleElement(t),this.emit(e.TrackEvent.ElementDetached,t)})),this.attachedElements=[],n}finally{0===this.attachedElements.length&&this.removeAppVisibilityListener()}}stop(){this.stopMonitor(),this._mediaStreamTrack.stop()}enable(){this._mediaStreamTrack.enabled=!0}disable(){this._mediaStreamTrack.enabled=!1}stopMonitor(){this.monitorInterval&&clearInterval(this.monitorInterval),this.timeSyncHandle&&cancelAnimationFrame(this.timeSyncHandle)}updateLoggerOptions(e){e.loggerName&&(this.log=Wn(e.loggerName)),e.loggerContextCb&&(this.loggerContextCb=e.loggerContextCb)}recycleElement(e){if(e instanceof HTMLAudioElement){let t=!0;e.pause(),qs.forEach((e=>{e.parentElement||(t=!1)})),t&&qs.push(e)}}handleAppVisibilityChanged(){return Qn(this,void 0,void 0,(function*(){this.isInBackground="hidden"===document.visibilityState,this.isInBackground||this.kind!==Ks.Kind.Video||setTimeout((()=>this.attachedElements.forEach((e=>e.play().catch((()=>{}))))),0)}))}addAppVisibilityListener(){mo()?(this.isInBackground="hidden"===document.visibilityState,document.addEventListener("visibilitychange",this.appVisibilityChangedListener)):this.isInBackground=!1}removeAppVisibilityListener(){mo()&&document.removeEventListener("visibilitychange",this.appVisibilityChangedListener)}}function Ws(e,t){let n,i;n=t.srcObject instanceof MediaStream?t.srcObject:new MediaStream,i="audio"===e.kind?n.getAudioTracks():n.getVideoTracks(),i.includes(e)||(i.forEach((e=>{n.removeTrack(e)})),n.addTrack(e)),uo()&&t instanceof HTMLVideoElement||(t.autoplay=!0),t.muted=0===n.getAudioTracks().length,t instanceof HTMLVideoElement&&(t.playsInline=!0),t.srcObject!==n&&(t.srcObject=n,(uo()||lo())&&t instanceof HTMLVideoElement&&setTimeout((()=>{t.srcObject=n,t.play().catch((()=>{}))}),0))}function Hs(e,t){if(t.srcObject instanceof MediaStream){const n=t.srcObject;n.removeTrack(e),n.getTracks().length>0?t.srcObject=n:t.srcObject=null}}!function(e){let t,n,i;!function(e){e.Audio="audio",e.Video="video",e.Unknown="unknown"}(t=e.Kind||(e.Kind={})),function(e){e.Camera="camera",e.Microphone="microphone",e.ScreenShare="screen_share",e.ScreenShareAudio="screen_share_audio",e.Unknown="unknown"}(n=e.Source||(e.Source={})),function(e){e.Active="active",e.Paused="paused",e.Unknown="unknown"}(i=e.StreamState||(e.StreamState={})),e.kindToProto=function(e){switch(e){case t.Audio:return Je.AUDIO;case t.Video:return Je.VIDEO;default:return Je.DATA}},e.kindFromProto=function(e){switch(e){case Je.AUDIO:return t.Audio;case Je.VIDEO:return t.Video;default:return t.Unknown}},e.sourceToProto=function(e){switch(e){case n.Camera:return Qe.CAMERA;case n.Microphone:return Qe.MICROPHONE;case n.ScreenShare:return Qe.SCREEN_SHARE;case n.ScreenShareAudio:return Qe.SCREEN_SHARE_AUDIO;default:return Qe.UNKNOWN}},e.sourceFromProto=function(e){switch(e){case Qe.CAMERA:return n.Camera;case Qe.MICROPHONE:return n.Microphone;case Qe.SCREEN_SHARE:return n.ScreenShare;case Qe.SCREEN_SHARE_AUDIO:return n.ScreenShareAudio;default:return n.Unknown}},e.streamStateFromProto=function(e){switch(e){case qt.ACTIVE:return i.Active;case qt.PAUSED:return i.Paused;default:return i.Unknown}}}(Ks);class Gs{constructor(e,t,n,i,s){if("object"==typeof e)this.width=e.width,this.height=e.height,this.aspectRatio=e.aspectRatio,this.encoding={maxBitrate:e.maxBitrate,maxFramerate:e.maxFramerate,priority:e.priority};else{if(void 0===t||void 0===n)throw new TypeError("Unsupported options: provide at least width, height and maxBitrate");this.width=e,this.height=t,this.aspectRatio=e/t,this.encoding={maxBitrate:n,maxFramerate:i,priority:s}}}get resolution(){return{width:this.width,height:this.height,frameRate:this.encoding.maxFramerate,aspectRatio:this.aspectRatio}}}const zs=["vp8","h264"],Js=["vp8","h264","vp9","av1"];function Qs(e){return!!zs.find((t=>t===e))}var Ys;e.BackupCodecPolicy=void 0,function(e){e[e.REGRESSION=0]="REGRESSION",e[e.SIMULCAST=1]="SIMULCAST"}(e.BackupCodecPolicy||(e.BackupCodecPolicy={})),e.AudioPresets=void 0,(Ys=e.AudioPresets||(e.AudioPresets={})).telephone={maxBitrate:12e3},Ys.speech={maxBitrate:24e3},Ys.music={maxBitrate:48e3},Ys.musicStereo={maxBitrate:64e3},Ys.musicHighQuality={maxBitrate:96e3},Ys.musicHighQualityStereo={maxBitrate:128e3};const Xs={h90:new Gs(160,90,9e4,20),h180:new Gs(320,180,16e4,20),h216:new Gs(384,216,18e4,20),h360:new Gs(640,360,45e4,20),h540:new Gs(960,540,8e5,25),h720:new Gs(1280,720,17e5,30),h1080:new Gs(1920,1080,3e6,30),h1440:new Gs(2560,1440,5e6,30),h2160:new Gs(3840,2160,8e6,30)},Zs={h120:new Gs(160,120,7e4,20),h180:new Gs(240,180,125e3,20),h240:new Gs(320,240,14e4,20),h360:new Gs(480,360,33e4,20),h480:new Gs(640,480,5e5,20),h540:new Gs(720,540,6e5,25),h720:new Gs(960,720,13e5,30),h1080:new Gs(1440,1080,23e5,30),h1440:new Gs(1920,1440,38e5,30)},$s={h360fps3:new Gs(640,360,2e5,3,"medium"),h360fps15:new Gs(640,360,4e5,15,"medium"),h720fps5:new Gs(1280,720,8e5,5,"medium"),h720fps15:new Gs(1280,720,15e5,15,"medium"),h720fps30:new Gs(1280,720,2e6,30,"medium"),h1080fps15:new Gs(1920,1080,25e5,15,"medium"),h1080fps30:new Gs(1920,1080,5e6,30,"medium"),original:new Gs(0,0,7e6,30,"medium")},eo="https://aomediacodec.github.io/av1-rtp-spec/#dependency-descriptor-rtp-header-extension";function to(e){return Qn(this,void 0,void 0,(function*(){return new Promise((t=>Vs.setTimeout(t,e)))}))}function no(){return"addTransceiver"in RTCPeerConnection.prototype}function io(){return"addTrack"in RTCPeerConnection.prototype}function so(){if(!("getCapabilities"in RTCRtpSender))return!1;if(uo())return!1;const e=RTCRtpSender.getCapabilities("video");let t=!1;if(e)for(const n of e.codecs)if("video/AV1"===n.mimeType){t=!0;break}return t}function oo(){if(!("getCapabilities"in RTCRtpSender))return!1;if(lo())return!1;if(uo()){const e=Ls();if((null==e?void 0:e.version)&&yo(e.version,"16")<0)return!1}const e=RTCRtpSender.getCapabilities("video");let t=!1;if(e)for(const n of e.codecs)if("video/VP9"===n.mimeType){t=!0;break}return t}function ro(e){return"av1"===e||"vp9"===e}function ao(e){return!!document&&(e||(e=document.createElement("audio")),"setSinkId"in e)}function co(){return"undefined"!=typeof RTCPeerConnection&&(no()||io())}function lo(){var e;return"Firefox"===(null===(e=Ls())||void 0===e?void 0:e.name)}function uo(){var e;return"Safari"===(null===(e=Ls())||void 0===e?void 0:e.name)}function ho(){const e=Ls();return"Safari"===(null==e?void 0:e.name)&&e.version.startsWith("17.")}function po(){var e,t;return!!mo()&&(null!==(t=null===(e=navigator.userAgentData)||void 0===e?void 0:e.mobile)&&void 0!==t?t:/Tablet|iPad|Mobile|Android|BlackBerry/.test(navigator.userAgent))}function mo(){return"undefined"!=typeof document}function go(){return"ReactNative"==navigator.product}function vo(e){return e.hostname.endsWith(".livekit.cloud")||e.hostname.endsWith(".livekit.run")}function fo(){if(global&&global.LiveKitReactNativeGlobal)return global.LiveKitReactNativeGlobal}function ko(){if(!go())return;let e=fo();return e?e.platform:void 0}function bo(){if(mo())return window.devicePixelRatio;if(go()){let e=fo();if(e)return e.devicePixelRatio}return 1}function yo(e,t){const n=e.split("."),i=t.split("."),s=Math.min(n.length,i.length);for(let e=0;e<s;++e){const t=parseInt(n[e],10),o=parseInt(i[e],10);if(t>o)return 1;if(t<o)return-1;if(e===s-1&&t===o)return 0}return""===e&&""!==t?-1:""===t?1:n.length==i.length?0:n.length<i.length?-1:1}function To(e){for(const t of e)t.target.handleResize(t)}function Co(e){for(const t of e)t.target.handleVisibilityChanged(t)}let So=null;const Eo=()=>(So||(So=new ResizeObserver(To)),So);let wo=null;const Po=()=>(wo||(wo=new IntersectionObserver(Co,{root:null,rootMargin:"0px"})),wo);let Ro,Io;function Oo(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:16,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:16,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const s=document.createElement("canvas");s.width=e,s.height=t;const o=s.getContext("2d");null==o||o.fillRect(0,0,s.width,s.height),i&&o&&(o.beginPath(),o.arc(e/2,t/2,50,0,2*Math.PI,!0),o.closePath(),o.fillStyle="grey",o.fill());const r=s.captureStream(),[a]=r.getTracks();if(!a)throw Error("Could not get empty media stream video track");return a.enabled=n,a}function Do(){if(!Io){const e=new AudioContext,t=e.createOscillator(),n=e.createGain();n.gain.setValueAtTime(0,0);const i=e.createMediaStreamDestination();if(t.connect(n),n.connect(i),t.start(),[Io]=i.stream.getAudioTracks(),!Io)throw Error("Could not get empty media stream audio track");Io.enabled=!1}return Io.clone()}class xo{constructor(e,t){this.onFinally=t,this.promise=new Promise(((t,n)=>Qn(this,void 0,void 0,(function*(){this.resolve=t,this.reject=n,e&&(yield e(t,n))})))).finally((()=>{var e;return null===(e=this.onFinally)||void 0===e?void 0:e.call(this)}))}}function Mo(e){if("string"==typeof e||"number"==typeof e)return e;if(Array.isArray(e))return e[0];if(e.exact)return Array.isArray(e.exact)?e.exact[0]:e.exact;if(e.ideal)return Array.isArray(e.ideal)?e.ideal[0]:e.ideal;throw Error("could not unwrap constraint")}function No(e){return e.startsWith("ws")?e.replace(/^(ws)/,"http"):e}function _o(t){switch(t.reason){case e.ConnectionErrorReason.LeaveRequest:return t.context;case e.ConnectionErrorReason.Cancelled:return $e.CLIENT_INITIATED;case e.ConnectionErrorReason.NotAllowed:return $e.USER_REJECTED;case e.ConnectionErrorReason.ServerUnreachable:return $e.JOIN_FAILURE;default:return $e.UNKNOWN_REASON}}function Ao(e){return void 0!==e?Number(e):void 0}function Lo(e){return void 0!==e?BigInt(e):void 0}function Uo(e){return!!e&&!(e instanceof MediaStreamTrack)&&e.isLocal}function jo(e){return!!e&&e.kind==Ks.Kind.Audio}function Fo(e){return!!e&&e.kind==Ks.Kind.Video}function Bo(e){return Uo(e)&&Fo(e)}function Vo(e){return Uo(e)&&jo(e)}function qo(e){return!!e&&!e.isLocal}function Ko(e){return!!e&&!e.isLocal}function Wo(e){return qo(e)&&Fo(e)}function Ho(e){return e.isLocal}function Go(e,t,n){var i,s,o,r,a;const{optionsWithoutProcessor:c,audioProcessor:d,videoProcessor:l}=nr(null!=e?e:{}),u=null==t?void 0:t.processor,h=null==n?void 0:n.processor,p=null!==(i=function(e){if(void 0!==e)return"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}(c))&&void 0!==i?i:{};return!0===p.audio&&(p.audio={}),!0===p.video&&(p.video={}),p.audio&&(zo(p.audio,t),null!==(s=(r=p.audio).deviceId)&&void 0!==s||(r.deviceId="default"),(d||u)&&(p.audio.processor=null!=d?d:u)),p.video&&(zo(p.video,n),null!==(o=(a=p.video).deviceId)&&void 0!==o||(a.deviceId="default"),(l||h)&&(p.video.processor=null!=l?l:h)),p}function zo(e,t){return Object.keys(t).forEach((n=>{void 0===e[n]&&(e[n]=t[n])})),e}function Jo(e){var t,n,i,s;const o={};if(e.video)if("object"==typeof e.video){const n={},s=n,r=e.video;Object.keys(r).forEach((e=>{if("resolution"===e)zo(s,r.resolution);else s[e]=r[e]})),o.video=n,null!==(t=(i=o.video).deviceId)&&void 0!==t||(i.deviceId="default")}else o.video=!!e.video&&{deviceId:"default"};else o.video=!1;return e.audio?"object"==typeof e.audio?(o.audio=e.audio,null!==(n=(s=o.audio).deviceId)&&void 0!==n||(s.deviceId="default")):o.audio={deviceId:"default"}:o.audio=!1,o}function Qo(e){return Qn(this,arguments,void 0,(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200;return function*(){const n=Yo();if(n){const i=n.createAnalyser();i.fftSize=2048;const s=i.frequencyBinCount,o=new Uint8Array(s);n.createMediaStreamSource(new MediaStream([e.mediaStreamTrack])).connect(i),yield to(t),i.getByteTimeDomainData(o);const r=o.some((e=>128!==e&&0!==e));return n.close(),!r}return!1}()}))}function Yo(){var e;const t="undefined"!=typeof window&&(window.AudioContext||window.webkitAudioContext);if(t){const n=new t({latencyHint:"interactive"});if("suspended"===n.state&&"undefined"!=typeof window&&(null===(e=window.document)||void 0===e?void 0:e.body)){const e=()=>Qn(this,void 0,void 0,(function*(){var t;try{"suspended"===n.state&&(yield n.resume())}catch(e){console.warn("Error trying to auto-resume audio context",e)}null===(t=window.document.body)||void 0===t||t.removeEventListener("click",e)}));window.document.body.addEventListener("click",e)}return n}}function Xo(e){return e===Ks.Source.Microphone?"audioinput":e===Ks.Source.Camera?"videoinput":void 0}function Zo(e){var t,n;let i=null===(t=e.video)||void 0===t||t;return e.resolution&&e.resolution.width>0&&e.resolution.height>0&&(i="boolean"==typeof i?{}:i,i=uo()?Object.assign(Object.assign({},i),{width:{max:e.resolution.width},height:{max:e.resolution.height},frameRate:e.resolution.frameRate}):Object.assign(Object.assign({},i),{width:{ideal:e.resolution.width},height:{ideal:e.resolution.height},frameRate:e.resolution.frameRate})),{audio:null!==(n=e.audio)&&void 0!==n&&n,video:i,controller:e.controller,selfBrowserSurface:e.selfBrowserSurface,surfaceSwitching:e.surfaceSwitching,systemAudio:e.systemAudio,preferCurrentTab:e.preferCurrentTab}}function $o(e){return e.split("/")[1].toLowerCase()}function er(e){const t=[];return e.forEach((e=>{void 0!==e.track&&t.push(new Zt({cid:e.track.mediaStreamID,track:e.trackInfo}))})),t}function tr(e){return"mediaStreamTrack"in e?{trackID:e.sid,source:e.source,muted:e.isMuted,enabled:e.mediaStreamTrack.enabled,kind:e.kind,streamID:e.mediaStreamID,streamTrackID:e.mediaStreamTrack.id}:{trackID:e.trackSid,enabled:e.isEnabled,muted:e.isMuted,trackInfo:Object.assign({mimeType:e.mimeType,name:e.trackName,encrypted:e.isEncrypted,kind:e.kind,source:e.source},e.track?tr(e.track):{})}}function nr(e){const t=Object.assign({},e);let n,i;return"object"==typeof t.audio&&t.audio.processor&&(n=t.audio.processor,t.audio=Object.assign(Object.assign({},t.audio),{processor:void 0})),"object"==typeof t.video&&t.video.processor&&(i=t.video.processor,t.video=Object.assign(Object.assign({},t.video),{processor:void 0})),{audioProcessor:n,videoProcessor:i,optionsWithoutProcessor:t}}class ir extends ei.EventEmitter{constructor(t){super(),this.onWorkerMessage=t=>{var n,i;const{kind:s,data:o}=t.data;switch(s){case"error":qn.error(o.error.message),this.emit(e.EncryptionEvent.EncryptionError,o.error);break;case"initAck":o.enabled&&this.keyProvider.getKeys().forEach((e=>{this.postKey(e)}));break;case"enable":if(o.enabled&&this.keyProvider.getKeys().forEach((e=>{this.postKey(e)})),this.encryptionEnabled!==o.enabled&&o.participantIdentity===(null===(n=this.room)||void 0===n?void 0:n.localParticipant.identity))this.emit(e.EncryptionEvent.ParticipantEncryptionStatusChanged,o.enabled,this.room.localParticipant),this.encryptionEnabled=o.enabled;else if(o.participantIdentity){const t=null===(i=this.room)||void 0===i?void 0:i.getParticipantByIdentity(o.participantIdentity);if(!t)throw TypeError("couldn't set encryption status, participant not found".concat(o.participantIdentity));this.emit(e.EncryptionEvent.ParticipantEncryptionStatusChanged,o.enabled,t)}break;case"ratchetKey":this.keyProvider.emit(e.KeyProviderEvent.KeyRatcheted,o.material,o.keyIndex)}},this.onWorkerError=t=>{qn.error("e2ee worker encountered an error:",{error:t.error}),this.emit(e.EncryptionEvent.EncryptionError,t.error)},this.keyProvider=t.keyProvider,this.worker=t.worker,this.encryptionEnabled=!1}setup(e){if(!us())throw new Rs("tried to setup end-to-end encryption on an unsupported browser");if(qn.info("setting up e2ee"),e!==this.room){this.room=e,this.setupEventListeners(e,this.keyProvider);const t={kind:"init",data:{keyProviderOptions:this.keyProvider.getOptions(),loglevel:Hn.getLevel()}};this.worker&&(qn.info("initializing worker",{worker:this.worker}),this.worker.onmessage=this.onWorkerMessage,this.worker.onerror=this.onWorkerError,this.worker.postMessage(t))}}setParticipantCryptorEnabled(e,t){qn.debug("set e2ee to ".concat(e," for participant ").concat(t)),this.postEnable(e,t)}setSifTrailer(e){e&&0!==e.length?this.postSifTrailer(e):qn.warn("ignoring server sent trailer as it's empty")}setupEngine(t){t.on(e.EngineEvent.RTPVideoMapUpdate,(e=>{this.postRTPMap(e)}))}setupEventListeners(t,n){t.on(e.RoomEvent.TrackPublished,((e,t)=>this.setParticipantCryptorEnabled(e.trackInfo.encryption!==dt.NONE,t.identity))),t.on(e.RoomEvent.ConnectionStateChanged,(n=>{n===e.ConnectionState.Connected&&t.remoteParticipants.forEach((e=>{e.trackPublications.forEach((t=>{this.setParticipantCryptorEnabled(t.trackInfo.encryption!==dt.NONE,e.identity)}))}))})).on(e.RoomEvent.TrackUnsubscribed,((e,t,n)=>{var i;const s={kind:"removeTransform",data:{participantIdentity:n.identity,trackId:e.mediaStreamID}};null===(i=this.worker)||void 0===i||i.postMessage(s)})).on(e.RoomEvent.TrackSubscribed,((e,t,n)=>{this.setupE2EEReceiver(e,n.identity,t.trackInfo)})).on(e.RoomEvent.SignalConnected,(()=>{if(!this.room)throw new TypeError("expected room to be present on signal connect");n.getKeys().forEach((e=>{this.postKey(e)})),this.setParticipantCryptorEnabled(this.room.localParticipant.isE2EEEnabled,this.room.localParticipant.identity)})),t.localParticipant.on(e.ParticipantEvent.LocalTrackPublished,(e=>Qn(this,void 0,void 0,(function*(){this.setupE2EESender(e.track,e.track.sender)})))),n.on(e.KeyProviderEvent.SetKey,(e=>this.postKey(e))).on(e.KeyProviderEvent.RatchetRequest,((e,t)=>this.postRatchetRequest(e,t)))}postRatchetRequest(e,t){if(!this.worker)throw Error("could not ratchet key, worker is missing");const n={kind:"ratchetRequest",data:{participantIdentity:e,keyIndex:t}};this.worker.postMessage(n)}postKey(e){let{key:t,participantIdentity:n,keyIndex:i}=e;var s;if(!this.worker)throw Error("could not set key, worker is missing");const o={kind:"setKey",data:{participantIdentity:n,isPublisher:n===(null===(s=this.room)||void 0===s?void 0:s.localParticipant.identity),key:t,keyIndex:i}};this.worker.postMessage(o)}postEnable(e,t){if(!this.worker)throw new ReferenceError("failed to enable e2ee, worker is not ready");{const n={kind:"enable",data:{enabled:e,participantIdentity:t}};this.worker.postMessage(n)}}postRTPMap(e){var t;if(!this.worker)throw TypeError("could not post rtp map, worker is missing");if(!(null===(t=this.room)||void 0===t?void 0:t.localParticipant.identity))throw TypeError("could not post rtp map, local participant identity is missing");const n={kind:"setRTPMap",data:{map:e,participantIdentity:this.room.localParticipant.identity}};this.worker.postMessage(n)}postSifTrailer(e){if(!this.worker)throw Error("could not post SIF trailer, worker is missing");const t={kind:"setSifTrailer",data:{trailer:e}};this.worker.postMessage(t)}setupE2EEReceiver(e,t,n){if(e.receiver){if(!(null==n?void 0:n.mimeType)||""===n.mimeType)throw new TypeError("MimeType missing from trackInfo, cannot set up E2EE cryptor");this.handleReceiver(e.receiver,e.mediaStreamID,t,"video"===e.kind?$o(n.mimeType):void 0)}}setupE2EESender(e,t){Uo(e)&&t?this.handleSender(t,e.mediaStreamID,void 0):t||qn.warn("early return because sender is not ready")}handleReceiver(e,t,n,i){return Qn(this,void 0,void 0,(function*(){if(this.worker){if(hs()){const s={kind:"decode",participantIdentity:n,trackId:t,codec:i};e.transform=new RTCRtpScriptTransform(this.worker,s)}else{if(as in e&&i){const e={kind:"updateCodec",data:{trackId:t,codec:i,participantIdentity:n}};return void this.worker.postMessage(e)}let s=e.writableStream,o=e.readableStream;if(!s||!o){const t=e.createEncodedStreams();e.writableStream=t.writable,s=t.writable,e.readableStream=t.readable,o=t.readable}const r={kind:"decode",data:{readableStream:o,writableStream:s,trackId:t,codec:i,participantIdentity:n}};this.worker.postMessage(r,[o,s])}e[as]=!0}}))}handleSender(e,t,n){var i;if(!(as in e)&&this.worker){if(!(null===(i=this.room)||void 0===i?void 0:i.localParticipant.identity)||""===this.room.localParticipant.identity)throw TypeError("local identity needs to be known in order to set up encrypted sender");if(hs()){qn.info("initialize script transform");const i={kind:"encode",participantIdentity:this.room.localParticipant.identity,trackId:t,codec:n};e.transform=new RTCRtpScriptTransform(this.worker,i)}else{qn.info("initialize encoded streams");const i=e.createEncodedStreams(),s={kind:"encode",data:{readableStream:i.readable,writableStream:i.writable,codec:n,trackId:t,participantIdentity:this.room.localParticipant.identity}};this.worker.postMessage(s,[i.readable,i.writable])}e[as]=!0}}}const sr="default";class or{constructor(){this._previousDevices=[]}static getInstance(){return void 0===this.instance&&(this.instance=new or),this.instance}get previousDevices(){return this._previousDevices}getDevices(e){return Qn(this,arguments,void 0,(function(e){var t=this;let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return function*(){var i;if((null===(i=or.userMediaPromiseMap)||void 0===i?void 0:i.size)>0){qn.debug("awaiting getUserMedia promise");try{e?yield or.userMediaPromiseMap.get(e):yield Promise.all(or.userMediaPromiseMap.values())}catch(e){qn.warn("error waiting for media permissons")}}let s=yield navigator.mediaDevices.enumerateDevices();if(n&&(!uo()||!t.hasDeviceInUse(e))){if(0===s.filter((t=>t.kind===e)).length||s.some((t=>{const n=""===t.label,i=!e||t.kind===e;return n&&i}))){const t={video:"audioinput"!==e&&"audiooutput"!==e,audio:"videoinput"!==e&&{deviceId:"default"}},n=yield navigator.mediaDevices.getUserMedia(t);s=yield navigator.mediaDevices.enumerateDevices(),n.getTracks().forEach((e=>{e.stop()}))}}return t._previousDevices=s,e&&(s=s.filter((t=>t.kind===e))),s}()}))}normalizeDeviceId(e,t,n){return Qn(this,void 0,void 0,(function*(){if(t!==sr)return t;const i=yield this.getDevices(e),s=i.find((e=>e.deviceId===sr));if(!s)return void qn.warn("could not reliably determine default device");const o=i.find((e=>e.deviceId!==sr&&e.groupId===(null!=n?n:s.groupId)));if(o)return null==o?void 0:o.deviceId;qn.warn("could not reliably determine default device")}))}hasDeviceInUse(e){return e?or.userMediaPromiseMap.has(e):or.userMediaPromiseMap.size>0}}var rr;or.mediaDeviceKinds=["audioinput","audiooutput","videoinput"],or.userMediaPromiseMap=new Map,function(e){e[e.WAITING=0]="WAITING",e[e.RUNNING=1]="RUNNING",e[e.COMPLETED=2]="COMPLETED"}(rr||(rr={}));class ar{constructor(){this.pendingTasks=new Map,this.taskMutex=new s,this.nextTaskIndex=0}run(e){return Qn(this,void 0,void 0,(function*(){const t={id:this.nextTaskIndex++,enqueuedAt:Date.now(),status:rr.WAITING};this.pendingTasks.set(t.id,t);const n=yield this.taskMutex.lock();try{return t.executedAt=Date.now(),t.status=rr.RUNNING,yield e()}finally{t.status=rr.COMPLETED,this.pendingTasks.delete(t.id),n()}}))}flush(){return Qn(this,void 0,void 0,(function*(){return this.run((()=>Qn(this,void 0,void 0,(function*(){}))))}))}snapshot(){return Array.from(this.pendingTasks.values())}}const cr=["syncState","trickle","offer","answer","simulate","leave"];var dr;!function(e){e[e.CONNECTING=0]="CONNECTING",e[e.CONNECTED=1]="CONNECTED",e[e.RECONNECTING=2]="RECONNECTING",e[e.DISCONNECTING=3]="DISCONNECTING",e[e.DISCONNECTED=4]="DISCONNECTED"}(dr||(dr={}));class lr{get currentState(){return this.state}get isDisconnected(){return this.state===dr.DISCONNECTING||this.state===dr.DISCONNECTED}get isEstablishingConnection(){return this.state===dr.CONNECTING||this.state===dr.RECONNECTING}getNextRequestId(){return this._requestId+=1,this._requestId}constructor(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var i;this.rtt=0,this.state=dr.DISCONNECTED,this.log=qn,this._requestId=0,this.resetCallbacks=()=>{this.onAnswer=void 0,this.onLeave=void 0,this.onLocalTrackPublished=void 0,this.onLocalTrackUnpublished=void 0,this.onNegotiateRequested=void 0,this.onOffer=void 0,this.onRemoteMuteChanged=void 0,this.onSubscribedQualityUpdate=void 0,this.onTokenRefresh=void 0,this.onTrickle=void 0,this.onClose=void 0},this.log=Wn(null!==(i=n.loggerName)&&void 0!==i?i:e.LoggerNames.Signal),this.loggerContextCb=n.loggerContextCb,this.useJSON=t,this.requestQueue=new ar,this.queuedRequests=[],this.closingLock=new s,this.connectionLock=new s,this.state=dr.DISCONNECTED}get logContext(){var e,t;return null!==(t=null===(e=this.loggerContextCb)||void 0===e?void 0:e.call(this))&&void 0!==t?t:{}}join(e,t,n,i){return Qn(this,void 0,void 0,(function*(){this.state=dr.CONNECTING,this.options=n;return yield this.connect(e,t,n,i)}))}reconnect(e,t,n,i){return Qn(this,void 0,void 0,(function*(){if(!this.options)return void this.log.warn("attempted to reconnect without signal options being set, ignoring",this.logContext);this.state=dr.RECONNECTING,this.clearPingInterval();return yield this.connect(e,t,Object.assign(Object.assign({},this.options),{reconnect:!0,sid:n,reconnectReason:i}))}))}connect(t,n,i,s){this.connectOptions=i;const o=new URL(function(e){return e.startsWith("http")?e.replace(/^(http)/,"ws"):e}(t)),r=o.pathname.endsWith("/");o.pathname+=r?"rtc":"/rtc";const a=function(e,t,n){var i;const s=new URLSearchParams;s.set("access_token",e),n.reconnect&&(s.set("reconnect","1"),n.sid&&s.set("sid",n.sid));s.set("auto_subscribe",n.autoSubscribe?"1":"0"),s.set("sdk",go()?"reactnative":"js"),s.set("version",t.version),s.set("protocol",t.protocol.toString()),t.deviceModel&&s.set("device_model",t.deviceModel);t.os&&s.set("os",t.os);t.osVersion&&s.set("os_version",t.osVersion);t.browser&&s.set("browser",t.browser);t.browserVersion&&s.set("browser_version",t.browserVersion);n.adaptiveStream&&s.set("adaptive_stream","1");n.reconnectReason&&s.set("reconnect_reason",n.reconnectReason.toString());(null===(i=navigator.connection)||void 0===i?void 0:i.type)&&s.set("network",navigator.connection.type);return s}(n,function(){var e;const t=new Ot({sdk:Dt.JS,protocol:15,version:Bs});return go()&&(t.os=null!==(e=ko())&&void 0!==e?e:""),t}(),i);for(const[e,t]of a)o.searchParams.set(e,t);return new Promise(((t,n)=>Qn(this,void 0,void 0,(function*(){const r=yield this.connectionLock.lock();try{const r=()=>Qn(this,void 0,void 0,(function*(){this.close(),clearTimeout(a),n(new Ps("room connection has been cancelled (signal)",e.ConnectionErrorReason.Cancelled))})),a=setTimeout((()=>{this.close(),n(new Ps("room connection has timed out (signal)",e.ConnectionErrorReason.ServerUnreachable))}),i.websocketTimeout);(null==s?void 0:s.aborted)&&r(),null==s||s.addEventListener("abort",r);const c=new URL(o.toString());c.searchParams.has("access_token")&&c.searchParams.set("access_token","<redacted>"),this.log.debug("connecting to ".concat(c),Object.assign({reconnect:i.reconnect,reconnectReason:i.reconnectReason},this.logContext)),this.ws&&(yield this.close(!1)),this.ws=new WebSocket(o.toString()),this.ws.binaryType="arraybuffer",this.ws.onopen=()=>{clearTimeout(a)},this.ws.onerror=t=>Qn(this,void 0,void 0,(function*(){if(this.state===dr.CONNECTED)this.handleWSError(t);else{this.state=dr.DISCONNECTED,clearTimeout(a);try{const t=new URL(o.toString());t.protocol="http".concat(t.protocol.substring(2)),t.pathname+="/validate";const i=yield fetch(t);if(i.status.toFixed(0).startsWith("4")){const t=yield i.text();n(new Ps(t,e.ConnectionErrorReason.NotAllowed,i.status))}else n(new Ps("Internal error",e.ConnectionErrorReason.InternalError,i.status))}catch(t){n(new Ps(t instanceof Error?t.message:"server was not reachable",e.ConnectionErrorReason.ServerUnreachable))}}})),this.ws.onmessage=o=>Qn(this,void 0,void 0,(function*(){var a,c,d;let l;if("string"==typeof o.data){const e=JSON.parse(o.data);l=Ht.fromJson(e,{ignoreUnknownFields:!0})}else{if(!(o.data instanceof ArrayBuffer))return void this.log.error("could not decode websocket message: ".concat(typeof o.data),this.logContext);l=Ht.fromBinary(new Uint8Array(o.data))}if(this.state!==dr.CONNECTED){let o=!1;if("join"===(null===(a=l.message)||void 0===a?void 0:a.case)?(this.state=dr.CONNECTED,null==s||s.removeEventListener("abort",r),this.pingTimeoutDuration=l.message.value.pingTimeout,this.pingIntervalDuration=l.message.value.pingInterval,this.pingTimeoutDuration&&this.pingTimeoutDuration>0&&(this.log.debug("ping config",Object.assign(Object.assign({},this.logContext),{timeout:this.pingTimeoutDuration,interval:this.pingIntervalDuration})),this.startPingInterval()),t(l.message.value)):this.state===dr.RECONNECTING&&"leave"!==l.message.case?(this.state=dr.CONNECTED,null==s||s.removeEventListener("abort",r),this.startPingInterval(),"reconnect"===(null===(c=l.message)||void 0===c?void 0:c.case)?t(l.message.value):(this.log.debug("declaring signal reconnected without reconnect response received",this.logContext),t(void 0),o=!0)):this.isEstablishingConnection&&"leave"===l.message.case?n(new Ps("Received leave request while trying to (re)connect",e.ConnectionErrorReason.LeaveRequest,void 0,l.message.value.reason)):i.reconnect||n(new Ps("did not receive join response, got ".concat(null===(d=l.message)||void 0===d?void 0:d.case," instead"),e.ConnectionErrorReason.InternalError)),!o)return}this.signalLatency&&(yield to(this.signalLatency)),this.handleSignalResponse(l)})),this.ws.onclose=t=>{this.isEstablishingConnection&&n(new Ps("Websocket got closed during a (re)connection attempt",e.ConnectionErrorReason.InternalError)),this.log.warn("websocket closed",Object.assign(Object.assign({},this.logContext),{reason:t.reason,code:t.code,wasClean:t.wasClean,state:this.state})),this.handleOnClose(t.reason)}}finally{r()}}))))}close(){return Qn(this,arguments,void 0,(function(){var e=this;let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return function*(){const n=yield e.closingLock.lock();try{if(e.clearPingInterval(),t&&(e.state=dr.DISCONNECTING),e.ws){e.ws.onmessage=null,e.ws.onopen=null,e.ws.onclose=null;const t=new Promise((t=>{e.ws?e.ws.onclose=()=>{t()}:t()}));e.ws.readyState<e.ws.CLOSING&&(e.ws.close(),yield Promise.race([t,to(250)])),e.ws=void 0}}finally{t&&(e.state=dr.DISCONNECTED),n()}}()}))}sendOffer(e){this.log.debug("sending offer",Object.assign(Object.assign({},this.logContext),{offerSdp:e.sdp})),this.sendRequest({case:"offer",value:hr(e)})}sendAnswer(e){return this.log.debug("sending answer",Object.assign(Object.assign({},this.logContext),{answerSdp:e.sdp})),this.sendRequest({case:"answer",value:hr(e)})}sendIceCandidate(e,t){return this.log.debug("sending ice candidate",Object.assign(Object.assign({},this.logContext),{candidate:e})),this.sendRequest({case:"trickle",value:new Jt({candidateInit:JSON.stringify(e),target:t})})}sendMuteTrack(e,t){return this.sendRequest({case:"mute",value:new Qt({sid:e,muted:t})})}sendAddTrack(e){return this.sendRequest({case:"addTrack",value:e})}sendUpdateLocalMetadata(e,t){return Qn(this,arguments,void 0,(function(e,t){var n=this;let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function*(){const s=n.getNextRequestId();return yield n.sendRequest({case:"updateMetadata",value:new ln({requestId:s,metadata:e,name:t,attributes:i})}),s}()}))}sendUpdateTrackSettings(e){this.sendRequest({case:"trackSetting",value:e})}sendUpdateSubscription(e){return this.sendRequest({case:"subscription",value:e})}sendSyncState(e){return this.sendRequest({case:"syncState",value:e})}sendUpdateVideoLayers(e,t){return this.sendRequest({case:"updateLayers",value:new dn({trackSid:e,layers:t})})}sendUpdateSubscriptionPermissions(e,t){return this.sendRequest({case:"subscriptionPermission",value:new Cn({allParticipants:e,trackPermissions:t})})}sendSimulateScenario(e){return this.sendRequest({case:"simulate",value:e})}sendPing(){return Promise.all([this.sendRequest({case:"ping",value:x.parse(Date.now())}),this.sendRequest({case:"pingReq",value:new Rn({timestamp:x.parse(Date.now()),rtt:x.parse(this.rtt)})})])}sendUpdateLocalAudioTrack(e,t){return this.sendRequest({case:"updateAudioTrack",value:new on({trackSid:e,features:t})})}sendLeave(){return this.sendRequest({case:"leave",value:new an({reason:$e.CLIENT_INITIATED,action:cn.DISCONNECT})})}sendRequest(e){return Qn(this,arguments,void 0,(function(e){var t=this;let n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function*(){const i=!n&&!function(e){const t=cr.indexOf(e.case)>=0;return qn.trace("request allowed to bypass queue:",{canPass:t,req:e}),t}(e);if(i&&t.state===dr.RECONNECTING)return void t.queuedRequests.push((()=>Qn(t,void 0,void 0,(function*(){yield this.sendRequest(e,!0)}))));if(n||(yield t.requestQueue.flush()),t.signalLatency&&(yield to(t.signalLatency)),!t.ws||t.ws.readyState!==t.ws.OPEN)return void t.log.error("cannot send signal request before connected, type: ".concat(null==e?void 0:e.case),t.logContext);const s=new Wt({message:e});try{t.useJSON?t.ws.send(s.toJsonString()):t.ws.send(s.toBinary())}catch(e){t.log.error("error sending signal message",Object.assign(Object.assign({},t.logContext),{error:e}))}}()}))}handleSignalResponse(e){var t,n;const i=e.message;if(null==i)return void this.log.debug("received unsupported message",this.logContext);let s=!1;if("answer"===i.case){const e=ur(i.value);this.onAnswer&&this.onAnswer(e)}else if("offer"===i.case){const e=ur(i.value);this.onOffer&&this.onOffer(e)}else if("trickle"===i.case){const e=JSON.parse(i.value.candidateInit);this.onTrickle&&this.onTrickle(e,i.value.target)}else"update"===i.case?this.onParticipantUpdate&&this.onParticipantUpdate(null!==(t=i.value.participants)&&void 0!==t?t:[]):"trackPublished"===i.case?this.onLocalTrackPublished&&this.onLocalTrackPublished(i.value):"speakersChanged"===i.case?this.onSpeakersChanged&&this.onSpeakersChanged(null!==(n=i.value.speakers)&&void 0!==n?n:[]):"leave"===i.case?this.onLeave&&this.onLeave(i.value):"mute"===i.case?this.onRemoteMuteChanged&&this.onRemoteMuteChanged(i.value.sid,i.value.muted):"roomUpdate"===i.case?this.onRoomUpdate&&i.value.room&&this.onRoomUpdate(i.value.room):"connectionQuality"===i.case?this.onConnectionQuality&&this.onConnectionQuality(i.value):"streamStateUpdate"===i.case?this.onStreamStateUpdate&&this.onStreamStateUpdate(i.value):"subscribedQualityUpdate"===i.case?this.onSubscribedQualityUpdate&&this.onSubscribedQualityUpdate(i.value):"subscriptionPermissionUpdate"===i.case?this.onSubscriptionPermissionUpdate&&this.onSubscriptionPermissionUpdate(i.value):"refreshToken"===i.case?this.onTokenRefresh&&this.onTokenRefresh(i.value):"trackUnpublished"===i.case?this.onLocalTrackUnpublished&&this.onLocalTrackUnpublished(i.value):"subscriptionResponse"===i.case?this.onSubscriptionError&&this.onSubscriptionError(i.value):"pong"===i.case||("pongResp"===i.case?(this.rtt=Date.now()-Number.parseInt(i.value.lastPingTimestamp.toString()),this.resetPingTimeout(),s=!0):"requestResponse"===i.case?this.onRequestResponse&&this.onRequestResponse(i.value):"trackSubscribed"===i.case?this.onLocalTrackSubscribed&&this.onLocalTrackSubscribed(i.value.trackSid):this.log.debug("unsupported message",Object.assign(Object.assign({},this.logContext),{msgCase:i.case})));s||this.resetPingTimeout()}setReconnected(){for(;this.queuedRequests.length>0;){const e=this.queuedRequests.shift();e&&this.requestQueue.run(e)}}handleOnClose(e){return Qn(this,void 0,void 0,(function*(){if(this.state===dr.DISCONNECTED)return;const t=this.onClose;yield this.close(),this.log.debug("websocket connection closed: ".concat(e),Object.assign(Object.assign({},this.logContext),{reason:e})),t&&t(e)}))}handleWSError(e){this.log.error("websocket error",Object.assign(Object.assign({},this.logContext),{error:e}))}resetPingTimeout(){this.clearPingTimeout(),this.pingTimeoutDuration?this.pingTimeout=Vs.setTimeout((()=>{this.log.warn("ping timeout triggered. last pong received at: ".concat(new Date(Date.now()-1e3*this.pingTimeoutDuration).toUTCString()),this.logContext),this.handleOnClose("ping timeout")}),1e3*this.pingTimeoutDuration):this.log.warn("ping timeout duration not set",this.logContext)}clearPingTimeout(){this.pingTimeout&&Vs.clearTimeout(this.pingTimeout)}startPingInterval(){this.clearPingInterval(),this.resetPingTimeout(),this.pingIntervalDuration?(this.log.debug("start ping interval",this.logContext),this.pingInterval=Vs.setInterval((()=>{this.sendPing()}),1e3*this.pingIntervalDuration)):this.log.warn("ping interval duration not set",this.logContext)}clearPingInterval(){this.log.debug("clearing ping interval",this.logContext),this.clearPingTimeout(),this.pingInterval&&Vs.clearInterval(this.pingInterval)}}function ur(e){const t={type:"offer",sdp:e.sdp};switch(e.type){case"answer":case"offer":case"pranswer":case"rollback":t.type=e.type}return t}function hr(e){return new en({sdp:e.sdp,type:e.type})}var pr,mr,gr,vr,fr,kr={},br={},yr={exports:{}};function Tr(){if(pr)return yr.exports;pr=1;var e=yr.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),t+=null!=e["network-id"]?" network-id %d":"%v",t+=null!=e["network-cost"]?" network-cost %d":"%v"}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(null!=e.clksrcExt?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var t="mediaclk:";return t+=null!=e.id?"id=%s %s":"%v%s",t+=null!=e.mediaClockValue?"=%s":"",t+=null!=e.rateNumerator?" rate=%s":"",t+=null!=e.rateDenominator?"/%s":""}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};return Object.keys(e).forEach((function(t){e[t].forEach((function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")}))})),yr.exports}function Cr(){return mr||(mr=1,function(e){var t=function(e){return String(Number(e))===e?Number(e):e},n=function(e,n,i){var s=e.name&&e.names;e.push&&!n[e.push]?n[e.push]=[]:s&&!n[e.name]&&(n[e.name]={});var o=e.push?{}:s?n[e.name]:n;!function(e,n,i,s){if(s&&!i)n[s]=t(e[1]);else for(var o=0;o<i.length;o+=1)null!=e[o+1]&&(n[i[o]]=t(e[o+1]))}(i.match(e.reg),o,e.names,e.name),e.push&&n[e.push].push(o)},i=Tr(),s=RegExp.prototype.test.bind(/^([a-z])=(.*)/);e.parse=function(e){var t={},o=[],r=t;return e.split(/(\r\n|\r|\n)/).filter(s).forEach((function(e){var t=e[0],s=e.slice(2);"m"===t&&(o.push({rtp:[],fmtp:[]}),r=o[o.length-1]);for(var a=0;a<(i[t]||[]).length;a+=1){var c=i[t][a];if(c.reg.test(s))return n(c,r,s)}})),t.media=o,t};var o=function(e,n){var i=n.split(/=(.+)/,2);return 2===i.length?e[i[0]]=t(i[1]):1===i.length&&n.length>1&&(e[i[0]]=void 0),e};e.parseParams=function(e){return e.split(/;\s?/).reduce(o,{})},e.parseFmtpConfig=e.parseParams,e.parsePayloads=function(e){return e.toString().split(" ").map(Number)},e.parseRemoteCandidates=function(e){for(var n=[],i=e.split(" ").map(t),s=0;s<i.length;s+=3)n.push({component:i[s],ip:i[s+1],port:i[s+2]});return n},e.parseImageAttributes=function(e){return e.split(" ").map((function(e){return e.substring(1,e.length-1).split(",").reduce(o,{})}))},e.parseSimulcastStreamList=function(e){return e.split(";").map((function(e){return e.split(",").map((function(e){var n,i=!1;return"~"!==e[0]?n=t(e):(n=t(e.substring(1,e.length)),i=!0),{scid:n,paused:i}}))}))}}(br)),br}function Sr(){if(vr)return gr;vr=1;var e=Tr(),t=/%[sdv%]/g,n=function(e){var n=1,i=arguments,s=i.length;return e.replace(t,(function(e){if(n>=s)return e;var t=i[n];switch(n+=1,e){case"%%":return"%";case"%s":return String(t);case"%d":return Number(t);case"%v":return""}}))},i=function(e,t,i){var s=[e+"="+(t.format instanceof Function?t.format(t.push?i:i[t.name]):t.format)];if(t.names)for(var o=0;o<t.names.length;o+=1){var r=t.names[o];t.name?s.push(i[t.name][r]):s.push(i[t.names[o]])}else s.push(i[t.name]);return n.apply(null,s)},s=["v","o","s","i","u","e","p","c","b","t","r","z","a"],o=["i","c","b","a"];return gr=function(t,n){n=n||{},null==t.version&&(t.version=0),null==t.name&&(t.name=" "),t.media.forEach((function(e){null==e.payloads&&(e.payloads="")}));var r=n.outerOrder||s,a=n.innerOrder||o,c=[];return r.forEach((function(n){e[n].forEach((function(e){e.name in t&&null!=t[e.name]?c.push(i(n,e,t)):e.push in t&&null!=t[e.push]&&t[e.push].forEach((function(t){c.push(i(n,e,t))}))}))})),t.media.forEach((function(t){c.push(i("m",e.m[0],t)),a.forEach((function(n){e[n].forEach((function(e){e.name in t&&null!=t[e.name]?c.push(i(n,e,t)):e.push in t&&null!=t[e.push]&&t[e.push].forEach((function(t){c.push(i(n,e,t))}))}))}))})),c.join("\r\n")+"\r\n"},gr}var Er=function(){if(fr)return kr;fr=1;var e=Cr(),t=Sr(),n=Tr();return kr.grammar=n,kr.write=t,kr.parse=e.parse,kr.parseParams=e.parseParams,kr.parseFmtpConfig=e.parseFmtpConfig,kr.parsePayloads=e.parsePayloads,kr.parseRemoteCandidates=e.parseRemoteCandidates,kr.parseImageAttributes=e.parseImageAttributes,kr.parseSimulcastStreamList=e.parseSimulcastStreamList,kr}();function wr(e,t,n){var i,s,o;void 0===t&&(t=50),void 0===n&&(n={});var r=null!=(i=n.isImmediate)&&i,a=null!=(s=n.callback)&&s,c=n.maxWait,d=Date.now(),l=[];function u(){if(void 0!==c){var e=Date.now()-d;if(e+t>=c)return c-e}return t}var h=function(){var t=[].slice.call(arguments),n=this;return new Promise((function(i,s){var c=r&&void 0===o;if(void 0!==o&&clearTimeout(o),o=setTimeout((function(){if(o=void 0,d=Date.now(),!r){var i=e.apply(n,t);a&&a(i),l.forEach((function(e){return(0,e.resolve)(i)})),l=[]}}),u()),c){var h=e.apply(n,t);return a&&a(h),i(h)}l.push({resolve:i,reject:s})}))};return h.cancel=function(e){void 0!==o&&clearTimeout(o),l.forEach((function(t){return(0,t.reject)(e)})),l=[]},h}const Pr="negotiationStarted",Rr="negotiationComplete",Ir="rtpVideoPayloadTypes";class Or extends ei.EventEmitter{get pc(){return this._pc||(this._pc=this.createPC()),this._pc}constructor(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var i;super(),this.log=qn,this.ddExtID=0,this.pendingCandidates=[],this.restartingIce=!1,this.renegotiate=!1,this.trackBitrates=[],this.remoteStereoMids=[],this.remoteNackMids=[],this.negotiate=wr((e=>Qn(this,void 0,void 0,(function*(){this.emit(Pr);try{yield this.createAndSendOffer()}catch(t){if(!e)throw t;e(t)}}))),20),this.close=()=>{this._pc&&(this._pc.close(),this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc.onicegatheringstatechange=null,this._pc.ondatachannel=null,this._pc.onnegotiationneeded=null,this._pc.onsignalingstatechange=null,this._pc.onicecandidate=null,this._pc.ondatachannel=null,this._pc.ontrack=null,this._pc.onconnectionstatechange=null,this._pc.oniceconnectionstatechange=null,this._pc=null)},this.log=Wn(null!==(i=n.loggerName)&&void 0!==i?i:e.LoggerNames.PCTransport),this.loggerOptions=n,this.config=t,this._pc=this.createPC()}createPC(){const e=new RTCPeerConnection(this.config);return e.onicecandidate=e=>{var t;e.candidate&&(null===(t=this.onIceCandidate)||void 0===t||t.call(this,e.candidate))},e.onicecandidateerror=e=>{var t;null===(t=this.onIceCandidateError)||void 0===t||t.call(this,e)},e.oniceconnectionstatechange=()=>{var t;null===(t=this.onIceConnectionStateChange)||void 0===t||t.call(this,e.iceConnectionState)},e.onsignalingstatechange=()=>{var t;null===(t=this.onSignalingStatechange)||void 0===t||t.call(this,e.signalingState)},e.onconnectionstatechange=()=>{var t;null===(t=this.onConnectionStateChange)||void 0===t||t.call(this,e.connectionState)},e.ondatachannel=e=>{var t;null===(t=this.onDataChannel)||void 0===t||t.call(this,e)},e.ontrack=e=>{var t;null===(t=this.onTrack)||void 0===t||t.call(this,e)},e}get logContext(){var e,t;return Object.assign({},null===(t=(e=this.loggerOptions).loggerContextCb)||void 0===t?void 0:t.call(e))}get isICEConnected(){return null!==this._pc&&("connected"===this.pc.iceConnectionState||"completed"===this.pc.iceConnectionState)}addIceCandidate(e){return Qn(this,void 0,void 0,(function*(){if(this.pc.remoteDescription&&!this.restartingIce)return this.pc.addIceCandidate(e);this.pendingCandidates.push(e)}))}setRemoteDescription(e){return Qn(this,void 0,void 0,(function*(){var t;let n;if("offer"===e.type){let{stereoMids:t,nackMids:n}=function(e){var t;const n=[],i=[],s=Er.parse(null!==(t=e.sdp)&&void 0!==t?t:"");let o=0;return s.media.forEach((e=>{var t;"audio"===e.type&&(e.rtp.some((e=>"opus"===e.codec&&(o=e.payload,!0))),(null===(t=e.rtcpFb)||void 0===t?void 0:t.some((e=>e.payload===o&&"nack"===e.type)))&&i.push(e.mid),e.fmtp.some((t=>t.payload===o&&(t.config.includes("sprop-stereo=1")&&n.push(e.mid),!0))))})),{stereoMids:n,nackMids:i}}(e);this.remoteStereoMids=t,this.remoteNackMids=n}else if("answer"===e.type){const i=Er.parse(null!==(t=e.sdp)&&void 0!==t?t:"");i.media.forEach((e=>{"audio"===e.type&&this.trackBitrates.some((t=>{if(!t.transceiver||e.mid!=t.transceiver.mid)return!1;let n=0;if(e.rtp.some((e=>e.codec.toUpperCase()===t.codec.toUpperCase()&&(n=e.payload,!0))),0===n)return!0;let i=!1;for(const s of e.fmtp)if(s.payload===n){s.config=s.config.split(";").filter((e=>!e.includes("maxaveragebitrate"))).join(";"),t.maxbr>0&&(s.config+=";maxaveragebitrate=".concat(1e3*t.maxbr)),i=!0;break}return i||t.maxbr>0&&e.fmtp.push({payload:n,config:"maxaveragebitrate=".concat(1e3*t.maxbr)}),!0}))})),n=Er.write(i)}if(yield this.setMungedSDP(e,n,!0),this.pendingCandidates.forEach((e=>{this.pc.addIceCandidate(e)})),this.pendingCandidates=[],this.restartingIce=!1,this.renegotiate)this.renegotiate=!1,yield this.createAndSendOffer();else if("answer"===e.type&&(this.emit(Rr),e.sdp)){Er.parse(e.sdp).media.forEach((e=>{"video"===e.type&&this.emit(Ir,e.rtp)}))}}))}createAndSendOffer(e){return Qn(this,void 0,void 0,(function*(){var t;if(void 0===this.onOffer)return;if((null==e?void 0:e.iceRestart)&&(this.log.debug("restarting ICE",this.logContext),this.restartingIce=!0),this._pc&&"have-local-offer"===this._pc.signalingState){const t=this._pc.remoteDescription;if(!(null==e?void 0:e.iceRestart)||!t)return void(this.renegotiate=!0);yield this._pc.setRemoteDescription(t)}else if(!this._pc||"closed"===this._pc.signalingState)return void this.log.warn("could not createOffer with closed peer connection",this.logContext);this.log.debug("starting to negotiate",this.logContext);const n=yield this.pc.createOffer(e);this.log.debug("original offer",Object.assign({sdp:n.sdp},this.logContext));const i=Er.parse(null!==(t=n.sdp)&&void 0!==t?t:"");i.media.forEach((e=>{xr(e),"audio"===e.type?Dr(e,[],[]):"video"===e.type&&this.trackBitrates.some((t=>{if(!e.msid||!t.cid||!e.msid.includes(t.cid))return!1;let n=0;if(e.rtp.some((e=>e.codec.toUpperCase()===t.codec.toUpperCase()&&(n=e.payload,!0))),0===n)return!0;if(ro(t.codec)&&this.ensureVideoDDExtensionForSVC(e,i),"av1"!==t.codec)return!0;const s=Math.round(.7*t.maxbr);for(const t of e.fmtp)if(t.payload===n){t.config.includes("x-google-start-bitrate")||(t.config+=";x-google-start-bitrate=".concat(s));break}return!0}))})),yield this.setMungedSDP(n,Er.write(i)),this.onOffer(n)}))}createAndSetAnswer(){return Qn(this,void 0,void 0,(function*(){var e;const t=yield this.pc.createAnswer(),n=Er.parse(null!==(e=t.sdp)&&void 0!==e?e:"");return n.media.forEach((e=>{xr(e),"audio"===e.type&&Dr(e,this.remoteStereoMids,this.remoteNackMids)})),yield this.setMungedSDP(t,Er.write(n)),t}))}createDataChannel(e,t){return this.pc.createDataChannel(e,t)}addTransceiver(e,t){return this.pc.addTransceiver(e,t)}addTrack(e){if(!this._pc)throw new Ds("PC closed, cannot add track");return this._pc.addTrack(e)}setTrackCodecBitrate(e){this.trackBitrates.push(e)}setConfiguration(e){var t;if(!this._pc)throw new Ds("PC closed, cannot configure");return null===(t=this._pc)||void 0===t?void 0:t.setConfiguration(e)}canRemoveTrack(){var e;return!!(null===(e=this._pc)||void 0===e?void 0:e.removeTrack)}removeTrack(e){var t;return null===(t=this._pc)||void 0===t?void 0:t.removeTrack(e)}getConnectionState(){var e,t;return null!==(t=null===(e=this._pc)||void 0===e?void 0:e.connectionState)&&void 0!==t?t:"closed"}getICEConnectionState(){var e,t;return null!==(t=null===(e=this._pc)||void 0===e?void 0:e.iceConnectionState)&&void 0!==t?t:"closed"}getSignallingState(){var e,t;return null!==(t=null===(e=this._pc)||void 0===e?void 0:e.signalingState)&&void 0!==t?t:"closed"}getTransceivers(){var e,t;return null!==(t=null===(e=this._pc)||void 0===e?void 0:e.getTransceivers())&&void 0!==t?t:[]}getSenders(){var e,t;return null!==(t=null===(e=this._pc)||void 0===e?void 0:e.getSenders())&&void 0!==t?t:[]}getLocalDescription(){var e;return null===(e=this._pc)||void 0===e?void 0:e.localDescription}getRemoteDescription(){var e;return null===(e=this.pc)||void 0===e?void 0:e.remoteDescription}getStats(){return this.pc.getStats()}getConnectedAddress(){return Qn(this,void 0,void 0,(function*(){var e;if(!this._pc)return;let t="";const n=new Map,i=new Map;if((yield this._pc.getStats()).forEach((e=>{switch(e.type){case"transport":t=e.selectedCandidatePairId;break;case"candidate-pair":""===t&&e.selected&&(t=e.id),n.set(e.id,e);break;case"remote-candidate":i.set(e.id,"".concat(e.address,":").concat(e.port))}})),""===t)return;const s=null===(e=n.get(t))||void 0===e?void 0:e.remoteCandidateId;return void 0!==s?i.get(s):void 0}))}setMungedSDP(e,t,n){return Qn(this,void 0,void 0,(function*(){if(t){const i=e.sdp;e.sdp=t;try{return this.log.debug("setting munged ".concat(n?"remote":"local"," description"),this.logContext),void(n?yield this.pc.setRemoteDescription(e):yield this.pc.setLocalDescription(e))}catch(n){this.log.warn("not able to set ".concat(e.type,", falling back to unmodified sdp"),Object.assign(Object.assign({},this.logContext),{error:n,sdp:t})),e.sdp=i}}try{n?yield this.pc.setRemoteDescription(e):yield this.pc.setLocalDescription(e)}catch(t){let i="unknown error";t instanceof Error?i=t.message:"string"==typeof t&&(i=t);const s={error:i,sdp:e.sdp};throw!n&&this.pc.remoteDescription&&(s.remoteSdp=this.pc.remoteDescription),this.log.error("unable to set ".concat(e.type),Object.assign(Object.assign({},this.logContext),{fields:s})),new xs(i)}}))}ensureVideoDDExtensionForSVC(e,t){var n,i;if(!(null===(n=e.ext)||void 0===n?void 0:n.some((e=>e.uri===eo)))){if(0===this.ddExtID){let e=0;t.media.forEach((t=>{var n;"video"===t.type&&(null===(n=t.ext)||void 0===n||n.forEach((t=>{t.value>e&&(e=t.value)})))})),this.ddExtID=e+1}null===(i=e.ext)||void 0===i||i.push({value:this.ddExtID,uri:eo})}}}function Dr(e,t,n){let i=0;e.rtp.some((e=>"opus"===e.codec&&(i=e.payload,!0))),i>0&&(e.rtcpFb||(e.rtcpFb=[]),n.includes(e.mid)&&!e.rtcpFb.some((e=>e.payload===i&&"nack"===e.type))&&e.rtcpFb.push({payload:i,type:"nack"}),t.includes(e.mid)&&e.fmtp.some((e=>e.payload===i&&(e.config.includes("stereo=1")||(e.config+=";stereo=1"),!0))))}function xr(e){if(e.connection){const t=e.connection.ip.indexOf(":")>=0;(4===e.connection.version&&t||6===e.connection.version&&!t)&&(e.connection.ip="0.0.0.0",e.connection.version=4)}}const Mr="vp8",Nr={audioPreset:e.AudioPresets.music,dtx:!0,red:!0,forceStereo:!1,simulcast:!0,screenShareEncoding:$s.h1080fps15.encoding,stopMicTrackOnMute:!1,videoCodec:Mr,backupCodec:!0},_r={deviceId:"default",autoGainControl:!0,echoCancellation:!0,noiseSuppression:!0,voiceIsolation:!0},Ar={deviceId:"default",resolution:Xs.h720.resolution},Lr={adaptiveStream:!1,dynacast:!1,stopLocalTrackOnUnpublish:!0,reconnectPolicy:new Jn,disconnectOnPageLeave:!0,webAudioMix:!1},Ur={autoSubscribe:!0,maxRetries:1,peerConnectionTimeout:15e3,websocketTimeout:15e3};var jr;!function(e){e[e.NEW=0]="NEW",e[e.CONNECTING=1]="CONNECTING",e[e.CONNECTED=2]="CONNECTED",e[e.FAILED=3]="FAILED",e[e.CLOSING=4]="CLOSING",e[e.CLOSED=5]="CLOSED"}(jr||(jr={}));class Fr{get needsPublisher(){return this.isPublisherConnectionRequired}get needsSubscriber(){return this.isSubscriberConnectionRequired}get currentState(){return this.state}constructor(t,n,i){var o;this.peerConnectionTimeout=Ur.peerConnectionTimeout,this.log=qn,this.updateState=()=>{var e;const t=this.state,n=this.requiredTransports.map((e=>e.getConnectionState()));n.every((e=>"connected"===e))?this.state=jr.CONNECTED:n.some((e=>"failed"===e))?this.state=jr.FAILED:n.some((e=>"connecting"===e))?this.state=jr.CONNECTING:n.every((e=>"closed"===e))?this.state=jr.CLOSED:n.some((e=>"closed"===e))?this.state=jr.CLOSING:n.every((e=>"new"===e))&&(this.state=jr.NEW),t!==this.state&&(this.log.debug("pc state change: from ".concat(jr[t]," to ").concat(jr[this.state]),this.logContext),null===(e=this.onStateChange)||void 0===e||e.call(this,this.state,this.publisher.getConnectionState(),this.subscriber.getConnectionState()))},this.log=Wn(null!==(o=i.loggerName)&&void 0!==o?o:e.LoggerNames.PCManager),this.loggerOptions=i,this.isPublisherConnectionRequired=!n,this.isSubscriberConnectionRequired=n,this.publisher=new Or(t,i),this.subscriber=new Or(t,i),this.publisher.onConnectionStateChange=this.updateState,this.subscriber.onConnectionStateChange=this.updateState,this.publisher.onIceConnectionStateChange=this.updateState,this.subscriber.onIceConnectionStateChange=this.updateState,this.publisher.onSignalingStatechange=this.updateState,this.subscriber.onSignalingStatechange=this.updateState,this.publisher.onIceCandidate=e=>{var t;null===(t=this.onIceCandidate)||void 0===t||t.call(this,e,Vt.PUBLISHER)},this.subscriber.onIceCandidate=e=>{var t;null===(t=this.onIceCandidate)||void 0===t||t.call(this,e,Vt.SUBSCRIBER)},this.subscriber.onDataChannel=e=>{var t;null===(t=this.onDataChannel)||void 0===t||t.call(this,e)},this.subscriber.onTrack=e=>{var t;null===(t=this.onTrack)||void 0===t||t.call(this,e)},this.publisher.onOffer=e=>{var t;null===(t=this.onPublisherOffer)||void 0===t||t.call(this,e)},this.state=jr.NEW,this.connectionLock=new s,this.remoteOfferLock=new s}get logContext(){var e,t;return Object.assign({},null===(t=(e=this.loggerOptions).loggerContextCb)||void 0===t?void 0:t.call(e))}requirePublisher(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.isPublisherConnectionRequired=e,this.updateState()}requireSubscriber(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.isSubscriberConnectionRequired=e,this.updateState()}createAndSendPublisherOffer(e){return this.publisher.createAndSendOffer(e)}setPublisherAnswer(e){return this.publisher.setRemoteDescription(e)}removeTrack(e){return this.publisher.removeTrack(e)}close(){return Qn(this,void 0,void 0,(function*(){if(this.publisher&&"closed"!==this.publisher.getSignallingState()){const e=this.publisher;for(const t of e.getSenders())try{e.canRemoveTrack()&&e.removeTrack(t)}catch(e){this.log.warn("could not removeTrack",Object.assign(Object.assign({},this.logContext),{error:e}))}}yield Promise.all([this.publisher.close(),this.subscriber.close()]),this.updateState()}))}triggerIceRestart(){return Qn(this,void 0,void 0,(function*(){this.subscriber.restartingIce=!0,this.needsPublisher&&(yield this.createAndSendPublisherOffer({iceRestart:!0}))}))}addIceCandidate(e,t){return Qn(this,void 0,void 0,(function*(){t===Vt.PUBLISHER?yield this.publisher.addIceCandidate(e):yield this.subscriber.addIceCandidate(e)}))}createSubscriberAnswerFromOffer(e){return Qn(this,void 0,void 0,(function*(){this.log.debug("received server offer",Object.assign(Object.assign({},this.logContext),{RTCSdpType:e.type,sdp:e.sdp,signalingState:this.subscriber.getSignallingState().toString()}));const t=yield this.remoteOfferLock.lock();try{yield this.subscriber.setRemoteDescription(e);return yield this.subscriber.createAndSetAnswer()}finally{t()}}))}updateConfiguration(e,t){this.publisher.setConfiguration(e),this.subscriber.setConfiguration(e),t&&this.triggerIceRestart()}ensurePCTransportConnection(e,t){return Qn(this,void 0,void 0,(function*(){var n;const i=yield this.connectionLock.lock();try{this.isPublisherConnectionRequired&&"connected"!==this.publisher.getConnectionState()&&"connecting"!==this.publisher.getConnectionState()&&(this.log.debug("negotiation required, start negotiating",this.logContext),this.publisher.negotiate()),yield Promise.all(null===(n=this.requiredTransports)||void 0===n?void 0:n.map((n=>this.ensureTransportConnected(n,e,t))))}finally{i()}}))}negotiate(e){return Qn(this,void 0,void 0,(function*(){return new Promise(((t,n)=>Qn(this,void 0,void 0,(function*(){const i=setTimeout((()=>{n("negotiation timed out")}),this.peerConnectionTimeout);e.signal.addEventListener("abort",(()=>{clearTimeout(i),n("negotiation aborted")})),this.publisher.once(Pr,(()=>{e.signal.aborted||this.publisher.once(Rr,(()=>{clearTimeout(i),t()}))})),yield this.publisher.negotiate((e=>{clearTimeout(i),n(e)}))}))))}))}addPublisherTransceiver(e,t){return this.publisher.addTransceiver(e,t)}addPublisherTrack(e){return this.publisher.addTrack(e)}createPublisherDataChannel(e,t){return this.publisher.createDataChannel(e,t)}getConnectedAddress(e){return e===Vt.PUBLISHER||e===Vt.SUBSCRIBER?this.publisher.getConnectedAddress():this.requiredTransports[0].getConnectedAddress()}get requiredTransports(){const e=[];return this.isPublisherConnectionRequired&&e.push(this.publisher),this.isSubscriberConnectionRequired&&e.push(this.subscriber),e}ensureTransportConnected(t,n){return Qn(this,arguments,void 0,(function(t,n){var i=this;let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.peerConnectionTimeout;return function*(){if("connected"!==t.getConnectionState())return new Promise(((t,o)=>Qn(i,void 0,void 0,(function*(){const i=()=>{this.log.warn("abort transport connection",this.logContext),Vs.clearTimeout(r),o(new Ps("room connection has been cancelled",e.ConnectionErrorReason.Cancelled))};(null==n?void 0:n.signal.aborted)&&i(),null==n||n.signal.addEventListener("abort",i);const r=Vs.setTimeout((()=>{null==n||n.signal.removeEventListener("abort",i),o(new Ps("could not establish pc connection",e.ConnectionErrorReason.InternalError))}),s);for(;this.state!==jr.CONNECTED;)if(yield to(50),null==n?void 0:n.signal.aborted)return void o(new Ps("room connection has been cancelled",e.ConnectionErrorReason.Cancelled));Vs.clearTimeout(r),null==n||n.signal.removeEventListener("abort",i),t()}))))}()}))}}class Br extends Error{constructor(e,t,n){super(t),this.code=e,this.message=qr(t,Br.MAX_MESSAGE_BYTES),this.data=n?qr(n,Br.MAX_DATA_BYTES):void 0}static fromProto(e){return new Br(e.code,e.message,e.data)}toProto(){return new wt({code:this.code,message:this.message,data:this.data})}static builtIn(e,t){return new Br(Br.ErrorCode[e],Br.ErrorMessage[e],t)}}Br.MAX_MESSAGE_BYTES=256,Br.MAX_DATA_BYTES=15360,Br.ErrorCode={APPLICATION_ERROR:1500,CONNECTION_TIMEOUT:1501,RESPONSE_TIMEOUT:1502,RECIPIENT_DISCONNECTED:1503,RESPONSE_PAYLOAD_TOO_LARGE:1504,SEND_FAILED:1505,UNSUPPORTED_METHOD:1400,RECIPIENT_NOT_FOUND:1401,REQUEST_PAYLOAD_TOO_LARGE:1402,UNSUPPORTED_SERVER:1403,UNSUPPORTED_VERSION:1404},Br.ErrorMessage={APPLICATION_ERROR:"Application error in method handler",CONNECTION_TIMEOUT:"Connection timeout",RESPONSE_TIMEOUT:"Response timeout",RECIPIENT_DISCONNECTED:"Recipient disconnected",RESPONSE_PAYLOAD_TOO_LARGE:"Response payload too large",SEND_FAILED:"Failed to send",UNSUPPORTED_METHOD:"Method not supported at destination",RECIPIENT_NOT_FOUND:"Recipient not found",REQUEST_PAYLOAD_TOO_LARGE:"Request payload too large",UNSUPPORTED_SERVER:"RPC not supported by server",UNSUPPORTED_VERSION:"Unsupported RPC version"};function Vr(e){return(new TextEncoder).encode(e).length}function qr(e,t){if(Vr(e)<=t)return e;let n=0,i=e.length;const s=new TextEncoder;for(;n<i;){const o=Math.floor((n+i+1)/2);s.encode(e.slice(0,o)).length<=t?n=o:i=o-1}return e.slice(0,n)}const Kr=2e3;function Wr(e,t){if(!t)return 0;let n,i;return"bytesReceived"in e?(n=e.bytesReceived,i=t.bytesReceived):"bytesSent"in e&&(n=e.bytesSent,i=t.bytesSent),void 0===n||void 0===i||void 0===e.timestamp||void 0===t.timestamp?0:8*(n-i)*1e3/(e.timestamp-t.timestamp)}class Hr extends Ks{get sender(){return this._sender}set sender(e){this._sender=e}get constraints(){return this._constraints}constructor(t,n,i){let o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];super(t,n,arguments.length>4?arguments[4]:void 0),this.manuallyStopped=!1,this._isUpstreamPaused=!1,this.handleTrackMuteEvent=()=>this.debouncedTrackMuteHandler().catch((()=>this.log.debug("track mute bounce got cancelled by an unmute event",this.logContext))),this.debouncedTrackMuteHandler=wr((()=>Qn(this,void 0,void 0,(function*(){yield this.pauseUpstream()}))),5e3),this.handleTrackUnmuteEvent=()=>Qn(this,void 0,void 0,(function*(){this.debouncedTrackMuteHandler.cancel("unmute"),yield this.resumeUpstream()})),this.handleEnded=()=>{this.isInBackground&&(this.reacquireTrack=!0),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent),this.emit(e.TrackEvent.Ended,this)},this.reacquireTrack=!1,this.providedByUser=o,this.muteLock=new s,this.pauseUpstreamLock=new s,this.processorLock=new s,this.restartLock=new s,this.setMediaStreamTrack(t,!0),this._constraints=t.getConstraints(),i&&(this._constraints=i)}get id(){return this._mediaStreamTrack.id}get dimensions(){if(this.kind!==Ks.Kind.Video)return;const{width:e,height:t}=this._mediaStreamTrack.getSettings();return e&&t?{width:e,height:t}:void 0}get isUpstreamPaused(){return this._isUpstreamPaused}get isUserProvided(){return this.providedByUser}get mediaStreamTrack(){var e,t;return null!==(t=null===(e=this.processor)||void 0===e?void 0:e.processedTrack)&&void 0!==t?t:this._mediaStreamTrack}get isLocal(){return!0}getSourceTrackSettings(){return this._mediaStreamTrack.getSettings()}setMediaStreamTrack(e,t){return Qn(this,void 0,void 0,(function*(){var n;if(e===this._mediaStreamTrack&&!t)return;let i;if(this._mediaStreamTrack&&(this.attachedElements.forEach((e=>{Hs(this._mediaStreamTrack,e)})),this.debouncedTrackMuteHandler.cancel("new-track"),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent)),this.mediaStream=new MediaStream([e]),e&&(e.addEventListener("ended",this.handleEnded),e.addEventListener("mute",this.handleTrackMuteEvent),e.addEventListener("unmute",this.handleTrackUnmuteEvent),this._constraints=e.getConstraints()),this.processor&&e){const t=yield this.processorLock.lock();try{if(this.log.debug("restarting processor",this.logContext),"unknown"===this.kind)throw TypeError("cannot set processor on track of unknown kind");this.processorElement&&(Ws(e,this.processorElement),this.processorElement.muted=!0),yield this.processor.restart({track:e,kind:this.kind,element:this.processorElement}),i=this.processor.processedTrack}finally{t()}}this.sender&&"closed"!==(null===(n=this.sender.transport)||void 0===n?void 0:n.state)&&(yield this.sender.replaceTrack(null!=i?i:e)),this.providedByUser||this._mediaStreamTrack===e||this._mediaStreamTrack.stop(),this._mediaStreamTrack=e,e&&(this._mediaStreamTrack.enabled=!this.isMuted,yield this.resumeUpstream(),this.attachedElements.forEach((t=>{Ws(null!=i?i:e,t)})))}))}waitForDimensions(){return Qn(this,arguments,void 0,(function(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3;return function*(){var n;if(e.kind===Ks.Kind.Audio)throw new Error("cannot get dimensions for audio tracks");"iOS"===(null===(n=Ls())||void 0===n?void 0:n.os)&&(yield to(10));const i=Date.now();for(;Date.now()-i<t;){const t=e.dimensions;if(t)return t;yield to(50)}throw new Is("unable to get track dimensions after timeout")}()}))}setDeviceId(e){return Qn(this,void 0,void 0,(function*(){return this._constraints.deviceId===e&&this._mediaStreamTrack.getSettings().deviceId===Mo(e)||(this._constraints.deviceId=e,!!this.isMuted||(yield this.restartTrack(),Mo(e)===this._mediaStreamTrack.getSettings().deviceId))}))}getDeviceId(){return Qn(this,arguments,void 0,(function(){var e=this;let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return function*(){if(e.source===Ks.Source.ScreenShare)return;const{deviceId:n,groupId:i}=e._mediaStreamTrack.getSettings(),s=e.kind===Ks.Kind.Audio?"audioinput":"videoinput";return t?or.getInstance().normalizeDeviceId(s,n,i):n}()}))}mute(){return Qn(this,void 0,void 0,(function*(){return this.setTrackMuted(!0),this}))}unmute(){return Qn(this,void 0,void 0,(function*(){return this.setTrackMuted(!1),this}))}replaceTrack(e,t){return Qn(this,void 0,void 0,(function*(){if(!this.sender)throw new Is("unable to replace an unpublished track");let n,i;return"boolean"==typeof t?n=t:void 0!==t&&(n=t.userProvidedTrack,i=t.stopProcessor),this.providedByUser=null==n||n,this.log.debug("replace MediaStreamTrack",this.logContext),yield this.setMediaStreamTrack(e),i&&this.processor&&(yield this.stopProcessor()),this}))}restart(t){return Qn(this,void 0,void 0,(function*(){this.manuallyStopped=!1;const n=yield this.restartLock.lock();try{t||(t=this._constraints);const{deviceId:n}=t,i=function(e,t){var n={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var s=0;for(i=Object.getOwnPropertySymbols(e);s<i.length;s++)t.indexOf(i[s])<0&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(n[i[s]]=e[i[s]])}return n}(t,["deviceId"]);this.log.debug("restarting track with constraints",Object.assign(Object.assign({},this.logContext),{constraints:t}));const s={audio:!1,video:!1};this.kind===Ks.Kind.Video?s.video=!n||{deviceId:n}:s.audio=!n||{deviceId:n},this.attachedElements.forEach((e=>{Hs(this.mediaStreamTrack,e)})),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.stop();const o=(yield navigator.mediaDevices.getUserMedia(s)).getTracks()[0];return yield o.applyConstraints(i),o.addEventListener("ended",this.handleEnded),this.log.debug("re-acquired MediaStreamTrack",this.logContext),yield this.setMediaStreamTrack(o),this._constraints=t,this.emit(e.TrackEvent.Restarted,this),this.manuallyStopped&&(this.log.warn("track was stopped during a restart, stopping restarted track",this.logContext),this.stop()),this}finally{n()}}))}setTrackMuted(t){this.log.debug("setting ".concat(this.kind," track ").concat(t?"muted":"unmuted"),this.logContext),this.isMuted===t&&this._mediaStreamTrack.enabled!==t||(this.isMuted=t,this._mediaStreamTrack.enabled=!t,this.emit(t?e.TrackEvent.Muted:e.TrackEvent.Unmuted,this))}get needsReAcquisition(){return"live"!==this._mediaStreamTrack.readyState||this._mediaStreamTrack.muted||!this._mediaStreamTrack.enabled||this.reacquireTrack}handleAppVisibilityChanged(){const e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return Qn(this,void 0,void 0,(function*(){yield e.handleAppVisibilityChanged.call(this),po()&&(this.log.debug("visibility changed, is in Background: ".concat(this.isInBackground),this.logContext),this.isInBackground||!this.needsReAcquisition||this.isUserProvided||this.isMuted||(this.log.debug("track needs to be reacquired, restarting ".concat(this.source),this.logContext),yield this.restart(),this.reacquireTrack=!1))}))}stop(){var e;this.manuallyStopped=!0,super.stop(),this._mediaStreamTrack.removeEventListener("ended",this.handleEnded),this._mediaStreamTrack.removeEventListener("mute",this.handleTrackMuteEvent),this._mediaStreamTrack.removeEventListener("unmute",this.handleTrackUnmuteEvent),null===(e=this.processor)||void 0===e||e.destroy(),this.processor=void 0}pauseUpstream(){return Qn(this,void 0,void 0,(function*(){var t;const n=yield this.pauseUpstreamLock.lock();try{if(!0===this._isUpstreamPaused)return;if(!this.sender)return void this.log.warn("unable to pause upstream for an unpublished track",this.logContext);this._isUpstreamPaused=!0,this.emit(e.TrackEvent.UpstreamPaused,this);const n=Ls();if("Safari"===(null==n?void 0:n.name)&&yo(n.version,"12.0")<0)throw new Rs("pauseUpstream is not supported on Safari < 12.");"closed"!==(null===(t=this.sender.transport)||void 0===t?void 0:t.state)&&(yield this.sender.replaceTrack(null))}finally{n()}}))}resumeUpstream(){return Qn(this,void 0,void 0,(function*(){var t;const n=yield this.pauseUpstreamLock.lock();try{if(!1===this._isUpstreamPaused)return;if(!this.sender)return void this.log.warn("unable to resume upstream for an unpublished track",this.logContext);this._isUpstreamPaused=!1,this.emit(e.TrackEvent.UpstreamResumed,this),"closed"!==(null===(t=this.sender.transport)||void 0===t?void 0:t.state)&&(yield this.sender.replaceTrack(this.mediaStreamTrack))}finally{n()}}))}getRTCStatsReport(){return Qn(this,void 0,void 0,(function*(){var e;if(!(null===(e=this.sender)||void 0===e?void 0:e.getStats))return;return yield this.sender.getStats()}))}setProcessor(t){return Qn(this,arguments,void 0,(function(t){var n=this;let i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return function*(){var s;const o=yield n.processorLock.lock();try{n.log.debug("setting up processor",n.logContext);const o=document.createElement(n.kind),r={kind:n.kind,track:n._mediaStreamTrack,element:o,audioContext:n.audioContext};if(yield t.init(r),n.log.debug("processor initialized",n.logContext),n.processor&&(yield n.stopProcessor()),"unknown"===n.kind)throw TypeError("cannot set processor on track of unknown kind");if(Ws(n._mediaStreamTrack,o),o.muted=!0,o.play().catch((e=>n.log.error("failed to play processor element",Object.assign(Object.assign({},n.logContext),{error:e})))),n.processor=t,n.processorElement=o,n.processor.processedTrack){for(const e of n.attachedElements)e!==n.processorElement&&i&&(Hs(n._mediaStreamTrack,e),Ws(n.processor.processedTrack,e));yield null===(s=n.sender)||void 0===s?void 0:s.replaceTrack(n.processor.processedTrack)}n.emit(e.TrackEvent.TrackProcessorUpdate,n.processor)}finally{o()}}()}))}getProcessor(){return this.processor}stopProcessor(){return Qn(this,arguments,void 0,(function(){var t=this;let n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return function*(){var i,s;t.processor&&(t.log.debug("stopping processor",t.logContext),null===(i=t.processor.processedTrack)||void 0===i||i.stop(),yield t.processor.destroy(),t.processor=void 0,n||(null===(s=t.processorElement)||void 0===s||s.remove(),t.processorElement=void 0),yield t._mediaStreamTrack.applyConstraints(t._constraints),yield t.setMediaStreamTrack(t._mediaStreamTrack,!0),t.emit(e.TrackEvent.TrackProcessorUpdate))}()}))}}class Gr extends Hr{get enhancedNoiseCancellation(){return this.isKrispNoiseFilterEnabled}constructor(t,n){let i=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],s=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;super(t,Ks.Kind.Audio,n,i,o),this.stopOnMute=!1,this.isKrispNoiseFilterEnabled=!1,this.monitorSender=()=>Qn(this,void 0,void 0,(function*(){if(!this.sender)return void(this._currentBitrate=0);let e;try{e=yield this.getSenderStats()}catch(e){return void this.log.error("could not get audio sender stats",Object.assign(Object.assign({},this.logContext),{error:e}))}e&&this.prevStats&&(this._currentBitrate=Wr(e,this.prevStats)),this.prevStats=e})),this.handleKrispNoiseFilterEnable=()=>{this.isKrispNoiseFilterEnabled=!0,this.log.debug("Krisp noise filter enabled",this.logContext),this.emit(e.TrackEvent.AudioTrackFeatureUpdate,this,nt.TF_ENHANCED_NOISE_CANCELLATION,!0)},this.handleKrispNoiseFilterDisable=()=>{this.isKrispNoiseFilterEnabled=!1,this.log.debug("Krisp noise filter disabled",this.logContext),this.emit(e.TrackEvent.AudioTrackFeatureUpdate,this,nt.TF_ENHANCED_NOISE_CANCELLATION,!1)},this.audioContext=s,this.checkForSilence()}mute(){const e=Object.create(null,{mute:{get:()=>super.mute}});return Qn(this,void 0,void 0,(function*(){const t=yield this.muteLock.lock();try{return this.isMuted?(this.log.debug("Track already muted",this.logContext),this):(this.source===Ks.Source.Microphone&&this.stopOnMute&&!this.isUserProvided&&(this.log.debug("stopping mic track",this.logContext),this._mediaStreamTrack.stop()),yield e.mute.call(this),this)}finally{t()}}))}unmute(){const e=Object.create(null,{unmute:{get:()=>super.unmute}});return Qn(this,void 0,void 0,(function*(){const t=yield this.muteLock.lock();try{if(!this.isMuted)return this.log.debug("Track already unmuted",this.logContext),this;const t=this._constraints.deviceId&&this._mediaStreamTrack.getSettings().deviceId!==Mo(this._constraints.deviceId);return this.source!==Ks.Source.Microphone||!this.stopOnMute&&"ended"!==this._mediaStreamTrack.readyState&&!t||this.isUserProvided||(this.log.debug("reacquiring mic track",this.logContext),yield this.restartTrack()),yield e.unmute.call(this),this}finally{t()}}))}restartTrack(e){return Qn(this,void 0,void 0,(function*(){let t;if(e){const n=Jo({audio:e});"boolean"!=typeof n.audio&&(t=n.audio)}yield this.restart(t)}))}restart(e){const t=Object.create(null,{restart:{get:()=>super.restart}});return Qn(this,void 0,void 0,(function*(){const n=yield t.restart.call(this,e);return this.checkForSilence(),n}))}startMonitor(){mo()&&(this.monitorInterval||(this.monitorInterval=setInterval((()=>{this.monitorSender()}),Kr)))}setProcessor(t){return Qn(this,void 0,void 0,(function*(){var n;const i=yield this.processorLock.lock();try{if(!go()&&!this.audioContext)throw Error("Audio context needs to be set on LocalAudioTrack in order to enable processors");this.processor&&(yield this.stopProcessor());const i={kind:this.kind,track:this._mediaStreamTrack,audioContext:this.audioContext};this.log.debug("setting up audio processor ".concat(t.name),this.logContext),yield t.init(i),this.processor=t,this.processor.processedTrack&&(yield null===(n=this.sender)||void 0===n?void 0:n.replaceTrack(this.processor.processedTrack),this.processor.processedTrack.addEventListener("enable-lk-krisp-noise-filter",this.handleKrispNoiseFilterEnable),this.processor.processedTrack.addEventListener("disable-lk-krisp-noise-filter",this.handleKrispNoiseFilterDisable)),this.emit(e.TrackEvent.TrackProcessorUpdate,this.processor)}finally{i()}}))}setAudioContext(e){this.audioContext=e}getSenderStats(){return Qn(this,void 0,void 0,(function*(){var e;if(!(null===(e=this.sender)||void 0===e?void 0:e.getStats))return;let t;return(yield this.sender.getStats()).forEach((e=>{"outbound-rtp"===e.type&&(t={type:"audio",streamId:e.id,packetsSent:e.packetsSent,packetsLost:e.packetsLost,bytesSent:e.bytesSent,timestamp:e.timestamp,roundTripTime:e.roundTripTime,jitter:e.jitter})})),t}))}checkForSilence(){return Qn(this,void 0,void 0,(function*(){const t=yield Qo(this);return t&&(this.isMuted||this.log.warn("silence detected on local audio track",this.logContext),this.emit(e.TrackEvent.AudioSilenceDetected)),t}))}}function zr(e,t,n){switch(e.kind){case"audio":return new Gr(e,t,!1,void 0,n);case"video":return new ra(e,t,!1,n);default:throw new Is("unsupported track type: ".concat(e.kind))}}const Jr=Object.values(Xs),Qr=Object.values(Zs),Yr=Object.values($s),Xr=[Xs.h180,Xs.h360],Zr=[Zs.h180,Zs.h360],$r=e=>[{scaleResolutionDownBy:2,fps:e.encoding.maxFramerate}].map((t=>{var n,i;return new Gs(Math.floor(e.width/t.scaleResolutionDownBy),Math.floor(e.height/t.scaleResolutionDownBy),Math.max(15e4,Math.floor(e.encoding.maxBitrate/(Math.pow(t.scaleResolutionDownBy,2)*((null!==(n=e.encoding.maxFramerate)&&void 0!==n?n:30)/(null!==(i=t.fps)&&void 0!==i?i:30))))),t.fps,e.encoding.priority)})),ea=["q","h","f"];function ta(e,t,n,i){var s,o;let r=null==i?void 0:i.videoEncoding;e&&(r=null==i?void 0:i.screenShareEncoding);const a=null==i?void 0:i.simulcast,c=null==i?void 0:i.scalabilityMode,d=null==i?void 0:i.videoCodec;if(!r&&!a&&!c||!t||!n)return[{}];r||(r=function(e,t,n,i){const s=function(e,t,n){if(e)return Yr;const i=t>n?t/n:n/t;if(Math.abs(i-16/9)<Math.abs(i-4/3))return Jr;return Qr}(e,t,n);let{encoding:o}=s[0];const r=Math.max(t,n);for(let e=0;e<s.length;e+=1){const t=s[e];if(o=t.encoding,t.width>=r)break}if(i)switch(i){case"av1":o=Object.assign({},o),o.maxBitrate=.7*o.maxBitrate;break;case"vp9":o=Object.assign({},o),o.maxBitrate=.85*o.maxBitrate}return o}(e,t,n,d),qn.debug("using video encoding",r));const l=r.maxFramerate,u=new Gs(t,n,r.maxBitrate,r.maxFramerate,r.priority);if(c&&ro(d)){const e=new oa(c),t=[];if(e.spatial>3)throw new Error("unsupported scalabilityMode: ".concat(c));const n=Ls();if(uo()||go()||"Chrome"===(null==n?void 0:n.name)&&yo(null==n?void 0:n.version,"113")<0){const n="h"==e.suffix?2:3;for(let i=0;i<e.spatial;i+=1)t.push({rid:ea[2-i],maxBitrate:r.maxBitrate/Math.pow(n,i),maxFramerate:u.encoding.maxFramerate});t[0].scalabilityMode=c}else t.push({maxBitrate:r.maxBitrate,maxFramerate:u.encoding.maxFramerate,scalabilityMode:c});return u.encoding.priority&&(t[0].priority=u.encoding.priority,t[0].networkPriority=u.encoding.priority),qn.debug("using svc encoding",{encodings:t}),t}if(!a)return[r];let h,p=[];if(p=e?null!==(s=sa(null==i?void 0:i.screenShareSimulcastLayers))&&void 0!==s?s:na(e,u):null!==(o=sa(null==i?void 0:i.videoSimulcastLayers))&&void 0!==o?o:na(e,u),p.length>0){const e=p[0];p.length>1&&([,h]=p);const i=Math.max(t,n);if(i>=960&&h)return ia(t,n,[e,h,u],l);if(i>=480)return ia(t,n,[e,u],l)}return ia(t,n,[u])}function na(e,t){if(e)return $r(t);const{width:n,height:i}=t,s=n>i?n/i:i/n;return Math.abs(s-16/9)<Math.abs(s-4/3)?Xr:Zr}function ia(e,t,n,i){const s=[];if(n.forEach(((n,o)=>{if(o>=ea.length)return;const r=Math.min(e,t),a={rid:ea[o],scaleResolutionDownBy:Math.max(1,r/Math.min(n.width,n.height)),maxBitrate:n.encoding.maxBitrate},c=i&&n.encoding.maxFramerate?Math.min(i,n.encoding.maxFramerate):n.encoding.maxFramerate;c&&(a.maxFramerate=c);const d=lo()||0===o;n.encoding.priority&&d&&(a.priority=n.encoding.priority,a.networkPriority=n.encoding.priority),s.push(a)})),go()&&"ios"===ko()){let e;s.forEach((t=>{e?t.maxFramerate&&t.maxFramerate>e&&(e=t.maxFramerate):e=t.maxFramerate}));let t=!0;s.forEach((n=>{var i;n.maxFramerate!=e&&(t&&(t=!1,qn.info("Simulcast on iOS React-Native requires all encodings to share the same framerate.")),qn.info('Setting framerate of encoding "'.concat(null!==(i=n.rid)&&void 0!==i?i:"",'" to ').concat(e)),n.maxFramerate=e)}))}return s}function sa(e){if(e)return e.sort(((e,t)=>{const{encoding:n}=e,{encoding:i}=t;return n.maxBitrate>i.maxBitrate?1:n.maxBitrate<i.maxBitrate?-1:n.maxBitrate===i.maxBitrate&&n.maxFramerate&&i.maxFramerate?n.maxFramerate>i.maxFramerate?1:-1:0}))}class oa{constructor(e){const t=e.match(/^L(\d)T(\d)(h|_KEY|_KEY_SHIFT){0,1}$/);if(!t)throw new Error("invalid scalability mode");if(this.spatial=parseInt(t[1]),this.temporal=parseInt(t[2]),t.length>3)switch(t[3]){case"h":case"_KEY":case"_KEY_SHIFT":this.suffix=t[3]}}toString(){var e;return"L".concat(this.spatial,"T").concat(this.temporal).concat(null!==(e=this.suffix)&&void 0!==e?e:"")}}class ra extends Hr{get sender(){return this._sender}set sender(e){this._sender=e,this.degradationPreference&&this.setDegradationPreference(this.degradationPreference)}constructor(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],i=arguments.length>3?arguments[3]:void 0;super(e,Ks.Kind.Video,t,n,i),this.simulcastCodecs=new Map,this.degradationPreference="balanced",this.monitorSender=()=>Qn(this,void 0,void 0,(function*(){if(!this.sender)return void(this._currentBitrate=0);let e;try{e=yield this.getSenderStats()}catch(e){return void this.log.error("could not get audio sender stats",Object.assign(Object.assign({},this.logContext),{error:e}))}const t=new Map(e.map((e=>[e.rid,e])));if(this.prevStats){let e=0;t.forEach(((t,n)=>{var i;const s=null===(i=this.prevStats)||void 0===i?void 0:i.get(n);e+=Wr(t,s)})),this._currentBitrate=e}this.prevStats=t})),this.senderLock=new s}get isSimulcast(){return!!(this.sender&&this.sender.getParameters().encodings.length>1)}startMonitor(e){var t;if(this.signalClient=e,!mo())return;const n=null===(t=this.sender)||void 0===t?void 0:t.getParameters();n&&(this.encodings=n.encodings),this.monitorInterval||(this.monitorInterval=setInterval((()=>{this.monitorSender()}),Kr))}stop(){this._mediaStreamTrack.getConstraints(),this.simulcastCodecs.forEach((e=>{e.mediaStreamTrack.stop()})),super.stop()}pauseUpstream(){const e=Object.create(null,{pauseUpstream:{get:()=>super.pauseUpstream}});return Qn(this,void 0,void 0,(function*(){var t,n,i,s,o;yield e.pauseUpstream.call(this);try{for(var r,a=!0,c=Xn(this.simulcastCodecs.values());!(t=(r=yield c.next()).done);a=!0){s=r.value,a=!1;const e=s;yield null===(o=e.sender)||void 0===o?void 0:o.replaceTrack(null)}}catch(e){n={error:e}}finally{try{a||t||!(i=c.return)||(yield i.call(c))}finally{if(n)throw n.error}}}))}resumeUpstream(){const e=Object.create(null,{resumeUpstream:{get:()=>super.resumeUpstream}});return Qn(this,void 0,void 0,(function*(){var t,n,i,s,o;yield e.resumeUpstream.call(this);try{for(var r,a=!0,c=Xn(this.simulcastCodecs.values());!(t=(r=yield c.next()).done);a=!0){s=r.value,a=!1;const e=s;yield null===(o=e.sender)||void 0===o?void 0:o.replaceTrack(e.mediaStreamTrack)}}catch(e){n={error:e}}finally{try{a||t||!(i=c.return)||(yield i.call(c))}finally{if(n)throw n.error}}}))}mute(){const e=Object.create(null,{mute:{get:()=>super.mute}});return Qn(this,void 0,void 0,(function*(){const t=yield this.muteLock.lock();try{return this.isMuted?(this.log.debug("Track already muted",this.logContext),this):(this.source!==Ks.Source.Camera||this.isUserProvided||(this.log.debug("stopping camera track",this.logContext),this._mediaStreamTrack.stop()),yield e.mute.call(this),this)}finally{t()}}))}unmute(){const e=Object.create(null,{unmute:{get:()=>super.unmute}});return Qn(this,void 0,void 0,(function*(){const t=yield this.muteLock.lock();try{return this.isMuted?(this.source!==Ks.Source.Camera||this.isUserProvided||(this.log.debug("reacquiring camera track",this.logContext),yield this.restartTrack()),yield e.unmute.call(this),this):(this.log.debug("Track already unmuted",this.logContext),this)}finally{t()}}))}setTrackMuted(e){super.setTrackMuted(e);for(const t of this.simulcastCodecs.values())t.mediaStreamTrack.enabled=!e}getSenderStats(){return Qn(this,void 0,void 0,(function*(){var e;if(!(null===(e=this.sender)||void 0===e?void 0:e.getStats))return[];const t=[],n=yield this.sender.getStats();return n.forEach((e=>{var i;if("outbound-rtp"===e.type){const s={type:"video",streamId:e.id,frameHeight:e.frameHeight,frameWidth:e.frameWidth,framesPerSecond:e.framesPerSecond,framesSent:e.framesSent,firCount:e.firCount,pliCount:e.pliCount,nackCount:e.nackCount,packetsSent:e.packetsSent,bytesSent:e.bytesSent,qualityLimitationReason:e.qualityLimitationReason,qualityLimitationDurations:e.qualityLimitationDurations,qualityLimitationResolutionChanges:e.qualityLimitationResolutionChanges,rid:null!==(i=e.rid)&&void 0!==i?i:e.id,retransmittedPacketsSent:e.retransmittedPacketsSent,targetBitrate:e.targetBitrate,timestamp:e.timestamp},o=n.get(e.remoteId);o&&(s.jitter=o.jitter,s.packetsLost=o.packetsLost,s.roundTripTime=o.roundTripTime),t.push(s)}})),t.sort(((e,t)=>{var n,i;return(null!==(n=t.frameWidth)&&void 0!==n?n:0)-(null!==(i=e.frameWidth)&&void 0!==i?i:0)})),t}))}setPublishingQuality(t){const n=[];for(let i=e.VideoQuality.LOW;i<=e.VideoQuality.HIGH;i+=1)n.push(new kn({quality:i,enabled:i<=t}));this.log.debug("setting publishing quality. max quality ".concat(t),this.logContext),this.setPublishingLayers(n)}restartTrack(e){return Qn(this,void 0,void 0,(function*(){var t,n,i,s,o;let r;if(e){const t=Jo({video:e});"boolean"!=typeof t.video&&(r=t.video)}yield this.restart(r);try{for(var a,c=!0,d=Xn(this.simulcastCodecs.values());!(t=(a=yield d.next()).done);c=!0){s=a.value,c=!1;const e=s;e.sender&&"closed"!==(null===(o=e.sender.transport)||void 0===o?void 0:o.state)&&(e.mediaStreamTrack=this.mediaStreamTrack.clone(),yield e.sender.replaceTrack(e.mediaStreamTrack))}}catch(e){n={error:e}}finally{try{c||t||!(i=d.return)||(yield i.call(d))}finally{if(n)throw n.error}}}))}setProcessor(e){const t=Object.create(null,{setProcessor:{get:()=>super.setProcessor}});return Qn(this,arguments,void 0,(function(e){var n=this;let i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return function*(){var s,o,r,a,c,d;if(yield t.setProcessor.call(n,e,i),null===(c=n.processor)||void 0===c?void 0:c.processedTrack)try{for(var l,u=!0,h=Xn(n.simulcastCodecs.values());!(s=(l=yield h.next()).done);u=!0){a=l.value,u=!1;const e=a;yield null===(d=e.sender)||void 0===d?void 0:d.replaceTrack(n.processor.processedTrack)}}catch(e){o={error:e}}finally{try{u||s||!(r=h.return)||(yield r.call(h))}finally{if(o)throw o.error}}}()}))}setDegradationPreference(e){return Qn(this,void 0,void 0,(function*(){if(this.degradationPreference=e,this.sender)try{this.log.debug("setting degradationPreference to ".concat(e),this.logContext);const t=this.sender.getParameters();t.degradationPreference=e,this.sender.setParameters(t)}catch(e){this.log.warn("failed to set degradationPreference",Object.assign({error:e},this.logContext))}}))}addSimulcastTrack(e,t){if(this.simulcastCodecs.has(e))return void this.log.error("".concat(e," already added, skipping adding simulcast codec"),this.logContext);const n={codec:e,mediaStreamTrack:this.mediaStreamTrack.clone(),sender:void 0,encodings:t};return this.simulcastCodecs.set(e,n),n}setSimulcastTrackSender(e,t){const n=this.simulcastCodecs.get(e);n&&(n.sender=t,setTimeout((()=>{this.subscribedCodecs&&this.setPublishingCodecs(this.subscribedCodecs)}),5e3))}setPublishingCodecs(e){return Qn(this,void 0,void 0,(function*(){var t,n,i,s,o,r,a;if(this.log.debug("setting publishing codecs",Object.assign(Object.assign({},this.logContext),{codecs:e,currentCodec:this.codec})),!this.codec&&e.length>0)return yield this.setPublishingLayers(e[0].qualities),[];this.subscribedCodecs=e;const c=[];try{for(t=!0,n=Xn(e);!(s=(i=yield n.next()).done);t=!0){a=i.value,t=!1;const e=a;if(this.codec&&this.codec!==e.codec){const t=this.simulcastCodecs.get(e.codec);if(this.log.debug("try setPublishingCodec for ".concat(e.codec),Object.assign(Object.assign({},this.logContext),{simulcastCodecInfo:t})),t&&t.sender)t.encodings&&(this.log.debug("try setPublishingLayersForSender ".concat(e.codec),this.logContext),yield aa(t.sender,t.encodings,e.qualities,this.senderLock,this.log,this.logContext));else for(const t of e.qualities)if(t.enabled){c.push(e.codec);break}}else yield this.setPublishingLayers(e.qualities)}}catch(e){o={error:e}}finally{try{t||s||!(r=n.return)||(yield r.call(n))}finally{if(o)throw o.error}}return c}))}setPublishingLayers(e){return Qn(this,void 0,void 0,(function*(){this.log.debug("setting publishing layers",Object.assign(Object.assign({},this.logContext),{qualities:e})),this.sender&&this.encodings&&(yield aa(this.sender,this.encodings,e,this.senderLock,this.log,this.logContext))}))}handleAppVisibilityChanged(){const e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return Qn(this,void 0,void 0,(function*(){yield e.handleAppVisibilityChanged.call(this),po()&&this.isInBackground&&this.source===Ks.Source.Camera&&(this._mediaStreamTrack.enabled=!1)}))}}function aa(e,t,n,i,s,o){return Qn(this,void 0,void 0,(function*(){const r=yield i.lock();s.debug("setPublishingLayersForSender",Object.assign(Object.assign({},o),{sender:e,qualities:n,senderEncodings:t}));try{const i=e.getParameters(),{encodings:r}=i;if(!r)return;if(r.length!==t.length)return void s.warn("cannot set publishing layers, encodings mismatch",Object.assign(Object.assign({},o),{encodings:r,senderEncodings:t}));let a=!1;!1&&r[0].scalabilityMode||r.forEach(((e,i)=>{var r;let c=null!==(r=e.rid)&&void 0!==r?r:"";""===c&&(c="q");const d=ca(c),l=n.find((e=>e.quality===d));l&&e.active!==l.enabled&&(a=!0,e.active=l.enabled,s.debug("setting layer ".concat(l.quality," to ").concat(e.active?"enabled":"disabled"),o),lo()&&(l.enabled?(e.scaleResolutionDownBy=t[i].scaleResolutionDownBy,e.maxBitrate=t[i].maxBitrate,e.maxFrameRate=t[i].maxFrameRate):(e.scaleResolutionDownBy=4,e.maxBitrate=10,e.maxFrameRate=2)))})),a&&(i.encodings=r,s.debug("setting encodings",Object.assign(Object.assign({},o),{encodings:i.encodings})),yield e.setParameters(i))}finally{r()}}))}function ca(t){switch(t){case"f":default:return e.VideoQuality.HIGH;case"h":return e.VideoQuality.MEDIUM;case"q":return e.VideoQuality.LOW}}function da(t,n,i,s){if(!i)return[new ht({quality:e.VideoQuality.HIGH,width:t,height:n,bitrate:0,ssrc:0})];if(s){const s=i[0].scalabilityMode,o=new oa(s),r=[],a="h"==o.suffix?1.5:2,c="h"==o.suffix?2:3;for(let s=0;s<o.spatial;s+=1)r.push(new ht({quality:Math.min(e.VideoQuality.HIGH,o.spatial-1)-s,width:Math.ceil(t/Math.pow(a,s)),height:Math.ceil(n/Math.pow(a,s)),bitrate:i[0].maxBitrate?Math.ceil(i[0].maxBitrate/Math.pow(c,s)):0,ssrc:0}));return r}return i.map((e=>{var i,s,o;const r=null!==(i=e.scaleResolutionDownBy)&&void 0!==i?i:1;let a=ca(null!==(s=e.rid)&&void 0!==s?s:"");return new ht({quality:a,width:Math.ceil(t/r),height:Math.ceil(n/r),bitrate:null!==(o=e.maxBitrate)&&void 0!==o?o:0,ssrc:0})}))}const la="_lossy",ua="_reliable",ha="leave-reconnect";var pa;!function(e){e[e.New=0]="New",e[e.Connected=1]="Connected",e[e.Disconnected=2]="Disconnected",e[e.Reconnecting=3]="Reconnecting",e[e.Closed=4]="Closed"}(pa||(pa={}));class ma extends ei.EventEmitter{get isClosed(){return this._isClosed}get pendingReconnect(){return!!this.reconnectTimeout}constructor(t){var n;super(),this.options=t,this.rtcConfig={},this.peerConnectionTimeout=Ur.peerConnectionTimeout,this.fullReconnectOnNext=!1,this.subscriberPrimary=!1,this.pcState=pa.New,this._isClosed=!0,this.pendingTrackResolvers={},this.reconnectAttempts=0,this.reconnectStart=0,this.attemptingReconnect=!1,this.joinAttempts=0,this.maxJoinAttempts=1,this.shouldFailNext=!1,this.log=qn,this.handleDataChannel=e=>Qn(this,[e],void 0,(function(e){var t=this;let{channel:n}=e;return function*(){if(n){if(n.label===ua)t.reliableDCSub=n;else{if(n.label!==la)return;t.lossyDCSub=n}t.log.debug("on data channel ".concat(n.id,", ").concat(n.label),t.logContext),n.onmessage=t.handleDataMessage}}()})),this.handleDataMessage=t=>Qn(this,void 0,void 0,(function*(){var n,i;const s=yield this.dataProcessLock.lock();try{let s;if(t.data instanceof ArrayBuffer)s=t.data;else{if(!(t.data instanceof Blob))return void this.log.error("unsupported data type",Object.assign(Object.assign({},this.logContext),{data:t.data}));s=yield t.data.arrayBuffer()}const o=pt.fromBinary(new Uint8Array(s));"speaker"===(null===(n=o.value)||void 0===n?void 0:n.case)?this.emit(e.EngineEvent.ActiveSpeakersUpdate,o.value.value.speakers):("user"===(null===(i=o.value)||void 0===i?void 0:i.case)&&function(e,t){const n=e.participantIdentity?e.participantIdentity:t.participantIdentity;e.participantIdentity=n,t.participantIdentity=n;const i=0!==e.destinationIdentities.length?e.destinationIdentities:t.destinationIdentities;e.destinationIdentities=i,t.destinationIdentities=i}(o,o.value.value),this.emit(e.EngineEvent.DataPacketReceived,o))}finally{s()}})),this.handleDataError=e=>{const t=0===e.currentTarget.maxRetransmits?"lossy":"reliable";if(e instanceof ErrorEvent&&e.error){const{error:n}=e.error;this.log.error("DataChannel error on ".concat(t,": ").concat(e.message),Object.assign(Object.assign({},this.logContext),{error:n}))}else this.log.error("Unknown DataChannel error on ".concat(t),Object.assign(Object.assign({},this.logContext),{event:e}))},this.handleBufferedAmountLow=e=>{const t=0===e.currentTarget.maxRetransmits?mt.LOSSY:mt.RELIABLE;this.updateAndEmitDCBufferStatus(t)},this.handleDisconnect=(t,n)=>{if(this._isClosed)return;this.log.warn("".concat(t," disconnected"),this.logContext),0===this.reconnectAttempts&&(this.reconnectStart=Date.now());const i=t=>{this.log.warn("could not recover connection after ".concat(this.reconnectAttempts," attempts, ").concat(t,"ms. giving up"),this.logContext),this.emit(e.EngineEvent.Disconnected),this.close()},s=Date.now()-this.reconnectStart;let o=this.getNextRetryDelay({elapsedMs:s,retryCount:this.reconnectAttempts});null!==o?(t===ha&&(o=0),this.log.debug("reconnecting in ".concat(o,"ms"),this.logContext),this.clearReconnectTimeout(),this.token&&this.regionUrlProvider&&this.regionUrlProvider.updateToken(this.token),this.reconnectTimeout=Vs.setTimeout((()=>this.attemptReconnect(n).finally((()=>this.reconnectTimeout=void 0))),o)):i(s)},this.waitForRestarted=()=>new Promise(((t,n)=>{this.pcState===pa.Connected&&t();const i=()=>{this.off(e.EngineEvent.Disconnected,s),t()},s=()=>{this.off(e.EngineEvent.Restarted,i),n()};this.once(e.EngineEvent.Restarted,i),this.once(e.EngineEvent.Disconnected,s)})),this.updateAndEmitDCBufferStatus=t=>{const n=this.isBufferStatusLow(t);void 0!==n&&n!==this.dcBufferStatus.get(t)&&(this.dcBufferStatus.set(t,n),this.emit(e.EngineEvent.DCBufferStatusChanged,n,t))},this.isBufferStatusLow=e=>{const t=this.dataChannelForKind(e);if(t)return t.bufferedAmount<=t.bufferedAmountLowThreshold},this.handleBrowserOnLine=()=>{this.client.currentState===dr.RECONNECTING&&(this.clearReconnectTimeout(),this.attemptReconnect(et.RR_SIGNAL_DISCONNECTED))},this.log=Wn(null!==(n=t.loggerName)&&void 0!==n?n:e.LoggerNames.Engine),this.loggerOptions={loggerName:t.loggerName,loggerContextCb:()=>this.logContext},this.client=new lr(void 0,this.loggerOptions),this.client.signalLatency=this.options.expSignalLatency,this.reconnectPolicy=this.options.reconnectPolicy,this.registerOnLineListener(),this.closingLock=new s,this.dataProcessLock=new s,this.dcBufferStatus=new Map([[mt.LOSSY,!0],[mt.RELIABLE,!0]]),this.client.onParticipantUpdate=t=>this.emit(e.EngineEvent.ParticipantUpdate,t),this.client.onConnectionQuality=t=>this.emit(e.EngineEvent.ConnectionQualityUpdate,t),this.client.onRoomUpdate=t=>this.emit(e.EngineEvent.RoomUpdate,t),this.client.onSubscriptionError=t=>this.emit(e.EngineEvent.SubscriptionError,t),this.client.onSubscriptionPermissionUpdate=t=>this.emit(e.EngineEvent.SubscriptionPermissionUpdate,t),this.client.onSpeakersChanged=t=>this.emit(e.EngineEvent.SpeakersChanged,t),this.client.onStreamStateUpdate=t=>this.emit(e.EngineEvent.StreamStateChanged,t),this.client.onRequestResponse=t=>this.emit(e.EngineEvent.SignalRequestResponse,t)}get logContext(){var e,t,n,i,s,o,r,a;return{room:null===(t=null===(e=this.latestJoinResponse)||void 0===e?void 0:e.room)||void 0===t?void 0:t.name,roomID:null===(i=null===(n=this.latestJoinResponse)||void 0===n?void 0:n.room)||void 0===i?void 0:i.sid,participant:null===(o=null===(s=this.latestJoinResponse)||void 0===s?void 0:s.participant)||void 0===o?void 0:o.identity,pID:null===(a=null===(r=this.latestJoinResponse)||void 0===r?void 0:r.participant)||void 0===a?void 0:a.sid}}join(t,n,i,s){return Qn(this,void 0,void 0,(function*(){this.url=t,this.token=n,this.signalOpts=i,this.maxJoinAttempts=i.maxRetries;try{this.joinAttempts+=1,this.setupSignalClientCallbacks();const e=yield this.client.join(t,n,i,s);return this._isClosed=!1,this.latestJoinResponse=e,this.subscriberPrimary=e.subscriberPrimary,this.pcManager||(yield this.configure(e)),this.subscriberPrimary&&!e.fastPublish||this.negotiate(),this.clientConfiguration=e.clientConfiguration,e}catch(o){if(o instanceof Ps&&o.reason===e.ConnectionErrorReason.ServerUnreachable&&(this.log.warn("Couldn't connect to server, attempt ".concat(this.joinAttempts," of ").concat(this.maxJoinAttempts),this.logContext),this.joinAttempts<this.maxJoinAttempts))return this.join(t,n,i,s);throw o}}))}close(){return Qn(this,void 0,void 0,(function*(){const t=yield this.closingLock.lock();if(this.isClosed)t();else try{this._isClosed=!0,this.joinAttempts=0,this.emit(e.EngineEvent.Closing),this.removeAllListeners(),this.deregisterOnLineListener(),this.clearPendingReconnect(),yield this.cleanupPeerConnections(),yield this.cleanupClient()}finally{t()}}))}cleanupPeerConnections(){return Qn(this,void 0,void 0,(function*(){var e;yield null===(e=this.pcManager)||void 0===e?void 0:e.close(),this.pcManager=void 0;const t=e=>{e&&(e.close(),e.onbufferedamountlow=null,e.onclose=null,e.onclosing=null,e.onerror=null,e.onmessage=null,e.onopen=null)};t(this.lossyDC),t(this.lossyDCSub),t(this.reliableDC),t(this.reliableDCSub),this.lossyDC=void 0,this.lossyDCSub=void 0,this.reliableDC=void 0,this.reliableDCSub=void 0}))}cleanupClient(){return Qn(this,void 0,void 0,(function*(){yield this.client.close(),this.client.resetCallbacks()}))}addTrack(t){if(this.pendingTrackResolvers[t.cid])throw new Is("a track with the same ID has already been published");return new Promise(((n,i)=>{const s=setTimeout((()=>{delete this.pendingTrackResolvers[t.cid],i(new Ps("publication of local track timed out, no response from server",e.ConnectionErrorReason.InternalError))}),1e4);this.pendingTrackResolvers[t.cid]={resolve:e=>{clearTimeout(s),n(e)},reject:()=>{clearTimeout(s),i(new Error("Cancelled publication by calling unpublish"))}},this.client.sendAddTrack(t)}))}removeTrack(e){if(e.track&&this.pendingTrackResolvers[e.track.id]){const{reject:t}=this.pendingTrackResolvers[e.track.id];t&&t(),delete this.pendingTrackResolvers[e.track.id]}try{return this.pcManager.removeTrack(e),!0}catch(e){this.log.warn("failed to remove track",Object.assign(Object.assign({},this.logContext),{error:e}))}return!1}updateMuteStatus(e,t){this.client.sendMuteTrack(e,t)}get dataSubscriberReadyState(){var e;return null===(e=this.reliableDCSub)||void 0===e?void 0:e.readyState}getConnectedServerAddress(){return Qn(this,void 0,void 0,(function*(){var e;return null===(e=this.pcManager)||void 0===e?void 0:e.getConnectedAddress()}))}setRegionUrlProvider(e){this.regionUrlProvider=e}configure(t){return Qn(this,void 0,void 0,(function*(){var n,i;if(this.pcManager&&this.pcManager.currentState!==jr.NEW)return;this.participantSid=null===(n=t.participant)||void 0===n?void 0:n.sid;const s=this.makeRTCConfiguration(t);var o;this.pcManager=new Fr(s,t.subscriberPrimary,this.loggerOptions),this.emit(e.EngineEvent.TransportsCreated,this.pcManager.publisher,this.pcManager.subscriber),this.pcManager.onIceCandidate=(e,t)=>{this.client.sendIceCandidate(e,t)},this.pcManager.onPublisherOffer=e=>{this.client.sendOffer(e)},this.pcManager.onDataChannel=this.handleDataChannel,this.pcManager.onStateChange=(n,i,s)=>Qn(this,void 0,void 0,(function*(){if(this.log.debug("primary PC state changed ".concat(n),this.logContext),["closed","disconnected","failed"].includes(i)&&(this.publisherConnectionPromise=void 0),n===jr.CONNECTED){const n=this.pcState===pa.New;this.pcState=pa.Connected,n&&this.emit(e.EngineEvent.Connected,t)}else n===jr.FAILED&&this.pcState===pa.Connected&&(this.pcState=pa.Disconnected,this.handleDisconnect("peerconnection failed","failed"===s?et.RR_SUBSCRIBER_FAILED:et.RR_PUBLISHER_FAILED));const o=this.client.isDisconnected||this.client.currentState===dr.RECONNECTING,r=[jr.FAILED,jr.CLOSING,jr.CLOSED].includes(n);o&&r&&!this._isClosed&&this.emit(e.EngineEvent.Offline)})),this.pcManager.onTrack=t=>{this.emit(e.EngineEvent.MediaTrackAdded,t.track,t.streams[0],t.receiver)},void 0!==(o=null===(i=t.serverInfo)||void 0===i?void 0:i.protocol)&&o>13||this.createDataChannels()}))}setupSignalClientCallbacks(){this.client.onAnswer=e=>Qn(this,void 0,void 0,(function*(){this.pcManager&&(this.log.debug("received server answer",Object.assign(Object.assign({},this.logContext),{RTCSdpType:e.type})),yield this.pcManager.setPublisherAnswer(e))})),this.client.onTrickle=(e,t)=>{this.pcManager&&(this.log.debug("got ICE candidate from peer",Object.assign(Object.assign({},this.logContext),{candidate:e,target:t})),this.pcManager.addIceCandidate(e,t))},this.client.onOffer=e=>Qn(this,void 0,void 0,(function*(){if(!this.pcManager)return;const t=yield this.pcManager.createSubscriberAnswerFromOffer(e);this.client.sendAnswer(t)})),this.client.onLocalTrackPublished=e=>{var t;if(this.log.debug("received trackPublishedResponse",Object.assign(Object.assign({},this.logContext),{cid:e.cid,track:null===(t=e.track)||void 0===t?void 0:t.sid})),!this.pendingTrackResolvers[e.cid])return void this.log.error("missing track resolver for ".concat(e.cid),Object.assign(Object.assign({},this.logContext),{cid:e.cid}));const{resolve:n}=this.pendingTrackResolvers[e.cid];delete this.pendingTrackResolvers[e.cid],n(e.track)},this.client.onLocalTrackUnpublished=t=>{this.emit(e.EngineEvent.LocalTrackUnpublished,t)},this.client.onLocalTrackSubscribed=t=>{this.emit(e.EngineEvent.LocalTrackSubscribed,t)},this.client.onTokenRefresh=e=>{this.token=e},this.client.onRemoteMuteChanged=(t,n)=>{this.emit(e.EngineEvent.RemoteMute,t,n)},this.client.onSubscribedQualityUpdate=t=>{this.emit(e.EngineEvent.SubscribedQualityUpdate,t)},this.client.onClose=()=>{this.handleDisconnect("signal",et.RR_SIGNAL_DISCONNECTED)},this.client.onLeave=t=>{switch(this.log.debug("client leave request",Object.assign(Object.assign({},this.logContext),{reason:null==t?void 0:t.reason})),t.regions&&this.regionUrlProvider&&(this.log.debug("updating regions",this.logContext),this.regionUrlProvider.setServerReportedRegions(t.regions)),t.action){case cn.DISCONNECT:this.emit(e.EngineEvent.Disconnected,null==t?void 0:t.reason),this.close();break;case cn.RECONNECT:this.fullReconnectOnNext=!0,this.handleDisconnect(ha);break;case cn.RESUME:this.handleDisconnect(ha)}}}makeRTCConfiguration(e){var t;const n=Object.assign({},this.rtcConfig);if((null===(t=this.signalOpts)||void 0===t?void 0:t.e2eeEnabled)&&(this.log.debug("E2EE - setting up transports with insertable streams",this.logContext),n.encodedInsertableStreams=!0),e.iceServers&&!n.iceServers){const t=[];e.iceServers.forEach((e=>{const n={urls:e.urls};e.username&&(n.username=e.username),e.credential&&(n.credential=e.credential),t.push(n)})),n.iceServers=t}return e.clientConfiguration&&e.clientConfiguration.forceRelay===Ze.ENABLED&&(n.iceTransportPolicy="relay"),n.sdpSemantics="unified-plan",n.continualGatheringPolicy="gather_continually",n}createDataChannels(){this.pcManager&&(this.lossyDC&&(this.lossyDC.onmessage=null,this.lossyDC.onerror=null),this.reliableDC&&(this.reliableDC.onmessage=null,this.reliableDC.onerror=null),this.lossyDC=this.pcManager.createPublisherDataChannel(la,{ordered:!0,maxRetransmits:0}),this.reliableDC=this.pcManager.createPublisherDataChannel(ua,{ordered:!0}),this.lossyDC.onmessage=this.handleDataMessage,this.reliableDC.onmessage=this.handleDataMessage,this.lossyDC.onerror=this.handleDataError,this.reliableDC.onerror=this.handleDataError,this.lossyDC.bufferedAmountLowThreshold=65535,this.reliableDC.bufferedAmountLowThreshold=65535,this.lossyDC.onbufferedamountlow=this.handleBufferedAmountLow,this.reliableDC.onbufferedamountlow=this.handleBufferedAmountLow)}createSender(e,t,n){return Qn(this,void 0,void 0,(function*(){if(no()){return yield this.createTransceiverRTCRtpSender(e,t,n)}if(io()){this.log.warn("using add-track fallback",this.logContext);return yield this.createRTCRtpSender(e.mediaStreamTrack)}throw new Ds("Required webRTC APIs not supported on this device")}))}createSimulcastSender(e,t,n,i){return Qn(this,void 0,void 0,(function*(){if(no())return this.createSimulcastTransceiverSender(e,t,n,i);if(io())return this.log.debug("using add-track fallback",this.logContext),this.createRTCRtpSender(e.mediaStreamTrack);throw new Ds("Cannot stream on this device")}))}createTransceiverRTCRtpSender(e,t,n){return Qn(this,void 0,void 0,(function*(){if(!this.pcManager)throw new Ds("publisher is closed");const i=[];e.mediaStream&&i.push(e.mediaStream),Fo(e)&&(e.codec=t.videoCodec);const s={direction:"sendonly",streams:i};n&&(s.sendEncodings=n);return(yield this.pcManager.addPublisherTransceiver(e.mediaStreamTrack,s)).sender}))}createSimulcastTransceiverSender(e,t,n,i){return Qn(this,void 0,void 0,(function*(){if(!this.pcManager)throw new Ds("publisher is closed");const s={direction:"sendonly"};i&&(s.sendEncodings=i);const o=yield this.pcManager.addPublisherTransceiver(t.mediaStreamTrack,s);if(n.videoCodec)return e.setSimulcastTrackSender(n.videoCodec,o.sender),o.sender}))}createRTCRtpSender(e){return Qn(this,void 0,void 0,(function*(){if(!this.pcManager)throw new Ds("publisher is closed");return this.pcManager.addPublisherTrack(e)}))}attemptReconnect(t){return Qn(this,void 0,void 0,(function*(){var n,i,s;if(!this._isClosed)if(this.attemptingReconnect)qn.warn("already attempting reconnect, returning early",this.logContext);else{(null===(n=this.clientConfiguration)||void 0===n?void 0:n.resumeConnection)!==Ze.DISABLED&&(null!==(s=null===(i=this.pcManager)||void 0===i?void 0:i.currentState)&&void 0!==s?s:jr.NEW)!==jr.NEW||(this.fullReconnectOnNext=!0);try{this.attemptingReconnect=!0,this.fullReconnectOnNext?yield this.restartConnection():yield this.resumeConnection(t),this.clearPendingReconnect(),this.fullReconnectOnNext=!1}catch(t){this.reconnectAttempts+=1;let n=!0;t instanceof Ds?(this.log.debug("received unrecoverable error",Object.assign(Object.assign({},this.logContext),{error:t})),n=!1):t instanceof ga||(this.fullReconnectOnNext=!0),n?this.handleDisconnect("reconnect",et.RR_UNKNOWN):(this.log.info("could not recover connection after ".concat(this.reconnectAttempts," attempts, ").concat(Date.now()-this.reconnectStart,"ms. giving up"),this.logContext),this.emit(e.EngineEvent.Disconnected),yield this.close())}finally{this.attemptingReconnect=!1}}}))}getNextRetryDelay(e){try{return this.reconnectPolicy.nextRetryDelayInMs(e)}catch(e){this.log.warn("encountered error in reconnect policy",Object.assign(Object.assign({},this.logContext),{error:e}))}return null}restartConnection(t){return Qn(this,void 0,void 0,(function*(){var n,i,s;try{if(!this.url||!this.token)throw new Ds("could not reconnect, url or token not saved");let i;this.log.info("reconnecting, attempt: ".concat(this.reconnectAttempts),this.logContext),this.emit(e.EngineEvent.Restarting),this.client.isDisconnected||(yield this.client.sendLeave()),yield this.cleanupPeerConnections(),yield this.cleanupClient();try{if(!this.signalOpts)throw this.log.warn("attempted connection restart, without signal options present",this.logContext),new ga;i=yield this.join(null!=t?t:this.url,this.token,this.signalOpts)}catch(t){if(t instanceof Ps&&t.reason===e.ConnectionErrorReason.NotAllowed)throw new Ds("could not reconnect, token might be expired");throw new ga}if(this.shouldFailNext)throw this.shouldFailNext=!1,new Error("simulated failure");if(this.client.setReconnected(),this.emit(e.EngineEvent.SignalRestarted,i),yield this.waitForPCReconnected(),this.client.currentState!==dr.CONNECTED)throw new ga("Signal connection got severed during reconnect");null===(n=this.regionUrlProvider)||void 0===n||n.resetAttempts(),this.emit(e.EngineEvent.Restarted)}catch(e){const t=yield null===(i=this.regionUrlProvider)||void 0===i?void 0:i.getNextBestRegionUrl();if(t)return void(yield this.restartConnection(t));throw null===(s=this.regionUrlProvider)||void 0===s||s.resetAttempts(),e}}))}resumeConnection(t){return Qn(this,void 0,void 0,(function*(){var n;if(!this.url||!this.token)throw new Ds("could not reconnect, url or token not saved");if(!this.pcManager)throw new Ds("publisher and subscriber connections unset");let i;this.log.info("resuming signal connection, attempt ".concat(this.reconnectAttempts),this.logContext),this.emit(e.EngineEvent.Resuming);try{this.setupSignalClientCallbacks(),i=yield this.client.reconnect(this.url,this.token,this.participantSid,t)}catch(t){let n="";if(t instanceof Error&&(n=t.message,this.log.error(t.message,Object.assign(Object.assign({},this.logContext),{error:t}))),t instanceof Ps&&t.reason===e.ConnectionErrorReason.NotAllowed)throw new Ds("could not reconnect, token might be expired");if(t instanceof Ps&&t.reason===e.ConnectionErrorReason.LeaveRequest)throw t;throw new ga(n)}if(this.emit(e.EngineEvent.SignalResumed),i){const e=this.makeRTCConfiguration(i);this.pcManager.updateConfiguration(e)}else this.log.warn("Did not receive reconnect response",this.logContext);if(this.shouldFailNext)throw this.shouldFailNext=!1,new Error("simulated failure");if(yield this.pcManager.triggerIceRestart(),yield this.waitForPCReconnected(),this.client.currentState!==dr.CONNECTED)throw new ga("Signal connection got severed during reconnect");this.client.setReconnected(),"open"===(null===(n=this.reliableDC)||void 0===n?void 0:n.readyState)&&null===this.reliableDC.id&&this.createDataChannels(),this.emit(e.EngineEvent.Resumed)}))}waitForPCInitialConnection(e,t){return Qn(this,void 0,void 0,(function*(){if(!this.pcManager)throw new Ds("PC manager is closed");yield this.pcManager.ensurePCTransportConnection(t,e)}))}waitForPCReconnected(){return Qn(this,void 0,void 0,(function*(){this.pcState=pa.Reconnecting,this.log.debug("waiting for peer connection to reconnect",this.logContext);try{if(yield to(2e3),!this.pcManager)throw new Ds("PC manager is closed");yield this.pcManager.ensurePCTransportConnection(void 0,this.peerConnectionTimeout),this.pcState=pa.Connected}catch(t){throw this.pcState=pa.Disconnected,new Ps("could not establish PC connection, ".concat(t.message),e.ConnectionErrorReason.InternalError)}}))}publishRpcResponse(e,t,n,i){return Qn(this,void 0,void 0,(function*(){const s=new pt({destinationIdentities:[e],kind:mt.RELIABLE,value:{case:"rpcResponse",value:new Et({requestId:t,value:i?{case:"error",value:i.toProto()}:{case:"payload",value:null!=n?n:""}})}});yield this.sendDataPacket(s,mt.RELIABLE)}))}publishRpcAck(e,t){return Qn(this,void 0,void 0,(function*(){const n=new pt({destinationIdentities:[e],kind:mt.RELIABLE,value:{case:"rpcAck",value:new St({requestId:t})}});yield this.sendDataPacket(n,mt.RELIABLE)}))}sendDataPacket(e,t){return Qn(this,void 0,void 0,(function*(){const n=e.toBinary();yield this.ensurePublisherConnected(t);const i=this.dataChannelForKind(t);i&&i.send(n),this.updateAndEmitDCBufferStatus(t)}))}waitForBufferStatusLow(t){return new Promise(((n,i)=>Qn(this,void 0,void 0,(function*(){if(this.isBufferStatusLow(t))n();else{const s=()=>i("Engine closed");for(this.once(e.EngineEvent.Closing,s);!this.dcBufferStatus.get(t);)yield to(10);this.off(e.EngineEvent.Closing,s),n()}}))))}ensureDataTransportConnected(t){return Qn(this,arguments,void 0,(function(t){var n=this;let i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.subscriberPrimary;return function*(){var s;if(!n.pcManager)throw new Ds("PC manager is closed");const o=i?n.pcManager.subscriber:n.pcManager.publisher,r=i?"Subscriber":"Publisher";if(!o)throw new Ps("".concat(r," connection not set"),e.ConnectionErrorReason.InternalError);let a=!1;i||n.dataChannelForKind(t,i)||(n.createDataChannels(),a=!0),a||i||n.pcManager.publisher.isICEConnected||"checking"===n.pcManager.publisher.getICEConnectionState()||(a=!0),a&&n.negotiate();const c=n.dataChannelForKind(t,i);if("open"===(null==c?void 0:c.readyState))return;const d=(new Date).getTime()+n.peerConnectionTimeout;for(;(new Date).getTime()<d;){if(o.isICEConnected&&"open"===(null===(s=n.dataChannelForKind(t,i))||void 0===s?void 0:s.readyState))return;yield to(50)}throw new Ps("could not establish ".concat(r," connection, state: ").concat(o.getICEConnectionState()),e.ConnectionErrorReason.InternalError)}()}))}ensurePublisherConnected(e){return Qn(this,void 0,void 0,(function*(){this.publisherConnectionPromise||(this.publisherConnectionPromise=this.ensureDataTransportConnected(e,!1)),yield this.publisherConnectionPromise}))}verifyTransport(){return!!this.pcManager&&(this.pcManager.currentState===jr.CONNECTED&&!(!this.client.ws||this.client.ws.readyState===WebSocket.CLOSED))}negotiate(){return Qn(this,void 0,void 0,(function*(){return new Promise(((t,n)=>Qn(this,void 0,void 0,(function*(){if(!this.pcManager)return void n(new xs("PC manager is closed"));this.pcManager.requirePublisher(),0!=this.pcManager.publisher.getTransceivers().length||this.lossyDC||this.reliableDC||this.createDataChannels();const i=new AbortController,s=()=>{i.abort(),this.log.debug("engine disconnected while negotiation was ongoing",this.logContext),t()};this.isClosed&&n("cannot negotiate on closed engine"),this.on(e.EngineEvent.Closing,s),this.pcManager.publisher.once(Ir,(t=>{const n=new Map;t.forEach((e=>{const t=e.codec.toLowerCase();var i;i=t,Js.includes(i)&&n.set(e.payload,t)})),this.emit(e.EngineEvent.RTPVideoMapUpdate,n)}));try{yield this.pcManager.negotiate(i),t()}catch(e){e instanceof xs&&(this.fullReconnectOnNext=!0),this.handleDisconnect("negotiation",et.RR_UNKNOWN),n(e)}finally{this.off(e.EngineEvent.Closing,s)}}))))}))}dataChannelForKind(e,t){if(t){if(e===mt.LOSSY)return this.lossyDCSub;if(e===mt.RELIABLE)return this.reliableDCSub}else{if(e===mt.LOSSY)return this.lossyDC;if(e===mt.RELIABLE)return this.reliableDC}}sendSyncState(e,t){var n,i;if(!this.pcManager)return void this.log.warn("sync state cannot be sent without peer connection setup",this.logContext);const s=this.pcManager.subscriber.getLocalDescription(),o=this.pcManager.subscriber.getRemoteDescription(),r=null===(i=null===(n=this.signalOpts)||void 0===n?void 0:n.autoSubscribe)||void 0===i||i,a=new Array,c=new Array;e.forEach((e=>{e.isDesired!==r&&a.push(e.trackSid),e.isEnabled||c.push(e.trackSid)})),this.client.sendSyncState(new En({answer:s?hr({sdp:s.sdp,type:s.type}):void 0,offer:o?hr({sdp:o.sdp,type:o.type}):void 0,subscription:new nn({trackSids:a,subscribe:!r,participantTracks:[]}),publishTracks:er(t),dataChannels:this.dataChannelsInfo(),trackSidsDisabled:c}))}failNext(){this.shouldFailNext=!0}dataChannelsInfo(){const e=[],t=(t,n)=>{void 0!==(null==t?void 0:t.id)&&null!==t.id&&e.push(new wn({label:t.label,id:t.id,target:n}))};return t(this.dataChannelForKind(mt.LOSSY),Vt.PUBLISHER),t(this.dataChannelForKind(mt.RELIABLE),Vt.PUBLISHER),t(this.dataChannelForKind(mt.LOSSY,!0),Vt.SUBSCRIBER),t(this.dataChannelForKind(mt.RELIABLE,!0),Vt.SUBSCRIBER),e}clearReconnectTimeout(){this.reconnectTimeout&&Vs.clearTimeout(this.reconnectTimeout)}clearPendingReconnect(){this.clearReconnectTimeout(),this.reconnectAttempts=0}registerOnLineListener(){mo()&&window.addEventListener("online",this.handleBrowserOnLine)}deregisterOnLineListener(){mo()&&window.removeEventListener("online",this.handleBrowserOnLine)}}class ga extends Error{}class va{constructor(e,t){this.lastUpdateAt=0,this.settingsCacheTime=3e3,this.attemptedRegions=[],this.serverUrl=new URL(e),this.token=t}updateToken(e){this.token=e}isCloud(){return vo(this.serverUrl)}getServerUrl(){return this.serverUrl}getNextBestRegionUrl(e){return Qn(this,void 0,void 0,(function*(){if(!this.isCloud())throw Error("region availability is only supported for LiveKit Cloud domains");(!this.regionSettings||Date.now()-this.lastUpdateAt>this.settingsCacheTime)&&(this.regionSettings=yield this.fetchRegionSettings(e));const t=this.regionSettings.regions.filter((e=>!this.attemptedRegions.find((t=>t.url===e.url))));if(t.length>0){const e=t[0];return this.attemptedRegions.push(e),qn.debug("next region: ".concat(e.region)),e.url}return null}))}resetAttempts(){this.attemptedRegions=[]}fetchRegionSettings(t){return Qn(this,void 0,void 0,(function*(){const n=yield fetch("".concat((i=this.serverUrl,"".concat(i.protocol.replace("ws","http"),"//").concat(i.host,"/settings")),"/regions"),{headers:{authorization:"Bearer ".concat(this.token)},signal:t});var i;if(n.ok){const e=yield n.json();return this.lastUpdateAt=Date.now(),e}throw new Ps("Could not fetch region settings: ".concat(n.statusText),401===n.status?e.ConnectionErrorReason.NotAllowed:e.ConnectionErrorReason.InternalError,n.status)}))}setServerReportedRegions(e){this.regionSettings=e,this.lastUpdateAt=Date.now()}}class fa{get info(){return this._info}constructor(e,t,n){this.reader=t,this.totalByteSize=n,this._info=e,this.bytesReceived=0}}class ka extends fa{handleChunkReceived(e){var t;this.bytesReceived+=e.content.byteLength;const n=this.totalByteSize?this.bytesReceived/this.totalByteSize:void 0;null===(t=this.onProgress)||void 0===t||t.call(this,n)}[Symbol.asyncIterator](){const e=this.reader.getReader();return{next:()=>Qn(this,void 0,void 0,(function*(){try{const{done:t,value:n}=yield e.read();return t?{done:!0,value:void 0}:(this.handleChunkReceived(n),{done:!1,value:n.content})}catch(e){return{done:!0,value:void 0}}})),return(){return Qn(this,void 0,void 0,(function*(){return e.releaseLock(),{done:!0,value:void 0}}))}}}readAll(){return Qn(this,void 0,void 0,(function*(){var e,t,n,i;let s=new Set;try{for(var o,r=!0,a=Xn(this);!(e=(o=yield a.next()).done);r=!0){i=o.value,r=!1;const e=i;s.add(e)}}catch(e){t={error:e}}finally{try{r||e||!(n=a.return)||(yield n.call(a))}finally{if(t)throw t.error}}return Array.from(s)}))}}class ba extends fa{constructor(e,t,n){super(e,t,n),this.receivedChunks=new Map}handleChunkReceived(e){var t;const n=Ao(e.chunkIndex),i=this.receivedChunks.get(n);if(i&&i.version>e.version)return;this.receivedChunks.set(n,e),this.bytesReceived+=e.content.byteLength;const s=this.totalByteSize?this.bytesReceived/this.totalByteSize:void 0;null===(t=this.onProgress)||void 0===t||t.call(this,s)}[Symbol.asyncIterator](){const e=this.reader.getReader(),t=new TextDecoder;return{next:()=>Qn(this,void 0,void 0,(function*(){try{const{done:n,value:i}=yield e.read();return n?{done:!0,value:void 0}:(this.handleChunkReceived(i),{done:!1,value:t.decode(i.content)})}catch(e){return{done:!0,value:void 0}}})),return(){return Qn(this,void 0,void 0,(function*(){return e.releaseLock(),{done:!0,value:void 0}}))}}}readAll(){return Qn(this,void 0,void 0,(function*(){var e,t,n,i;let s="";try{for(var o,r=!0,a=Xn(this);!(e=(o=yield a.next()).done);r=!0){i=o.value,r=!1;s+=i}}catch(e){t={error:e}}finally{try{r||e||!(n=a.return)||(yield n.call(a))}finally{if(t)throw t.error}}return s}))}}class ya{constructor(e,t,n){this.writableStream=e,this.defaultWriter=e.getWriter(),this.onClose=n,this.info=t}write(e){return this.defaultWriter.write(e)}close(){return Qn(this,void 0,void 0,(function*(){var e;yield this.defaultWriter.close(),this.defaultWriter.releaseLock(),null===(e=this.onClose)||void 0===e||e.call(this)}))}}class Ta extends ya{}class Ca extends Ks{constructor(e,t,n,i,s){super(e,n,s),this.sid=t,this.receiver=i}get isLocal(){return!1}setMuted(t){this.isMuted!==t&&(this.isMuted=t,this._mediaStreamTrack.enabled=!t,this.emit(t?e.TrackEvent.Muted:e.TrackEvent.Unmuted,this))}setMediaStream(t){this.mediaStream=t;const n=i=>{i.track===this._mediaStreamTrack&&(t.removeEventListener("removetrack",n),this.receiver&&"playoutDelayHint"in this.receiver&&(this.receiver.playoutDelayHint=void 0),this.receiver=void 0,this._currentBitrate=0,this.emit(e.TrackEvent.Ended,this))};t.addEventListener("removetrack",n)}start(){this.startMonitor(),super.enable()}stop(){this.stopMonitor(),super.disable()}getRTCStatsReport(){return Qn(this,void 0,void 0,(function*(){var e;if(!(null===(e=this.receiver)||void 0===e?void 0:e.getStats))return;return yield this.receiver.getStats()}))}setPlayoutDelay(e){this.receiver?"playoutDelayHint"in this.receiver?this.receiver.playoutDelayHint=e:this.log.warn("Playout delay not supported in this browser"):this.log.warn("Cannot set playout delay, track already ended")}getPlayoutDelay(){if(this.receiver){if("playoutDelayHint"in this.receiver)return this.receiver.playoutDelayHint;this.log.warn("Playout delay not supported in this browser")}else this.log.warn("Cannot get playout delay, track already ended");return 0}startMonitor(){this.monitorInterval||(this.monitorInterval=setInterval((()=>this.monitorReceiver()),Kr)),"undefined"!=typeof RTCRtpReceiver&&"getSynchronizationSources"in RTCRtpReceiver&&this.registerTimeSyncUpdate()}registerTimeSyncUpdate(){const t=()=>{var n;this.timeSyncHandle=requestAnimationFrame((()=>t()));const i=null===(n=this.receiver)||void 0===n?void 0:n.getSynchronizationSources()[0];if(i){const{timestamp:t,rtpTimestamp:n}=i;n&&this.rtpTimestamp!==n&&(this.emit(e.TrackEvent.TimeSyncUpdate,{timestamp:t,rtpTimestamp:n}),this.rtpTimestamp=n)}};t()}}class Sa extends Ca{constructor(e,t,n,i,s,o){super(e,t,Ks.Kind.Audio,n,o),this.monitorReceiver=()=>Qn(this,void 0,void 0,(function*(){if(!this.receiver)return void(this._currentBitrate=0);const e=yield this.getReceiverStats();e&&this.prevStats&&this.receiver&&(this._currentBitrate=Wr(e,this.prevStats)),this.prevStats=e})),this.audioContext=i,this.webAudioPluginNodes=[],s&&(this.sinkId=s.deviceId)}setVolume(e){var t;for(const n of this.attachedElements)this.audioContext?null===(t=this.gainNode)||void 0===t||t.gain.setTargetAtTime(e,0,.1):n.volume=e;go()&&this._mediaStreamTrack._setVolume(e),this.elementVolume=e}getVolume(){if(this.elementVolume)return this.elementVolume;if(go())return 1;let e=0;return this.attachedElements.forEach((t=>{t.volume>e&&(e=t.volume)})),e}setSinkId(e){return Qn(this,void 0,void 0,(function*(){this.sinkId=e,yield Promise.all(this.attachedElements.map((t=>{if(ao(t))return t.setSinkId(e)})))}))}attach(e){const t=0===this.attachedElements.length;return e?super.attach(e):e=super.attach(),this.sinkId&&ao(e)&&e.setSinkId(this.sinkId),this.audioContext&&t&&(this.log.debug("using audio context mapping",this.logContext),this.connectWebAudio(this.audioContext,e),e.volume=0,e.muted=!0),this.elementVolume&&this.setVolume(this.elementVolume),e}detach(e){let t;return e?(t=super.detach(e),this.audioContext&&(this.attachedElements.length>0?this.connectWebAudio(this.audioContext,this.attachedElements[0]):this.disconnectWebAudio())):(t=super.detach(),this.disconnectWebAudio()),t}setAudioContext(e){this.audioContext=e,e&&this.attachedElements.length>0?this.connectWebAudio(e,this.attachedElements[0]):e||this.disconnectWebAudio()}setWebAudioPlugins(e){this.webAudioPluginNodes=e,this.attachedElements.length>0&&this.audioContext&&this.connectWebAudio(this.audioContext,this.attachedElements[0])}connectWebAudio(t,n){this.disconnectWebAudio(),this.sourceNode=t.createMediaStreamSource(n.srcObject);let i=this.sourceNode;this.webAudioPluginNodes.forEach((e=>{i.connect(e),i=e})),this.gainNode=t.createGain(),i.connect(this.gainNode),this.gainNode.connect(t.destination),this.elementVolume&&this.gainNode.gain.setTargetAtTime(this.elementVolume,0,.1),"running"!==t.state&&t.resume().then((()=>{"running"!==t.state&&this.emit(e.TrackEvent.AudioPlaybackFailed,new Error("Audio Context couldn't be started automatically"))})).catch((t=>{this.emit(e.TrackEvent.AudioPlaybackFailed,t)}))}disconnectWebAudio(){var e,t;null===(e=this.gainNode)||void 0===e||e.disconnect(),null===(t=this.sourceNode)||void 0===t||t.disconnect(),this.gainNode=void 0,this.sourceNode=void 0}getReceiverStats(){return Qn(this,void 0,void 0,(function*(){if(!this.receiver||!this.receiver.getStats)return;let e;return(yield this.receiver.getStats()).forEach((t=>{"inbound-rtp"===t.type&&(e={type:"audio",streamId:t.id,timestamp:t.timestamp,jitter:t.jitter,bytesReceived:t.bytesReceived,concealedSamples:t.concealedSamples,concealmentEvents:t.concealmentEvents,silentConcealedSamples:t.silentConcealedSamples,silentConcealmentEvents:t.silentConcealmentEvents,totalAudioEnergy:t.totalAudioEnergy,totalSamplesDuration:t.totalSamplesDuration})})),e}))}}class Ea extends Ca{constructor(e,t,n,i,s){super(e,t,Ks.Kind.Video,n,s),this.elementInfos=[],this.monitorReceiver=()=>Qn(this,void 0,void 0,(function*(){if(!this.receiver)return void(this._currentBitrate=0);const e=yield this.getReceiverStats();e&&this.prevStats&&this.receiver&&(this._currentBitrate=Wr(e,this.prevStats)),this.prevStats=e})),this.debouncedHandleResize=wr((()=>{this.updateDimensions()}),100),this.adaptiveStreamSettings=i}get isAdaptiveStream(){return void 0!==this.adaptiveStreamSettings}get mediaStreamTrack(){return this._mediaStreamTrack}setMuted(e){super.setMuted(e),this.attachedElements.forEach((t=>{e?Hs(this._mediaStreamTrack,t):Ws(this._mediaStreamTrack,t)}))}attach(e){if(e?super.attach(e):e=super.attach(),this.adaptiveStreamSettings&&void 0===this.elementInfos.find((t=>t.element===e))){const t=new wa(e);this.observeElementInfo(t)}return e}observeElementInfo(e){this.adaptiveStreamSettings&&void 0===this.elementInfos.find((t=>t===e))?(e.handleResize=()=>{this.debouncedHandleResize()},e.handleVisibilityChanged=()=>{this.updateVisibility()},this.elementInfos.push(e),e.observe(),this.debouncedHandleResize(),this.updateVisibility()):this.log.warn("visibility resize observer not triggered",this.logContext)}stopObservingElementInfo(e){if(!this.isAdaptiveStream)return void this.log.warn("stopObservingElementInfo ignored",this.logContext);const t=this.elementInfos.filter((t=>t===e));for(const e of t)e.stopObserving();this.elementInfos=this.elementInfos.filter((t=>t!==e)),this.updateVisibility(),this.debouncedHandleResize()}detach(e){let t=[];if(e)return this.stopObservingElement(e),super.detach(e);t=super.detach();for(const e of t)this.stopObservingElement(e);return t}getDecoderImplementation(){var e;return null===(e=this.prevStats)||void 0===e?void 0:e.decoderImplementation}getReceiverStats(){return Qn(this,void 0,void 0,(function*(){if(!this.receiver||!this.receiver.getStats)return;const e=yield this.receiver.getStats();let t,n="",i=new Map;return e.forEach((e=>{"inbound-rtp"===e.type?(n=e.codecId,t={type:"video",streamId:e.id,framesDecoded:e.framesDecoded,framesDropped:e.framesDropped,framesReceived:e.framesReceived,packetsReceived:e.packetsReceived,packetsLost:e.packetsLost,frameWidth:e.frameWidth,frameHeight:e.frameHeight,pliCount:e.pliCount,firCount:e.firCount,nackCount:e.nackCount,jitter:e.jitter,timestamp:e.timestamp,bytesReceived:e.bytesReceived,decoderImplementation:e.decoderImplementation}):"codec"===e.type&&i.set(e.id,e)})),t&&""!==n&&i.get(n)&&(t.mimeType=i.get(n).mimeType),t}))}stopObservingElement(e){const t=this.elementInfos.filter((t=>t.element===e));for(const e of t)this.stopObservingElementInfo(e)}handleAppVisibilityChanged(){const e=Object.create(null,{handleAppVisibilityChanged:{get:()=>super.handleAppVisibilityChanged}});return Qn(this,void 0,void 0,(function*(){yield e.handleAppVisibilityChanged.call(this),this.isAdaptiveStream&&this.updateVisibility()}))}updateVisibility(){var t,n;const i=this.elementInfos.reduce(((e,t)=>Math.max(e,t.visibilityChangedAt||0)),0),s=!(null!==(n=null===(t=this.adaptiveStreamSettings)||void 0===t?void 0:t.pauseVideoInBackground)&&void 0!==n&&!n)&&this.isInBackground,o=this.elementInfos.some((e=>e.pictureInPicture)),r=this.elementInfos.some((e=>e.visible))&&!s||o;this.lastVisible!==r&&(!r&&Date.now()-i<100?Vs.setTimeout((()=>{this.updateVisibility()}),100):(this.lastVisible=r,this.emit(e.TrackEvent.VisibilityChanged,r,this)))}updateDimensions(){var t,n;let i=0,s=0;const o=this.getPixelDensity();for(const e of this.elementInfos){const t=e.width()*o,n=e.height()*o;t+n>i+s&&(i=t,s=n)}(null===(t=this.lastDimensions)||void 0===t?void 0:t.width)===i&&(null===(n=this.lastDimensions)||void 0===n?void 0:n.height)===s||(this.lastDimensions={width:i,height:s},this.emit(e.TrackEvent.VideoDimensionsChanged,this.lastDimensions,this))}getPixelDensity(){var e;const t=null===(e=this.adaptiveStreamSettings)||void 0===e?void 0:e.pixelDensity;if("screen"===t)return bo();if(!t){return bo()>2?2:1}return t}}class wa{get visible(){return this.isPiP||this.isIntersecting}get pictureInPicture(){return this.isPiP}constructor(e,t){this.onVisibilityChanged=e=>{var t;const{target:n,isIntersecting:i}=e;n===this.element&&(this.isIntersecting=i,this.isPiP=Pa(this.element),this.visibilityChangedAt=Date.now(),null===(t=this.handleVisibilityChanged)||void 0===t||t.call(this))},this.onEnterPiP=()=>{var e,t,n;null===(t=null===(e=window.documentPictureInPicture)||void 0===e?void 0:e.window)||void 0===t||t.addEventListener("pagehide",this.onLeavePiP),this.isPiP=Pa(this.element),null===(n=this.handleVisibilityChanged)||void 0===n||n.call(this)},this.onLeavePiP=()=>{var e;this.isPiP=Pa(this.element),null===(e=this.handleVisibilityChanged)||void 0===e||e.call(this)},this.element=e,this.isIntersecting=null!=t?t:Ra(e),this.isPiP=mo()&&Pa(e),this.visibilityChangedAt=0}width(){return this.element.clientWidth}height(){return this.element.clientHeight}observe(){var e,t,n;this.isIntersecting=Ra(this.element),this.isPiP=Pa(this.element),this.element.handleResize=()=>{var e;null===(e=this.handleResize)||void 0===e||e.call(this)},this.element.handleVisibilityChanged=this.onVisibilityChanged,Po().observe(this.element),Eo().observe(this.element),this.element.addEventListener("enterpictureinpicture",this.onEnterPiP),this.element.addEventListener("leavepictureinpicture",this.onLeavePiP),null===(e=window.documentPictureInPicture)||void 0===e||e.addEventListener("enter",this.onEnterPiP),null===(n=null===(t=window.documentPictureInPicture)||void 0===t?void 0:t.window)||void 0===n||n.addEventListener("pagehide",this.onLeavePiP)}stopObserving(){var e,t,n,i,s;null===(e=Po())||void 0===e||e.unobserve(this.element),null===(t=Eo())||void 0===t||t.unobserve(this.element),this.element.removeEventListener("enterpictureinpicture",this.onEnterPiP),this.element.removeEventListener("leavepictureinpicture",this.onLeavePiP),null===(n=window.documentPictureInPicture)||void 0===n||n.removeEventListener("enter",this.onEnterPiP),null===(s=null===(i=window.documentPictureInPicture)||void 0===i?void 0:i.window)||void 0===s||s.removeEventListener("pagehide",this.onLeavePiP)}}function Pa(e){var t,n;return document.pictureInPictureElement===e||!!(null===(t=window.documentPictureInPicture)||void 0===t?void 0:t.window)&&Ra(e,null===(n=window.documentPictureInPicture)||void 0===n?void 0:n.window)}function Ra(e,t){const n=t||window;let i=e.offsetTop,s=e.offsetLeft;const o=e.offsetWidth,r=e.offsetHeight,{hidden:a}=e,{display:c}=getComputedStyle(e);for(;e.offsetParent;)i+=(e=e.offsetParent).offsetTop,s+=e.offsetLeft;return i<n.pageYOffset+n.innerHeight&&s<n.pageXOffset+n.innerWidth&&i+r>n.pageYOffset&&s+o>n.pageXOffset&&!a&&"none"!==c}class Ia extends ei.EventEmitter{constructor(t,n,i,s){var o;super(),this.metadataMuted=!1,this.encryption=dt.NONE,this.log=qn,this.handleMuted=()=>{this.emit(e.TrackEvent.Muted)},this.handleUnmuted=()=>{this.emit(e.TrackEvent.Unmuted)},this.log=Wn(null!==(o=null==s?void 0:s.loggerName)&&void 0!==o?o:e.LoggerNames.Publication),this.loggerContextCb=this.loggerContextCb,this.setMaxListeners(100),this.kind=t,this.trackSid=n,this.trackName=i,this.source=Ks.Source.Unknown}setTrack(t){this.track&&(this.track.off(e.TrackEvent.Muted,this.handleMuted),this.track.off(e.TrackEvent.Unmuted,this.handleUnmuted)),this.track=t,t&&(t.on(e.TrackEvent.Muted,this.handleMuted),t.on(e.TrackEvent.Unmuted,this.handleUnmuted))}get logContext(){var e;return Object.assign(Object.assign({},null===(e=this.loggerContextCb)||void 0===e?void 0:e.call(this)),tr(this))}get isMuted(){return this.metadataMuted}get isEnabled(){return!0}get isSubscribed(){return void 0!==this.track}get isEncrypted(){return this.encryption!==dt.NONE}get audioTrack(){if(jo(this.track))return this.track}get videoTrack(){if(Fo(this.track))return this.track}updateInfo(e){this.trackSid=e.sid,this.trackName=e.name,this.source=Ks.sourceFromProto(e.source),this.mimeType=e.mimeType,this.kind===Ks.Kind.Video&&e.width>0&&(this.dimensions={width:e.width,height:e.height},this.simulcasted=e.simulcast),this.encryption=e.encryption,this.trackInfo=e,this.log.debug("update publication info",Object.assign(Object.assign({},this.logContext),{info:e}))}}!function(e){var t,n;(t=e.SubscriptionStatus||(e.SubscriptionStatus={})).Desired="desired",t.Subscribed="subscribed",t.Unsubscribed="unsubscribed",(n=e.PermissionStatus||(e.PermissionStatus={})).Allowed="allowed",n.NotAllowed="not_allowed"}(Ia);class Oa extends Ia{get isUpstreamPaused(){var e;return null===(e=this.track)||void 0===e?void 0:e.isUpstreamPaused}constructor(t,n,i,s){super(t,n.sid,n.name,s),this.track=void 0,this.handleTrackEnded=()=>{this.emit(e.TrackEvent.Ended)},this.updateInfo(n),this.setTrack(i)}setTrack(t){this.track&&this.track.off(e.TrackEvent.Ended,this.handleTrackEnded),super.setTrack(t),t&&t.on(e.TrackEvent.Ended,this.handleTrackEnded)}get isMuted(){return this.track?this.track.isMuted:super.isMuted}get audioTrack(){return super.audioTrack}get videoTrack(){return super.videoTrack}get isLocal(){return!0}mute(){return Qn(this,void 0,void 0,(function*(){var e;return null===(e=this.track)||void 0===e?void 0:e.mute()}))}unmute(){return Qn(this,void 0,void 0,(function*(){var e;return null===(e=this.track)||void 0===e?void 0:e.unmute()}))}pauseUpstream(){return Qn(this,void 0,void 0,(function*(){var e;yield null===(e=this.track)||void 0===e?void 0:e.pauseUpstream()}))}resumeUpstream(){return Qn(this,void 0,void 0,(function*(){var e;yield null===(e=this.track)||void 0===e?void 0:e.resumeUpstream()}))}getTrackFeatures(){var e;if(jo(this.track)){const t=this.track.getSourceTrackSettings(),n=new Set;return t.autoGainControl&&n.add(nt.TF_AUTO_GAIN_CONTROL),t.echoCancellation&&n.add(nt.TF_ECHO_CANCELLATION),t.noiseSuppression&&n.add(nt.TF_NOISE_SUPPRESSION),t.channelCount&&t.channelCount>1&&n.add(nt.TF_STEREO),(null===(e=this.options)||void 0===e?void 0:e.dtx)||n.add(nt.TF_NO_DTX),this.track.enhancedNoiseCancellation&&n.add(nt.TF_ENHANCED_NOISE_CANCELLATION),Array.from(n.values())}return[]}}e.ConnectionQuality=void 0,function(e){e.Excellent="excellent",e.Good="good",e.Poor="poor",e.Lost="lost",e.Unknown="unknown"}(e.ConnectionQuality||(e.ConnectionQuality={}));class Da extends ei.EventEmitter{get logContext(){var e,t;return Object.assign({},null===(t=null===(e=this.loggerOptions)||void 0===e?void 0:e.loggerContextCb)||void 0===t?void 0:t.call(e))}get isEncrypted(){return this.trackPublications.size>0&&Array.from(this.trackPublications.values()).every((e=>e.isEncrypted))}get isAgent(){var e;return(null===(e=this.permissions)||void 0===e?void 0:e.agent)||this.kind===ct.AGENT}get kind(){return this._kind}get attributes(){return Object.freeze(Object.assign({},this._attributes))}constructor(t,n,i,s,o,r){let a=arguments.length>6&&void 0!==arguments[6]?arguments[6]:ct.STANDARD;var c;super(),this.audioLevel=0,this.isSpeaking=!1,this._connectionQuality=e.ConnectionQuality.Unknown,this.log=qn,this.log=Wn(null!==(c=null==r?void 0:r.loggerName)&&void 0!==c?c:e.LoggerNames.Participant),this.loggerOptions=r,this.setMaxListeners(100),this.sid=t,this.identity=n,this.name=i,this.metadata=s,this.audioTrackPublications=new Map,this.videoTrackPublications=new Map,this.trackPublications=new Map,this._kind=a,this._attributes=null!=o?o:{}}getTrackPublications(){return Array.from(this.trackPublications.values())}getTrackPublication(e){for(const[,t]of this.trackPublications)if(t.source===e)return t}getTrackPublicationByName(e){for(const[,t]of this.trackPublications)if(t.trackName===e)return t}get connectionQuality(){return this._connectionQuality}get isCameraEnabled(){var e;const t=this.getTrackPublication(Ks.Source.Camera);return!(null===(e=null==t?void 0:t.isMuted)||void 0===e||e)}get isMicrophoneEnabled(){var e;const t=this.getTrackPublication(Ks.Source.Microphone);return!(null===(e=null==t?void 0:t.isMuted)||void 0===e||e)}get isScreenShareEnabled(){return!!this.getTrackPublication(Ks.Source.ScreenShare)}get isLocal(){return!1}get joinedAt(){return this.participantInfo?new Date(1e3*Number.parseInt(this.participantInfo.joinedAt.toString())):new Date}updateInfo(e){return!(this.participantInfo&&this.participantInfo.sid===e.sid&&this.participantInfo.version>e.version)&&(this.identity=e.identity,this.sid=e.sid,this._setName(e.name),this._setMetadata(e.metadata),this._setAttributes(e.attributes),e.permission&&this.setPermissions(e.permission),this.participantInfo=e,this.log.trace("update participant info",Object.assign(Object.assign({},this.logContext),{info:e})),!0)}_setMetadata(t){const n=this.metadata!==t,i=this.metadata;this.metadata=t,n&&this.emit(e.ParticipantEvent.ParticipantMetadataChanged,i)}_setName(t){const n=this.name!==t;this.name=t,n&&this.emit(e.ParticipantEvent.ParticipantNameChanged,t)}_setAttributes(t){const n=function(e,t){var n;void 0===e&&(e={}),void 0===t&&(t={});const i=[...Object.keys(t),...Object.keys(e)],s={};for(const o of i)e[o]!==t[o]&&(s[o]=null!==(n=t[o])&&void 0!==n?n:"");return s}(this.attributes,t);this._attributes=t,Object.keys(n).length>0&&this.emit(e.ParticipantEvent.AttributesChanged,n)}setPermissions(t){var n,i,s,o,r,a;const c=this.permissions,d=t.canPublish!==(null===(n=this.permissions)||void 0===n?void 0:n.canPublish)||t.canSubscribe!==(null===(i=this.permissions)||void 0===i?void 0:i.canSubscribe)||t.canPublishData!==(null===(s=this.permissions)||void 0===s?void 0:s.canPublishData)||t.hidden!==(null===(o=this.permissions)||void 0===o?void 0:o.hidden)||t.recorder!==(null===(r=this.permissions)||void 0===r?void 0:r.recorder)||t.canPublishSources.length!==this.permissions.canPublishSources.length||t.canPublishSources.some(((e,t)=>{var n;return e!==(null===(n=this.permissions)||void 0===n?void 0:n.canPublishSources[t])}))||t.canSubscribeMetrics!==(null===(a=this.permissions)||void 0===a?void 0:a.canSubscribeMetrics);return this.permissions=t,d&&this.emit(e.ParticipantEvent.ParticipantPermissionsChanged,c),d}setIsSpeaking(t){t!==this.isSpeaking&&(this.isSpeaking=t,t&&(this.lastSpokeAt=new Date),this.emit(e.ParticipantEvent.IsSpeakingChanged,t))}setConnectionQuality(t){const n=this._connectionQuality;this._connectionQuality=function(t){switch(t){case Xe.EXCELLENT:return e.ConnectionQuality.Excellent;case Xe.GOOD:return e.ConnectionQuality.Good;case Xe.POOR:return e.ConnectionQuality.Poor;case Xe.LOST:return e.ConnectionQuality.Lost;default:return e.ConnectionQuality.Unknown}}(t),n!==this._connectionQuality&&this.emit(e.ParticipantEvent.ConnectionQualityChanged,this._connectionQuality)}setAudioContext(e){this.audioContext=e,this.audioTrackPublications.forEach((t=>jo(t.track)&&t.track.setAudioContext(e)))}addTrackPublication(t){t.on(e.TrackEvent.Muted,(()=>{this.emit(e.ParticipantEvent.TrackMuted,t)})),t.on(e.TrackEvent.Unmuted,(()=>{this.emit(e.ParticipantEvent.TrackUnmuted,t)}));const n=t;switch(n.track&&(n.track.sid=t.trackSid),this.trackPublications.set(t.trackSid,t),t.kind){case Ks.Kind.Audio:this.audioTrackPublications.set(t.trackSid,t);break;case Ks.Kind.Video:this.videoTrackPublications.set(t.trackSid,t)}}}const xa=15e3;class Ma extends Da{constructor(e,t,n,i,s){super(e,t,void 0,void 0,void 0,{loggerName:i.loggerName,loggerContextCb:()=>this.engine.logContext}),this.pendingPublishing=new Set,this.pendingPublishPromises=new Map,this.participantTrackPermissions=[],this.allParticipantsAllowedToSubscribe=!0,this.encryptionType=dt.NONE,this.enabledPublishVideoCodecs=[],this.pendingAcks=new Map,this.pendingResponses=new Map,this.handleReconnecting=()=>{this.reconnectFuture||(this.reconnectFuture=new xo)},this.handleReconnected=()=>{var e,t;null===(t=null===(e=this.reconnectFuture)||void 0===e?void 0:e.resolve)||void 0===t||t.call(e),this.reconnectFuture=void 0,this.updateTrackSubscriptionPermissions()},this.handleDisconnected=()=>{var e,t;this.reconnectFuture&&(this.reconnectFuture.promise.catch((e=>this.log.warn(e.message,this.logContext))),null===(t=null===(e=this.reconnectFuture)||void 0===e?void 0:e.reject)||void 0===t||t.call(e,"Got disconnected during reconnection attempt"),this.reconnectFuture=void 0)},this.handleSignalRequestResponse=e=>{const{requestId:t,reason:n,message:i}=e,s=this.pendingSignalRequests.get(t);s&&(n!==Nn.OK&&s.reject(new Ns(i,n)),this.pendingSignalRequests.delete(t))},this.handleDataPacket=e=>{switch(e.value.case){case"rpcResponse":let t=e.value.value,n=null,i=null;"payload"===t.value.case?n=t.value.value:"error"===t.value.case&&(i=Br.fromProto(t.value.value)),this.handleIncomingRpcResponse(t.requestId,n,i);break;case"rpcAck":let s=e.value.value;this.handleIncomingRpcAck(s.requestId)}},this.updateTrackSubscriptionPermissions=()=>{this.log.debug("updating track subscription permissions",Object.assign(Object.assign({},this.logContext),{allParticipantsAllowed:this.allParticipantsAllowedToSubscribe,participantTrackPermissions:this.participantTrackPermissions})),this.engine.client.sendUpdateSubscriptionPermissions(this.allParticipantsAllowedToSubscribe,this.participantTrackPermissions.map((e=>function(e){var t,n,i;if(!e.participantSid&&!e.participantIdentity)throw new Error("Invalid track permission, must provide at least one of participantIdentity and participantSid");return new Tn({participantIdentity:null!==(t=e.participantIdentity)&&void 0!==t?t:"",participantSid:null!==(n=e.participantSid)&&void 0!==n?n:"",allTracks:null!==(i=e.allowAll)&&void 0!==i&&i,trackSids:e.allowedTrackSids||[]})}(e))))},this.onTrackUnmuted=e=>{this.onTrackMuted(e,e.isUpstreamPaused)},this.onTrackMuted=(e,t)=>{void 0===t&&(t=!0),e.sid?this.engine.updateMuteStatus(e.sid,t):this.log.error("could not update mute status for unpublished track",Object.assign(Object.assign({},this.logContext),tr(e)))},this.onTrackUpstreamPaused=e=>{this.log.debug("upstream paused",Object.assign(Object.assign({},this.logContext),tr(e))),this.onTrackMuted(e,!0)},this.onTrackUpstreamResumed=e=>{this.log.debug("upstream resumed",Object.assign(Object.assign({},this.logContext),tr(e))),this.onTrackMuted(e,e.isMuted)},this.onTrackFeatureUpdate=e=>{const t=this.audioTrackPublications.get(e.sid);t?this.engine.client.sendUpdateLocalAudioTrack(t.trackSid,t.getTrackFeatures()):this.log.warn("Could not update local audio track settings, missing publication for track ".concat(e.sid),this.logContext)},this.handleSubscribedQualityUpdate=e=>Qn(this,void 0,void 0,(function*(){var t,n,i,s,o,r;if(!(null===(o=this.roomOptions)||void 0===o?void 0:o.dynacast))return;const a=this.videoTrackPublications.get(e.trackSid);if(a)if(e.subscribedCodecs.length>0){if(!a.videoTrack)return;const o=yield a.videoTrack.setPublishingCodecs(e.subscribedCodecs);try{for(var c,d=!0,l=Xn(o);!(t=(c=yield l.next()).done);d=!0){s=c.value,d=!1;const e=s;Qs(e)&&(this.log.debug("publish ".concat(e," for ").concat(a.videoTrack.sid),Object.assign(Object.assign({},this.logContext),tr(a))),yield this.publishAdditionalCodecForTrack(a.videoTrack,e,a.options))}}catch(e){n={error:e}}finally{try{d||t||!(i=l.return)||(yield i.call(l))}finally{if(n)throw n.error}}}else e.subscribedQualities.length>0&&(yield null===(r=a.videoTrack)||void 0===r?void 0:r.setPublishingLayers(e.subscribedQualities));else this.log.warn("received subscribed quality update for unknown track",Object.assign(Object.assign({},this.logContext),{trackSid:e.trackSid}))})),this.handleLocalTrackUnpublished=e=>{const t=this.trackPublications.get(e.trackSid);t?this.unpublishTrack(t.track):this.log.warn("received unpublished event for unknown track",Object.assign(Object.assign({},this.logContext),{trackSid:e.trackSid}))},this.handleTrackEnded=e=>Qn(this,void 0,void 0,(function*(){if(e.source===Ks.Source.ScreenShare||e.source===Ks.Source.ScreenShareAudio)this.log.debug("unpublishing local track due to TrackEnded",Object.assign(Object.assign({},this.logContext),tr(e))),this.unpublishTrack(e);else if(e.isUserProvided)yield e.mute();else if(Vo(e)||Bo(e))try{if(mo())try{const t=yield null===navigator||void 0===navigator?void 0:navigator.permissions.query({name:e.source===Ks.Source.Camera?"camera":"microphone"});if(t&&"denied"===t.state)throw this.log.warn("user has revoked access to ".concat(e.source),Object.assign(Object.assign({},this.logContext),tr(e))),t.onchange=()=>{"denied"!==t.state&&(e.isMuted||e.restartTrack(),t.onchange=null)},new Error("GetUserMedia Permission denied")}catch(e){}e.isMuted||(this.log.debug("track ended, attempting to use a different device",Object.assign(Object.assign({},this.logContext),tr(e))),Vo(e)?yield e.restartTrack({deviceId:"default"}):yield e.restartTrack())}catch(t){this.log.warn("could not restart track, muting instead",Object.assign(Object.assign({},this.logContext),tr(e))),yield e.mute()}})),this.audioTrackPublications=new Map,this.videoTrackPublications=new Map,this.trackPublications=new Map,this.engine=n,this.roomOptions=i,this.setupEngine(n),this.activeDeviceMap=new Map([["audioinput","default"],["videoinput","default"],["audiooutput","default"]]),this.pendingSignalRequests=new Map,this.rpcHandlers=s}get lastCameraError(){return this.cameraError}get lastMicrophoneError(){return this.microphoneError}get isE2EEEnabled(){return this.encryptionType!==dt.NONE}getTrackPublication(e){const t=super.getTrackPublication(e);if(t)return t}getTrackPublicationByName(e){const t=super.getTrackPublicationByName(e);if(t)return t}setupEngine(t){this.engine=t,this.engine.on(e.EngineEvent.RemoteMute,((e,t)=>{const n=this.trackPublications.get(e);n&&n.track&&(t?n.mute():n.unmute())})),this.engine.on(e.EngineEvent.Connected,this.handleReconnected).on(e.EngineEvent.SignalRestarted,this.handleReconnected).on(e.EngineEvent.SignalResumed,this.handleReconnected).on(e.EngineEvent.Restarting,this.handleReconnecting).on(e.EngineEvent.Resuming,this.handleReconnecting).on(e.EngineEvent.LocalTrackUnpublished,this.handleLocalTrackUnpublished).on(e.EngineEvent.SubscribedQualityUpdate,this.handleSubscribedQualityUpdate).on(e.EngineEvent.Disconnected,this.handleDisconnected).on(e.EngineEvent.SignalRequestResponse,this.handleSignalRequestResponse).on(e.EngineEvent.DataPacketReceived,this.handleDataPacket)}setMetadata(e){return Qn(this,void 0,void 0,(function*(){yield this.requestMetadataUpdate({metadata:e})}))}setName(e){return Qn(this,void 0,void 0,(function*(){yield this.requestMetadataUpdate({name:e})}))}setAttributes(e){return Qn(this,void 0,void 0,(function*(){yield this.requestMetadataUpdate({attributes:e})}))}requestMetadataUpdate(e){return Qn(this,arguments,void 0,(function(e){var t=this;let{metadata:n,name:i,attributes:s}=e;return function*(){return new Promise(((e,o)=>Qn(t,void 0,void 0,(function*(){var t,r;try{let a=!1;const c=yield this.engine.client.sendUpdateLocalMetadata(null!==(t=null!=n?n:this.metadata)&&void 0!==t?t:"",null!==(r=null!=i?i:this.name)&&void 0!==r?r:"",s),d=performance.now();for(this.pendingSignalRequests.set(c,{resolve:e,reject:e=>{o(e),a=!0},values:{name:i,metadata:n,attributes:s}});performance.now()-d<5e3&&!a;){if((!i||this.name===i)&&(!n||this.metadata===n)&&(!s||Object.entries(s).every((e=>{let[t,n]=e;return this.attributes[t]===n||""===n&&!this.attributes[t]}))))return this.pendingSignalRequests.delete(c),void e();yield to(50)}o(new Ns("Request to update local metadata timed out","TimeoutError"))}catch(e){e instanceof Error&&o(e)}}))))}()}))}setCameraEnabled(e,t,n){return this.setTrackEnabled(Ks.Source.Camera,e,t,n)}setMicrophoneEnabled(e,t,n){return this.setTrackEnabled(Ks.Source.Microphone,e,t,n)}setScreenShareEnabled(e,t,n){return this.setTrackEnabled(Ks.Source.ScreenShare,e,t,n)}setPermissions(t){const n=this.permissions,i=super.setPermissions(t);return i&&n&&this.emit(e.ParticipantEvent.ParticipantPermissionsChanged,n),i}setE2EEEnabled(e){return Qn(this,void 0,void 0,(function*(){this.encryptionType=e?dt.GCM:dt.NONE,yield this.republishAllTracks(void 0,!1)}))}setTrackEnabled(t,n,i,s){return Qn(this,void 0,void 0,(function*(){var o,r;this.log.debug("setTrackEnabled",Object.assign(Object.assign({},this.logContext),{source:t,enabled:n})),this.republishPromise&&(yield this.republishPromise);let a=this.getTrackPublication(t);if(n)if(a)yield a.unmute();else{let n;if(this.pendingPublishing.has(t)){const e=yield this.waitForPendingPublicationOfSource(t);return e||this.log.info("waiting for pending publication promise timed out",Object.assign(Object.assign({},this.logContext),{source:t})),yield null==e?void 0:e.unmute(),e}this.pendingPublishing.add(t);try{switch(t){case Ks.Source.Camera:n=yield this.createTracks({video:null===(o=i)||void 0===o||o});break;case Ks.Source.Microphone:n=yield this.createTracks({audio:null===(r=i)||void 0===r||r});break;case Ks.Source.ScreenShare:n=yield this.createScreenTracks(Object.assign({},i));break;default:throw new Is(t)}}catch(i){throw null==n||n.forEach((e=>{e.stop()})),i instanceof Error&&this.emit(e.ParticipantEvent.MediaDevicesError,i),this.pendingPublishing.delete(t),i}try{const e=[];for(const t of n)this.log.info("publishing track",Object.assign(Object.assign({},this.logContext),tr(t))),e.push(this.publishTrack(t,s));const t=yield Promise.all(e);[a]=t}catch(e){throw null==n||n.forEach((e=>{e.stop()})),e}finally{this.pendingPublishing.delete(t)}}else if(!(null==a?void 0:a.track)&&this.pendingPublishing.has(t)&&(a=yield this.waitForPendingPublicationOfSource(t),a||this.log.info("waiting for pending publication promise timed out",Object.assign(Object.assign({},this.logContext),{source:t}))),a&&a.track)if(t===Ks.Source.ScreenShare){a=yield this.unpublishTrack(a.track);const e=this.getTrackPublication(Ks.Source.ScreenShareAudio);e&&e.track&&this.unpublishTrack(e.track)}else yield a.mute();return a}))}enableCameraAndMicrophone(){return Qn(this,void 0,void 0,(function*(){if(!this.pendingPublishing.has(Ks.Source.Camera)&&!this.pendingPublishing.has(Ks.Source.Microphone)){this.pendingPublishing.add(Ks.Source.Camera),this.pendingPublishing.add(Ks.Source.Microphone);try{const e=yield this.createTracks({audio:!0,video:!0});yield Promise.all(e.map((e=>this.publishTrack(e))))}finally{this.pendingPublishing.delete(Ks.Source.Camera),this.pendingPublishing.delete(Ks.Source.Microphone)}}}))}createTracks(t){return Qn(this,void 0,void 0,(function*(){var n,i;null!=t||(t={});const s=Go(t,null===(n=this.roomOptions)||void 0===n?void 0:n.audioCaptureDefaults,null===(i=this.roomOptions)||void 0===i?void 0:i.videoCaptureDefaults),{audioProcessor:o,videoProcessor:r,optionsWithoutProcessor:a}=nr(s),c=Jo(a);let d;try{d=yield navigator.mediaDevices.getUserMedia(c)}catch(e){throw e instanceof Error&&(c.audio&&(this.microphoneError=e),c.video&&(this.cameraError=e)),e}return c.audio&&(this.microphoneError=void 0,this.emit(e.ParticipantEvent.AudioStreamAcquired)),c.video&&(this.cameraError=void 0),Promise.all(d.getTracks().map((e=>Qn(this,void 0,void 0,(function*(){let t;const n="audio"===e.kind?c.audio:c.video;"boolean"!=typeof n&&(t=n);const i=zr(e,t,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});return i.kind===Ks.Kind.Video?i.source=Ks.Source.Camera:i.kind===Ks.Kind.Audio&&(i.source=Ks.Source.Microphone,i.setAudioContext(this.audioContext)),i.mediaStream=d,jo(i)&&o?yield i.setProcessor(o):Fo(i)&&r&&(yield i.setProcessor(r)),i})))))}))}createScreenTracks(t){return Qn(this,void 0,void 0,(function*(){if(void 0===t&&(t={}),void 0===navigator.mediaDevices.getDisplayMedia)throw new Rs("getDisplayMedia not supported");void 0!==t.resolution||ho()||(t.resolution=$s.h1080fps30.resolution);const n=Zo(t),i=yield navigator.mediaDevices.getDisplayMedia(n),s=i.getVideoTracks();if(0===s.length)throw new Is("no video track found");const o=new ra(s[0],void 0,!1,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});o.source=Ks.Source.ScreenShare,t.contentHint&&(o.mediaStreamTrack.contentHint=t.contentHint);const r=[o];if(i.getAudioTracks().length>0){this.emit(e.ParticipantEvent.AudioStreamAcquired);const t=new Gr(i.getAudioTracks()[0],void 0,!1,this.audioContext,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});t.source=Ks.Source.ScreenShareAudio,r.push(t)}return r}))}publishTrack(e,t){return Qn(this,void 0,void 0,(function*(){return this.publishOrRepublishTrack(e,t)}))}publishOrRepublishTrack(e,t){return Qn(this,arguments,void 0,(function(e,t){var n=this;let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function*(){var s,o,r,a;let c,d;if(Vo(e)&&e.setAudioContext(n.audioContext),yield null===(s=n.reconnectFuture)||void 0===s?void 0:s.promise,n.republishPromise&&!i&&(yield n.republishPromise),Uo(e)&&n.pendingPublishPromises.has(e)&&(yield n.pendingPublishPromises.get(e)),e instanceof MediaStreamTrack)c=e.getConstraints();else{let t;switch(c=e.constraints,e.source){case Ks.Source.Microphone:t="audioinput";break;case Ks.Source.Camera:t="videoinput"}t&&n.activeDeviceMap.has(t)&&(c=Object.assign(Object.assign({},c),{deviceId:n.activeDeviceMap.get(t)}))}if(e instanceof MediaStreamTrack)switch(e.kind){case"audio":e=new Gr(e,c,!0,n.audioContext,{loggerName:n.roomOptions.loggerName,loggerContextCb:()=>n.logContext});break;case"video":e=new ra(e,c,!0,{loggerName:n.roomOptions.loggerName,loggerContextCb:()=>n.logContext});break;default:throw new Is("unsupported MediaStreamTrack kind ".concat(e.kind))}else e.updateLoggerOptions({loggerName:n.roomOptions.loggerName,loggerContextCb:()=>n.logContext});if(n.trackPublications.forEach((t=>{t.track&&t.track===e&&(d=t)})),d)return n.log.warn("track has already been published, skipping",Object.assign(Object.assign({},n.logContext),tr(d))),d;const l="channelCount"in e.mediaStreamTrack.getSettings()&&2===e.mediaStreamTrack.getSettings().channelCount||2===e.mediaStreamTrack.getConstraints().channelCount,u=null!==(o=null==t?void 0:t.forceStereo)&&void 0!==o?o:l;u&&(t||(t={}),void 0===t.dtx&&n.log.info("Opus DTX will be disabled for stereo tracks by default. Enable them explicitly to make it work.",Object.assign(Object.assign({},n.logContext),tr(e))),void 0===t.red&&n.log.info("Opus RED will be disabled for stereo tracks by default. Enable them explicitly to make it work."),null!==(r=t.dtx)&&void 0!==r||(t.dtx=!1),null!==(a=t.red)&&void 0!==a||(t.red=!1));const h=Object.assign(Object.assign({},n.roomOptions.publishDefaults),t);!function(){const e=Ls(),t="17.2";if(e)return"Safari"!==e.name&&"iOS"!==e.os||!!("iOS"===e.os&&e.osVersion&&yo(t,e.osVersion)>=0)||"Safari"===e.name&&yo(t,e.version)>=0}()&&n.roomOptions.e2ee&&(n.log.info("End-to-end encryption is set up, simulcast publishing will be disabled on Safari versions and iOS browsers running iOS < v17.2",Object.assign({},n.logContext)),h.simulcast=!1),h.source&&(e.source=h.source);const p=n.publish(e,h,u);n.pendingPublishPromises.set(e,p);try{return yield p}catch(e){throw e}finally{n.pendingPublishPromises.delete(e)}}()}))}hasPermissionsToPublish(e){if(!this.permissions)return this.log.warn("no permissions present for publishing track",Object.assign(Object.assign({},this.logContext),tr(e))),!1;const{canPublish:t,canPublishSources:n}=this.permissions;return!(!t||0!==n.length&&!n.map((e=>function(e){switch(e){case Qe.CAMERA:return Ks.Source.Camera;case Qe.MICROPHONE:return Ks.Source.Microphone;case Qe.SCREEN_SHARE:return Ks.Source.ScreenShare;case Qe.SCREEN_SHARE_AUDIO:return Ks.Source.ScreenShareAudio;default:return Ks.Source.Unknown}}(e))).includes(e.source))||(this.log.warn("insufficient permissions to publish",Object.assign(Object.assign({},this.logContext),tr(e))),!1)}publish(t,n,i){return Qn(this,void 0,void 0,(function*(){var s,o,r,a,c,d,l,u,h,p;if(!this.hasPermissionsToPublish(t))throw new Ms("failed to publish track, insufficient permissions",403);Array.from(this.trackPublications.values()).find((e=>Uo(t)&&e.source===t.source))&&t.source!==Ks.Source.Unknown&&this.log.info("publishing a second track with the same source: ".concat(t.source),Object.assign(Object.assign({},this.logContext),tr(t))),n.stopMicTrackOnMute&&jo(t)&&(t.stopOnMute=!0),t.source===Ks.Source.ScreenShare&&lo()&&(n.simulcast=!1),"av1"!==n.videoCodec||so()||(n.videoCodec=void 0),"vp9"!==n.videoCodec||oo()||(n.videoCodec=void 0),void 0===n.videoCodec&&(n.videoCodec=Mr),this.enabledPublishVideoCodecs.length>0&&(this.enabledPublishVideoCodecs.some((e=>n.videoCodec===$o(e.mime)))||(n.videoCodec=$o(this.enabledPublishVideoCodecs[0].mime)));const m=n.videoCodec;t.on(e.TrackEvent.Muted,this.onTrackMuted),t.on(e.TrackEvent.Unmuted,this.onTrackUnmuted),t.on(e.TrackEvent.Ended,this.handleTrackEnded),t.on(e.TrackEvent.UpstreamPaused,this.onTrackUpstreamPaused),t.on(e.TrackEvent.UpstreamResumed,this.onTrackUpstreamResumed),t.on(e.TrackEvent.AudioTrackFeatureUpdate,this.onTrackFeatureUpdate);const g=new zt({cid:t.mediaStreamTrack.id,name:n.name,type:Ks.kindToProto(t.kind),muted:t.isMuted,source:Ks.sourceToProto(t.source),disableDtx:!(null===(s=n.dtx)||void 0===s||s),encryption:this.encryptionType,stereo:i,disableRed:this.isE2EEEnabled||!(null===(o=n.red)||void 0===o||o),stream:null==n?void 0:n.stream,backupCodecPolicy:null==n?void 0:n.backupCodecPolicy});let v;if(t.kind===Ks.Kind.Video){let e={width:0,height:0};try{e=yield t.waitForDimensions()}catch(n){const i=null!==(a=null===(r=this.roomOptions.videoCaptureDefaults)||void 0===r?void 0:r.resolution)&&void 0!==a?a:Xs.h720.resolution;e={width:i.width,height:i.height},this.log.error("could not determine track dimensions, using defaults",Object.assign(Object.assign(Object.assign({},this.logContext),tr(t)),{dims:e}))}g.width=e.width,g.height=e.height,Bo(t)&&(ro(m)&&(t.source===Ks.Source.ScreenShare&&(n.scalabilityMode="L1T3","contentHint"in t.mediaStreamTrack&&(t.mediaStreamTrack.contentHint="motion",this.log.info("forcing contentHint to motion for screenshare with SVC codecs",Object.assign(Object.assign({},this.logContext),tr(t))))),n.scalabilityMode=null!==(c=n.scalabilityMode)&&void 0!==c?c:"L3T3_KEY"),g.simulcastCodecs=[new Gt({codec:m,cid:t.mediaStreamTrack.id})],!0===n.backupCodec&&(n.backupCodec={codec:Mr}),n.backupCodec&&m!==n.backupCodec.codec&&g.encryption===dt.NONE&&(this.roomOptions.dynacast||(this.roomOptions.dynacast=!0),g.simulcastCodecs.push(new Gt({codec:n.backupCodec.codec,cid:""})))),v=ta(t.source===Ks.Source.ScreenShare,g.width,g.height,n),g.layers=da(g.width,g.height,v,ro(n.videoCodec))}else t.kind===Ks.Kind.Audio&&(v=[{maxBitrate:null===(d=n.audioPreset)||void 0===d?void 0:d.maxBitrate,priority:null!==(u=null===(l=n.audioPreset)||void 0===l?void 0:l.priority)&&void 0!==u?u:"high",networkPriority:null!==(p=null===(h=n.audioPreset)||void 0===h?void 0:h.priority)&&void 0!==p?p:"high"}]);if(!this.engine||this.engine.isClosed)throw new Ds("cannot publish track when not connected");const f=()=>Qn(this,void 0,void 0,(function*(){var e,i,s;if(!this.engine.pcManager)throw new Ds("pcManager is not ready");if(t.sender=yield this.engine.createSender(t,n,v),Bo(t)&&(null!==(e=n.degradationPreference)&&void 0!==e||(n.degradationPreference=function(e){return e.source===Ks.Source.ScreenShare||e.constraints.height&&Mo(e.constraints.height)>=1080?"maintain-resolution":"balanced"}(t)),t.setDegradationPreference(n.degradationPreference)),v)if(lo()&&t.kind===Ks.Kind.Audio){let e;for(const n of this.engine.pcManager.publisher.getTransceivers())if(n.sender===t.sender){e=n;break}e&&this.engine.pcManager.publisher.setTrackCodecBitrate({transceiver:e,codec:"opus",maxbr:(null===(i=v[0])||void 0===i?void 0:i.maxBitrate)?v[0].maxBitrate/1e3:0})}else t.codec&&ro(t.codec)&&(null===(s=v[0])||void 0===s?void 0:s.maxBitrate)&&this.engine.pcManager.publisher.setTrackCodecBitrate({cid:g.cid,codec:t.codec,maxbr:v[0].maxBitrate/1e3});yield this.engine.negotiate()}));let k;if(this.enabledPublishVideoCodecs.length>0){k=(yield Promise.all([this.engine.addTrack(g),f()]))[0]}else{let e;if(k=yield this.engine.addTrack(g),k.codecs.forEach((t=>{void 0===e&&(e=t.mimeType)})),e&&t.kind===Ks.Kind.Video){const i=$o(e);i!==m&&(this.log.debug("falling back to server selected codec",Object.assign(Object.assign(Object.assign({},this.logContext),tr(t)),{codec:i})),n.videoCodec=i,v=ta(t.source===Ks.Source.ScreenShare,g.width,g.height,n))}yield f()}const b=new Oa(t.kind,k,t,{loggerName:this.roomOptions.loggerName,loggerContextCb:()=>this.logContext});return b.options=n,t.sid=k.sid,this.log.debug("publishing ".concat(t.kind," with encodings"),Object.assign(Object.assign({},this.logContext),{encodings:v,trackInfo:k})),Bo(t)?t.startMonitor(this.engine.client):Vo(t)&&t.startMonitor(),this.addTrackPublication(b),this.emit(e.ParticipantEvent.LocalTrackPublished,b),b}))}get isLocal(){return!0}publishAdditionalCodecForTrack(e,t,n){return Qn(this,void 0,void 0,(function*(){var i;if(this.encryptionType!==dt.NONE)return;let s;if(this.trackPublications.forEach((t=>{t.track&&t.track===e&&(s=t)})),!s)throw new Is("track is not published");if(!Bo(e))throw new Is("track is not a video track");const o=Object.assign(Object.assign({},null===(i=this.roomOptions)||void 0===i?void 0:i.publishDefaults),n),r=function(e,t,n){var i,s,o,r;if(!n.backupCodec||!0===n.backupCodec||n.backupCodec.codec===n.videoCodec)return;t!==n.backupCodec.codec&&qn.warn("requested a different codec than specified as backup",{serverRequested:t,backup:n.backupCodec.codec}),n.videoCodec=t,n.videoEncoding=n.backupCodec.encoding;const a=e.mediaStreamTrack.getSettings(),c=null!==(i=a.width)&&void 0!==i?i:null===(s=e.dimensions)||void 0===s?void 0:s.width,d=null!==(o=a.height)&&void 0!==o?o:null===(r=e.dimensions)||void 0===r?void 0:r.height;return e.source===Ks.Source.ScreenShare&&n.simulcast&&(n.simulcast=!1),ta(e.source===Ks.Source.ScreenShare,c,d,n)}(e,t,o);if(!r)return void this.log.info("backup codec has been disabled, ignoring request to add additional codec for track",Object.assign(Object.assign({},this.logContext),tr(e)));const a=e.addSimulcastTrack(t,r);if(!a)return;const c=new zt({cid:a.mediaStreamTrack.id,type:Ks.kindToProto(e.kind),muted:e.isMuted,source:Ks.sourceToProto(e.source),sid:e.sid,simulcastCodecs:[{codec:o.videoCodec,cid:a.mediaStreamTrack.id}]});if(c.layers=da(c.width,c.height,r),!this.engine||this.engine.isClosed)throw new Ds("cannot publish track when not connected");const d=(yield Promise.all([this.engine.addTrack(c),(()=>Qn(this,void 0,void 0,(function*(){yield this.engine.createSimulcastSender(e,a,o,r),yield this.engine.negotiate()})))()]))[0];this.log.debug("published ".concat(t," for track ").concat(e.sid),Object.assign(Object.assign({},this.logContext),{encodings:r,trackInfo:d}))}))}unpublishTrack(t,n){return Qn(this,void 0,void 0,(function*(){var i,s;if(Uo(t)){const e=this.pendingPublishPromises.get(t);e&&(this.log.info("awaiting publish promise before attempting to unpublish",Object.assign(Object.assign({},this.logContext),tr(t))),yield e)}const o=this.getPublicationForTrack(t),r=o?tr(o):void 0;if(this.log.debug("unpublishing track",Object.assign(Object.assign({},this.logContext),r)),!o||!o.track)return void this.log.warn("track was not unpublished because no publication was found",Object.assign(Object.assign({},this.logContext),r));(t=o.track).off(e.TrackEvent.Muted,this.onTrackMuted),t.off(e.TrackEvent.Unmuted,this.onTrackUnmuted),t.off(e.TrackEvent.Ended,this.handleTrackEnded),t.off(e.TrackEvent.UpstreamPaused,this.onTrackUpstreamPaused),t.off(e.TrackEvent.UpstreamResumed,this.onTrackUpstreamResumed),t.off(e.TrackEvent.AudioTrackFeatureUpdate,this.onTrackFeatureUpdate),void 0===n&&(n=null===(s=null===(i=this.roomOptions)||void 0===i?void 0:i.stopLocalTrackOnUnpublish)||void 0===s||s),n?t.stop():t.stopMonitor();let a=!1;const c=t.sender;if(t.sender=void 0,this.engine.pcManager&&this.engine.pcManager.currentState<jr.FAILED&&c)try{for(const e of this.engine.pcManager.publisher.getTransceivers())e.sender===c&&(e.direction="inactive",a=!0);if(this.engine.removeTrack(c)&&(a=!0),Bo(t)){for(const[,e]of t.simulcastCodecs)e.sender&&(this.engine.removeTrack(e.sender)&&(a=!0),e.sender=void 0);t.simulcastCodecs.clear()}}catch(e){this.log.warn("failed to unpublish track",Object.assign(Object.assign(Object.assign({},this.logContext),r),{error:e}))}switch(this.trackPublications.delete(o.trackSid),o.kind){case Ks.Kind.Audio:this.audioTrackPublications.delete(o.trackSid);break;case Ks.Kind.Video:this.videoTrackPublications.delete(o.trackSid)}return this.emit(e.ParticipantEvent.LocalTrackUnpublished,o),o.setTrack(void 0),a&&(yield this.engine.negotiate()),o}))}unpublishTracks(e){return Qn(this,void 0,void 0,(function*(){return(yield Promise.all(e.map((e=>this.unpublishTrack(e))))).filter((e=>!!e))}))}republishAllTracks(e){return Qn(this,arguments,void 0,(function(e){var t=this;let n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return function*(){t.republishPromise&&(yield t.republishPromise),t.republishPromise=new Promise(((i,s)=>Qn(t,void 0,void 0,(function*(){try{const t=[];this.trackPublications.forEach((n=>{n.track&&(e&&(n.options=Object.assign(Object.assign({},n.options),e)),t.push(n))})),yield Promise.all(t.map((e=>Qn(this,void 0,void 0,(function*(){const t=e.track;yield this.unpublishTrack(t,!1),!n||t.isMuted||t.source===Ks.Source.ScreenShare||t.source===Ks.Source.ScreenShareAudio||!Vo(t)&&!Bo(t)||t.isUserProvided||(this.log.debug("restarting existing track",Object.assign(Object.assign({},this.logContext),{track:e.trackSid})),yield t.restartTrack()),yield this.publishOrRepublishTrack(t,e.options,!0)}))))),i()}catch(e){s(e)}finally{this.republishPromise=void 0}})))),yield t.republishPromise}()}))}publishData(e){return Qn(this,arguments,void 0,(function(e){var t=this;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return function*(){const i=n.reliable?mt.RELIABLE:mt.LOSSY,s=n.destinationIdentities,o=n.topic,r=new pt({kind:i,value:{case:"user",value:new ft({participantIdentity:t.identity,payload:e,destinationIdentities:s,topic:o})}});yield t.engine.sendDataPacket(r,i)}()}))}publishDtmf(e,t){return Qn(this,void 0,void 0,(function*(){const n=new pt({kind:mt.RELIABLE,value:{case:"sipDtmf",value:new kt({code:e,digit:t})}});yield this.engine.sendDataPacket(n,mt.RELIABLE)}))}sendChatMessage(t,n){return Qn(this,void 0,void 0,(function*(){const i={id:crypto.randomUUID(),message:t,timestamp:Date.now(),attachedFiles:null==n?void 0:n.attachments},s=new pt({value:{case:"chatMessage",value:new Tt(Object.assign(Object.assign({},i),{timestamp:x.parse(i.timestamp)}))}});return yield this.engine.sendDataPacket(s,mt.RELIABLE),this.emit(e.ParticipantEvent.ChatMessage,i),i}))}editChatMessage(t,n){return Qn(this,void 0,void 0,(function*(){const i=Object.assign(Object.assign({},n),{message:t,editTimestamp:Date.now()}),s=new pt({value:{case:"chatMessage",value:new Tt(Object.assign(Object.assign({},i),{timestamp:x.parse(i.timestamp),editTimestamp:x.parse(i.editTimestamp)}))}});return yield this.engine.sendDataPacket(s,mt.RELIABLE),this.emit(e.ParticipantEvent.ChatMessage,i),i}))}sendText(e,t){return Qn(this,void 0,void 0,(function*(){var n;const i=crypto.randomUUID(),s=(new TextEncoder).encode(e).byteLength,o=null===(n=null==t?void 0:t.attachments)||void 0===n?void 0:n.map((()=>crypto.randomUUID())),r=new Array(o?o.length+1:1).fill(0),a=(e,n)=>{var i;r[n]=e;const s=r.reduce(((e,t)=>e+t),0);null===(i=null==t?void 0:t.onProgress)||void 0===i||i.call(t,s)},c=yield this.streamText({streamId:i,totalSize:s,destinationIdentities:null==t?void 0:t.destinationIdentities,topic:null==t?void 0:t.topic,attachedStreamIds:o,attributes:null==t?void 0:t.attributes});return yield c.write(e),a(1,0),yield c.close(),(null==t?void 0:t.attachments)&&o&&(yield Promise.all(t.attachments.map(((e,n)=>Qn(this,void 0,void 0,(function*(){return this._sendFile(o[n],e,{topic:t.topic,mimeType:e.type,onProgress:e=>{a(e,n+1)}})})))))),c.info}))}streamText(t){return Qn(this,void 0,void 0,(function*(){var n,i;const s=null!==(n=null==t?void 0:t.streamId)&&void 0!==n?n:crypto.randomUUID(),o={id:s,mimeType:"text/plain",timestamp:Date.now(),topic:null!==(i=null==t?void 0:t.topic)&&void 0!==i?i:"",size:null==t?void 0:t.totalSize,attributes:null==t?void 0:t.attributes},r=new jt({streamId:s,mimeType:o.mimeType,topic:o.topic,timestamp:Lo(o.timestamp),totalLength:Lo(null==t?void 0:t.totalSize),attributes:o.attributes,contentHeader:{case:"textHeader",value:new Lt({version:null==t?void 0:t.version,attachedStreamIds:null==t?void 0:t.attachedStreamIds,replyToStreamId:null==t?void 0:t.replyToStreamId,operationType:"update"===(null==t?void 0:t.type)?At.UPDATE:At.CREATE})}}),a=null==t?void 0:t.destinationIdentities,c=new pt({destinationIdentities:a,value:{case:"streamHeader",value:r}});yield this.engine.sendDataPacket(c,mt.RELIABLE);let d=0;const l=this,u=new WritableStream({write(e){return Qn(this,void 0,void 0,(function*(){for(const t of function(e,t){const n=[];let i=(new TextEncoder).encode(e);for(;i.length>t;){let e=t;for(;e>0;){const t=i[e];if(void 0!==t&&128!=(192&t))break;e--}n.push(i.slice(0,e)),i=i.slice(e)}return i.length>0&&n.push(i),n}(e,xa)){yield l.engine.waitForBufferStatusLow(mt.RELIABLE);const e=new Ft({content:t,streamId:s,chunkIndex:Lo(d)}),n=new pt({destinationIdentities:a,value:{case:"streamChunk",value:e}});yield l.engine.sendDataPacket(n,mt.RELIABLE),d+=1}}))},close(){return Qn(this,void 0,void 0,(function*(){const e=new Bt({streamId:s}),t=new pt({destinationIdentities:a,value:{case:"streamTrailer",value:e}});yield l.engine.sendDataPacket(t,mt.RELIABLE)}))},abort(e){console.log("Sink error:",e)}});let h=()=>Qn(this,void 0,void 0,(function*(){yield p.close()}));l.engine.once(e.EngineEvent.Closing,h);const p=new Ta(u,o,(()=>this.engine.off(e.EngineEvent.Closing,h)));return p}))}sendFile(e,t){return Qn(this,void 0,void 0,(function*(){const n=crypto.randomUUID();return yield this._sendFile(n,e,t),{id:n}}))}_sendFile(e,t,n){return Qn(this,void 0,void 0,(function*(){var i,s;const o=t.size,r=new jt({totalLength:Lo(o),mimeType:null!==(i=null==n?void 0:n.mimeType)&&void 0!==i?i:t.type,streamId:e,topic:null==n?void 0:n.topic,encryptionType:null==n?void 0:n.encryptionType,timestamp:Lo(Date.now()),contentHeader:{case:"byteHeader",value:new Ut({name:t.name})}}),a=null==n?void 0:n.destinationIdentities,c=new pt({destinationIdentities:a,value:{case:"streamHeader",value:r}});function d(e){return new Promise((t=>{const n=new FileReader;n.onload=()=>{t(new Uint8Array(n.result))},n.readAsArrayBuffer(e)}))}yield this.engine.sendDataPacket(c,mt.RELIABLE);const l=Math.ceil(o/xa);for(let i=0;i<l;i++){const r=yield d(t.slice(i*xa,Math.min((i+1)*xa,o)));yield this.engine.waitForBufferStatusLow(mt.RELIABLE);const c=new Ft({content:r,streamId:e,chunkIndex:Lo(i)}),u=new pt({destinationIdentities:a,value:{case:"streamChunk",value:c}});yield this.engine.sendDataPacket(u,mt.RELIABLE),null===(s=null==n?void 0:n.onProgress)||void 0===s||s.call(n,(i+1)/l)}const u=new Bt({streamId:e}),h=new pt({destinationIdentities:a,value:{case:"streamTrailer",value:u}});yield this.engine.sendDataPacket(h,mt.RELIABLE)}))}performRpc(e){return Qn(this,arguments,void 0,(function(e){var t=this;let{destinationIdentity:n,method:i,payload:s,responseTimeout:o=1e4}=e;return function*(){return new Promise(((e,r)=>Qn(t,void 0,void 0,(function*(){var t,a,c,d;if(Vr(s)>15360)return void r(Br.builtIn("REQUEST_PAYLOAD_TOO_LARGE"));if((null===(a=null===(t=this.engine.latestJoinResponse)||void 0===t?void 0:t.serverInfo)||void 0===a?void 0:a.version)&&yo(null===(d=null===(c=this.engine.latestJoinResponse)||void 0===c?void 0:c.serverInfo)||void 0===d?void 0:d.version,"1.8.0")<0)return void r(Br.builtIn("UNSUPPORTED_SERVER"));const l=crypto.randomUUID();yield this.publishRpcRequest(n,l,i,s,o-2e3);const u=setTimeout((()=>{this.pendingAcks.delete(l),r(Br.builtIn("CONNECTION_TIMEOUT")),this.pendingResponses.delete(l),clearTimeout(h)}),2e3);this.pendingAcks.set(l,{resolve:()=>{clearTimeout(u)},participantIdentity:n});const h=setTimeout((()=>{this.pendingResponses.delete(l),r(Br.builtIn("RESPONSE_TIMEOUT"))}),o);this.pendingResponses.set(l,{resolve:(t,n)=>{clearTimeout(h),this.pendingAcks.has(l)&&(console.warn("RPC response received before ack",l),this.pendingAcks.delete(l),clearTimeout(u)),n?r(n):e(null!=t?t:"")},participantIdentity:n})}))))}()}))}registerRpcMethod(e,t){this.rpcHandlers.has(e)&&this.log.warn("you're overriding the RPC handler for method ".concat(e,", in the future this will throw an error")),this.rpcHandlers.set(e,t)}unregisterRpcMethod(e){this.rpcHandlers.delete(e)}setTrackSubscriptionPermissions(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];this.participantTrackPermissions=t,this.allParticipantsAllowedToSubscribe=e,this.engine.client.isDisconnected||this.updateTrackSubscriptionPermissions()}handleIncomingRpcAck(e){const t=this.pendingAcks.get(e);t?(t.resolve(),this.pendingAcks.delete(e)):console.error("Ack received for unexpected RPC request",e)}handleIncomingRpcResponse(e,t,n){const i=this.pendingResponses.get(e);i?(i.resolve(t,n),this.pendingResponses.delete(e)):console.error("Response received for unexpected RPC request",e)}publishRpcRequest(e,t,n,i,s){return Qn(this,void 0,void 0,(function*(){const o=new pt({destinationIdentities:[e],kind:mt.RELIABLE,value:{case:"rpcRequest",value:new Ct({id:t,method:n,payload:i,responseTimeoutMs:s,version:1})}});yield this.engine.sendDataPacket(o,mt.RELIABLE)}))}handleParticipantDisconnected(e){for(const[t,{participantIdentity:n}]of this.pendingAcks)n===e&&this.pendingAcks.delete(t);for(const[t,{participantIdentity:n,resolve:i}]of this.pendingResponses)n===e&&(i(null,Br.builtIn("RECIPIENT_DISCONNECTED")),this.pendingResponses.delete(t))}setEnabledPublishCodecs(e){this.enabledPublishVideoCodecs=e.filter((e=>"video"===e.mime.split("/")[0].toLowerCase()))}updateInfo(e){return e.sid===this.sid&&(!!super.updateInfo(e)&&(e.tracks.forEach((e=>{var t,n;const i=this.trackPublications.get(e.sid);if(i){const s=i.isMuted||null!==(n=null===(t=i.track)||void 0===t?void 0:t.isUpstreamPaused)&&void 0!==n&&n;s!==e.muted&&(this.log.debug("updating server mute state after reconcile",Object.assign(Object.assign(Object.assign({},this.logContext),tr(i)),{mutedOnServer:s})),this.engine.client.sendMuteTrack(e.sid,s))}})),!0))}getPublicationForTrack(e){let t;return this.trackPublications.forEach((n=>{const i=n.track;i&&(e instanceof MediaStreamTrack?(Vo(i)||Bo(i))&&i.mediaStreamTrack===e&&(t=n):e===i&&(t=n))})),t}waitForPendingPublicationOfSource(e){return Qn(this,void 0,void 0,(function*(){const t=Date.now();for(;Date.now()<t+1e4;){const t=Array.from(this.pendingPublishPromises.entries()).find((t=>{let[n]=t;return n.source===e}));if(t)return t[1];yield to(20)}}))}}class Na extends Ia{constructor(t,n,i,s){super(t,n.sid,n.name,s),this.track=void 0,this.allowed=!0,this.disabled=!1,this.currentVideoQuality=e.VideoQuality.HIGH,this.handleEnded=t=>{this.setTrack(void 0),this.emit(e.TrackEvent.Ended,t)},this.handleVisibilityChange=e=>{this.log.debug("adaptivestream video visibility ".concat(this.trackSid,", visible=").concat(e),this.logContext),this.disabled=!e,this.emitTrackUpdate()},this.handleVideoDimensionsChange=e=>{this.log.debug("adaptivestream video dimensions ".concat(e.width,"x").concat(e.height),this.logContext),this.videoDimensions=e,this.emitTrackUpdate()},this.subscribed=i,this.updateInfo(n)}setSubscribed(t){const n=this.subscriptionStatus,i=this.permissionStatus;this.subscribed=t,t&&(this.allowed=!0);const s=new nn({trackSids:[this.trackSid],subscribe:this.subscribed,participantTracks:[new Pt({participantSid:"",trackSids:[this.trackSid]})]});this.emit(e.TrackEvent.UpdateSubscription,s),this.emitSubscriptionUpdateIfChanged(n),this.emitPermissionUpdateIfChanged(i)}get subscriptionStatus(){return!1===this.subscribed?Ia.SubscriptionStatus.Unsubscribed:super.isSubscribed?Ia.SubscriptionStatus.Subscribed:Ia.SubscriptionStatus.Desired}get permissionStatus(){return this.allowed?Ia.PermissionStatus.Allowed:Ia.PermissionStatus.NotAllowed}get isSubscribed(){return!1!==this.subscribed&&super.isSubscribed}get isDesired(){return!1!==this.subscribed}get isEnabled(){return!this.disabled}get isLocal(){return!1}setEnabled(e){this.isManualOperationAllowed()&&this.disabled!==!e&&(this.disabled=!e,this.emitTrackUpdate())}setVideoQuality(e){this.isManualOperationAllowed()&&this.currentVideoQuality!==e&&(this.currentVideoQuality=e,this.videoDimensions=void 0,this.emitTrackUpdate())}setVideoDimensions(e){var t,n;this.isManualOperationAllowed()&&((null===(t=this.videoDimensions)||void 0===t?void 0:t.width)===e.width&&(null===(n=this.videoDimensions)||void 0===n?void 0:n.height)===e.height||(Wo(this.track)&&(this.videoDimensions=e),this.currentVideoQuality=void 0,this.emitTrackUpdate()))}setVideoFPS(e){this.isManualOperationAllowed()&&Wo(this.track)&&this.fps!==e&&(this.fps=e,this.emitTrackUpdate())}get videoQuality(){return this.currentVideoQuality}setTrack(t){const n=this.subscriptionStatus,i=this.permissionStatus,s=this.track;s!==t&&(s&&(s.off(e.TrackEvent.VideoDimensionsChanged,this.handleVideoDimensionsChange),s.off(e.TrackEvent.VisibilityChanged,this.handleVisibilityChange),s.off(e.TrackEvent.Ended,this.handleEnded),s.detach(),s.stopMonitor(),this.emit(e.TrackEvent.Unsubscribed,s)),super.setTrack(t),t&&(t.sid=this.trackSid,t.on(e.TrackEvent.VideoDimensionsChanged,this.handleVideoDimensionsChange),t.on(e.TrackEvent.VisibilityChanged,this.handleVisibilityChange),t.on(e.TrackEvent.Ended,this.handleEnded),this.emit(e.TrackEvent.Subscribed,t)),this.emitPermissionUpdateIfChanged(i),this.emitSubscriptionUpdateIfChanged(n))}setAllowed(e){const t=this.subscriptionStatus,n=this.permissionStatus;this.allowed=e,this.emitPermissionUpdateIfChanged(n),this.emitSubscriptionUpdateIfChanged(t)}setSubscriptionError(t){this.emit(e.TrackEvent.SubscriptionFailed,t)}updateInfo(t){super.updateInfo(t);const n=this.metadataMuted;this.metadataMuted=t.muted,this.track?this.track.setMuted(t.muted):n!==t.muted&&this.emit(t.muted?e.TrackEvent.Muted:e.TrackEvent.Unmuted)}emitSubscriptionUpdateIfChanged(t){const n=this.subscriptionStatus;t!==n&&this.emit(e.TrackEvent.SubscriptionStatusChanged,n,t)}emitPermissionUpdateIfChanged(t){this.permissionStatus!==t&&this.emit(e.TrackEvent.SubscriptionPermissionChanged,this.permissionStatus,t)}isManualOperationAllowed(){return this.kind===Ks.Kind.Video&&this.isAdaptiveStream?(this.log.warn("adaptive stream is enabled, cannot change video track settings",this.logContext),!1):!!this.isDesired||(this.log.warn("cannot update track settings when not subscribed",this.logContext),!1)}get isAdaptiveStream(){return Wo(this.track)&&this.track.isAdaptiveStream}emitTrackUpdate(){const t=new sn({trackSids:[this.trackSid],disabled:this.disabled,fps:this.fps});this.videoDimensions?(t.width=Math.ceil(this.videoDimensions.width),t.height=Math.ceil(this.videoDimensions.height)):void 0!==this.currentVideoQuality?t.quality=this.currentVideoQuality:t.quality=e.VideoQuality.HIGH,this.emit(e.TrackEvent.UpdateSettings,t)}}class _a extends Da{static fromParticipantInfo(e,t,n){return new _a(e,t.sid,t.identity,t.name,t.metadata,t.attributes,n,t.kind)}get logContext(){return Object.assign(Object.assign({},super.logContext),{rpID:this.sid,remoteParticipant:this.identity})}constructor(e,t,n,i,s,o,r){super(t,n||"",i,s,o,r,arguments.length>7&&void 0!==arguments[7]?arguments[7]:ct.STANDARD),this.signalClient=e,this.trackPublications=new Map,this.audioTrackPublications=new Map,this.videoTrackPublications=new Map,this.volumeMap=new Map}addTrackPublication(t){super.addTrackPublication(t),t.on(e.TrackEvent.UpdateSettings,(e=>{this.log.debug("send update settings",Object.assign(Object.assign({},this.logContext),tr(t))),this.signalClient.sendUpdateTrackSettings(e)})),t.on(e.TrackEvent.UpdateSubscription,(e=>{e.participantTracks.forEach((e=>{e.participantSid=this.sid})),this.signalClient.sendUpdateSubscription(e)})),t.on(e.TrackEvent.SubscriptionPermissionChanged,(n=>{this.emit(e.ParticipantEvent.TrackSubscriptionPermissionChanged,t,n)})),t.on(e.TrackEvent.SubscriptionStatusChanged,(n=>{this.emit(e.ParticipantEvent.TrackSubscriptionStatusChanged,t,n)})),t.on(e.TrackEvent.Subscribed,(n=>{this.emit(e.ParticipantEvent.TrackSubscribed,n,t)})),t.on(e.TrackEvent.Unsubscribed,(n=>{this.emit(e.ParticipantEvent.TrackUnsubscribed,n,t)})),t.on(e.TrackEvent.SubscriptionFailed,(n=>{this.emit(e.ParticipantEvent.TrackSubscriptionFailed,t.trackSid,n)}))}getTrackPublication(e){const t=super.getTrackPublication(e);if(t)return t}getTrackPublicationByName(e){const t=super.getTrackPublicationByName(e);if(t)return t}setVolume(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Ks.Source.Microphone;this.volumeMap.set(t,e);const n=this.getTrackPublication(t);n&&n.track&&n.track.setVolume(e)}getVolume(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ks.Source.Microphone;const t=this.getTrackPublication(e);return t&&t.track?t.track.getVolume():this.volumeMap.get(e)}addSubscribedMediaTrack(t,n,i,s,o,r){let a=this.getTrackPublicationBySid(n);if(a||n.startsWith("TR")||this.trackPublications.forEach((e=>{a||t.kind!==e.kind.toString()||(a=e)})),!a)return 0===r?(this.log.error("could not find published track",Object.assign(Object.assign({},this.logContext),{trackSid:n})),void this.emit(e.ParticipantEvent.TrackSubscriptionFailed,n)):(void 0===r&&(r=20),void setTimeout((()=>{this.addSubscribedMediaTrack(t,n,i,s,o,r-1)}),150));if("ended"===t.readyState)return this.log.error("unable to subscribe because MediaStreamTrack is ended. Do not call MediaStreamTrack.stop()",Object.assign(Object.assign({},this.logContext),tr(a))),void this.emit(e.ParticipantEvent.TrackSubscriptionFailed,n);let c;return c="video"===t.kind?new Ea(t,n,s,o):new Sa(t,n,s,this.audioContext,this.audioOutput),c.source=a.source,c.isMuted=a.isMuted,c.setMediaStream(i),c.start(),a.setTrack(c),this.volumeMap.has(a.source)&&qo(c)&&jo(c)&&c.setVolume(this.volumeMap.get(a.source)),a}get hasMetadata(){return!!this.participantInfo}getTrackPublicationBySid(e){return this.trackPublications.get(e)}updateInfo(t){if(!super.updateInfo(t))return!1;const n=new Map,i=new Map;return t.tracks.forEach((e=>{var t,s;let o=this.getTrackPublicationBySid(e.sid);if(o)o.updateInfo(e);else{const n=Ks.kindFromProto(e.type);if(!n)return;o=new Na(n,e,null===(t=this.signalClient.connectOptions)||void 0===t?void 0:t.autoSubscribe,{loggerContextCb:()=>this.logContext,loggerName:null===(s=this.loggerOptions)||void 0===s?void 0:s.loggerName}),o.updateInfo(e),i.set(e.sid,o);const r=Array.from(this.trackPublications.values()).find((e=>e.source===(null==o?void 0:o.source)));r&&o.source!==Ks.Source.Unknown&&this.log.debug("received a second track publication for ".concat(this.identity," with the same source: ").concat(o.source),Object.assign(Object.assign({},this.logContext),{oldTrack:tr(r),newTrack:tr(o)})),this.addTrackPublication(o)}n.set(e.sid,o)})),this.trackPublications.forEach((e=>{n.has(e.trackSid)||(this.log.trace("detected removed track on remote participant, unpublishing",Object.assign(Object.assign({},this.logContext),tr(e))),this.unpublishTrack(e.trackSid,!0))})),i.forEach((t=>{this.emit(e.ParticipantEvent.TrackPublished,t)})),!0}unpublishTrack(t,n){const i=this.trackPublications.get(t);if(!i)return;const{track:s}=i;switch(s&&(s.stop(),i.setTrack(void 0)),this.trackPublications.delete(t),i.kind){case Ks.Kind.Audio:this.audioTrackPublications.delete(t);break;case Ks.Kind.Video:this.videoTrackPublications.delete(t)}n&&this.emit(e.ParticipantEvent.TrackUnpublished,i)}setAudioOutput(e){return Qn(this,void 0,void 0,(function*(){this.audioOutput=e;const t=[];this.audioTrackPublications.forEach((n=>{var i;jo(n.track)&&qo(n.track)&&t.push(n.track.setSinkId(null!==(i=e.deviceId)&&void 0!==i?i:"default"))})),yield Promise.all(t)}))}emit(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];return this.log.trace("participant event",Object.assign(Object.assign({},this.logContext),{event:e,args:n})),super.emit(e,...n)}}var Aa;e.ConnectionState=void 0,(Aa=e.ConnectionState||(e.ConnectionState={})).Disconnected="disconnected",Aa.Connecting="connecting",Aa.Connected="connected",Aa.Reconnecting="reconnecting",Aa.SignalReconnecting="signalReconnecting";class La extends ei.EventEmitter{constructor(t){var n,i,o,r;if(super(),n=this,this.state=e.ConnectionState.Disconnected,this.activeSpeakers=[],this.isE2EEEnabled=!1,this.audioEnabled=!0,this.isVideoPlaybackBlocked=!1,this.log=qn,this.bufferedEvents=[],this.isResuming=!1,this.byteStreamControllers=new Map,this.textStreamControllers=new Map,this.byteStreamHandlers=new Map,this.textStreamHandlers=new Map,this.rpcHandlers=new Map,this.connect=(t,n,i)=>Qn(this,void 0,void 0,(function*(){var s;if(!co())throw go()?Error("WebRTC isn't detected, have you called registerGlobals?"):Error("LiveKit doesn't seem to be supported on this browser. Try to update your browser and make sure no browser extensions are disabling webRTC.");const o=yield this.disconnectLock.lock();if(this.state===e.ConnectionState.Connected)return this.log.info("already connected to room ".concat(this.name),this.logContext),o(),Promise.resolve();if(this.connectFuture)return o(),this.connectFuture.promise;this.setAndEmitConnectionState(e.ConnectionState.Connecting),(null===(s=this.regionUrlProvider)||void 0===s?void 0:s.getServerUrl().toString())!==t&&(this.regionUrl=void 0,this.regionUrlProvider=void 0),vo(new URL(t))&&(void 0===this.regionUrlProvider?this.regionUrlProvider=new va(t,n):this.regionUrlProvider.updateToken(n),this.regionUrlProvider.fetchRegionSettings().then((e=>{var t;null===(t=this.regionUrlProvider)||void 0===t||t.setServerReportedRegions(e)})).catch((e=>{this.log.warn("could not fetch region settings",Object.assign(Object.assign({},this.logContext),{error:e}))})));const r=(s,a,c)=>Qn(this,void 0,void 0,(function*(){var d,l;this.abortController&&this.abortController.abort();const u=new AbortController;this.abortController=u,null==o||o();try{yield this.attemptConnection(null!=c?c:t,n,i,u),this.abortController=void 0,s()}catch(t){if(this.regionUrlProvider&&t instanceof Ps&&t.reason!==e.ConnectionErrorReason.Cancelled&&t.reason!==e.ConnectionErrorReason.NotAllowed){let n=null;try{n=yield this.regionUrlProvider.getNextBestRegionUrl(null===(d=this.abortController)||void 0===d?void 0:d.signal)}catch(t){if(t instanceof Ps&&(401===t.status||t.reason===e.ConnectionErrorReason.Cancelled))return this.handleDisconnect(this.options.stopLocalTrackOnUnpublish),void a(t)}n&&!(null===(l=this.abortController)||void 0===l?void 0:l.signal.aborted)?(this.log.info("Initial connection failed with ConnectionError: ".concat(t.message,". Retrying with another region: ").concat(n),this.logContext),this.recreateEngine(),yield r(s,a,n)):(this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,_o(t)),a(t))}else{let e=$e.UNKNOWN_REASON;t instanceof Ps&&(e=_o(t)),this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,e),a(t)}}})),a=this.regionUrl;return this.regionUrl=void 0,this.connectFuture=new xo(((e,t)=>{r(e,t,a)}),(()=>{this.clearConnectionFutures()})),this.connectFuture.promise})),this.connectSignal=(e,t,n,i,s,o)=>Qn(this,void 0,void 0,(function*(){var r,a,c;const d=yield n.join(e,t,{autoSubscribe:i.autoSubscribe,adaptiveStream:"object"==typeof s.adaptiveStream||s.adaptiveStream,maxRetries:i.maxRetries,e2eeEnabled:!!this.e2eeManager,websocketTimeout:i.websocketTimeout},o.signal);let l=d.serverInfo;if(l||(l={version:d.serverVersion,region:d.serverRegion}),this.serverInfo=l,this.log.debug("connected to Livekit Server ".concat(Object.entries(l).map((e=>{let[t,n]=e;return"".concat(t,": ").concat(n)})).join(", ")),{room:null===(r=d.room)||void 0===r?void 0:r.name,roomSid:null===(a=d.room)||void 0===a?void 0:a.sid,identity:null===(c=d.participant)||void 0===c?void 0:c.identity}),!l.version)throw new Os("unknown server version");return"0.15.1"===l.version&&this.options.dynacast&&(this.log.debug("disabling dynacast due to server version",this.logContext),s.dynacast=!1),d})),this.applyJoinResponse=e=>{const t=e.participant;if(this.localParticipant.sid=t.sid,this.localParticipant.identity=t.identity,this.localParticipant.setEnabledPublishCodecs(e.enabledPublishCodecs),this.options.e2ee&&this.e2eeManager)try{this.e2eeManager.setSifTrailer(e.sifTrailer)}catch(e){this.log.error(e instanceof Error?e.message:"Could not set SifTrailer",Object.assign(Object.assign({},this.logContext),{error:e}))}this.handleParticipantUpdates([t,...e.otherParticipants]),e.room&&this.handleRoomUpdate(e.room)},this.attemptConnection=(t,n,i,s)=>Qn(this,void 0,void 0,(function*(){var o,r;this.state===e.ConnectionState.Reconnecting||this.isResuming||(null===(o=this.engine)||void 0===o?void 0:o.pendingReconnect)?(this.log.info("Reconnection attempt replaced by new connection attempt",this.logContext),this.recreateEngine()):this.maybeCreateEngine(),(null===(r=this.regionUrlProvider)||void 0===r?void 0:r.isCloud())&&this.engine.setRegionUrlProvider(this.regionUrlProvider),this.acquireAudioContext(),this.connOptions=Object.assign(Object.assign({},Ur),i),this.connOptions.rtcConfig&&(this.engine.rtcConfig=this.connOptions.rtcConfig),this.connOptions.peerConnectionTimeout&&(this.engine.peerConnectionTimeout=this.connOptions.peerConnectionTimeout);try{const i=yield this.connectSignal(t,n,this.engine,this.connOptions,this.options,s);this.applyJoinResponse(i),this.setupLocalParticipantEvents(),this.emit(e.RoomEvent.SignalConnected)}catch(t){yield this.engine.close(),this.recreateEngine();const n=new Ps("could not establish signal connection",e.ConnectionErrorReason.ServerUnreachable);throw t instanceof Error&&(n.message="".concat(n.message,": ").concat(t.message)),t instanceof Ps&&(n.reason=t.reason,n.status=t.status),this.log.debug("error trying to establish signal connection",Object.assign(Object.assign({},this.logContext),{error:t})),n}if(s.signal.aborted)throw yield this.engine.close(),this.recreateEngine(),new Ps("Connection attempt aborted",e.ConnectionErrorReason.Cancelled);try{yield this.engine.waitForPCInitialConnection(this.connOptions.peerConnectionTimeout,s)}catch(e){throw yield this.engine.close(),this.recreateEngine(),e}mo()&&this.options.disconnectOnPageLeave&&(window.addEventListener("pagehide",this.onPageLeave),window.addEventListener("beforeunload",this.onPageLeave)),mo()&&document.addEventListener("freeze",this.onPageLeave),this.setAndEmitConnectionState(e.ConnectionState.Connected),this.emit(e.RoomEvent.Connected),this.registerConnectionReconcile()})),this.disconnect=function(){for(var t=arguments.length,i=new Array(t),s=0;s<t;s++)i[s]=arguments[s];return Qn(n,[...i],void 0,(function(){var t=this;let n=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return function*(){var i,s,o,r;const a=yield t.disconnectLock.lock();try{if(t.state===e.ConnectionState.Disconnected)return void t.log.debug("already disconnected",t.logContext);t.log.info("disconnect from room",Object.assign({},t.logContext)),(t.state===e.ConnectionState.Connecting||t.state===e.ConnectionState.Reconnecting||t.isResuming)&&(t.log.warn("abort connection attempt",t.logContext),null===(i=t.abortController)||void 0===i||i.abort(),null===(o=null===(s=t.connectFuture)||void 0===s?void 0:s.reject)||void 0===o||o.call(s,new Ps("Client initiated disconnect",e.ConnectionErrorReason.Cancelled)),t.connectFuture=void 0),(null===(r=t.engine)||void 0===r?void 0:r.client.isDisconnected)||(yield t.engine.client.sendLeave()),t.engine&&(yield t.engine.close()),t.handleDisconnect(n,$e.CLIENT_INITIATED),t.engine=void 0}finally{a()}}()}))},this.onPageLeave=()=>Qn(this,void 0,void 0,(function*(){this.log.info("Page leave detected, disconnecting",this.logContext),yield this.disconnect()})),this.startAudio=()=>Qn(this,void 0,void 0,(function*(){const t=[],n=Ls();if(n&&"iOS"===n.os){const n="livekit-dummy-audio-el";let i=document.getElementById(n);if(!i){i=document.createElement("audio"),i.id=n,i.autoplay=!0,i.hidden=!0;const t=Do();t.enabled=!0;const s=new MediaStream([t]);i.srcObject=s,document.addEventListener("visibilitychange",(()=>{i&&(i.srcObject=document.hidden?null:s,document.hidden||(this.log.debug("page visible again, triggering startAudio to resume playback and update playback status",this.logContext),this.startAudio()))})),document.body.append(i),this.once(e.RoomEvent.Disconnected,(()=>{null==i||i.remove(),i=null}))}t.push(i)}this.remoteParticipants.forEach((e=>{e.audioTrackPublications.forEach((e=>{e.track&&e.track.attachedElements.forEach((e=>{t.push(e)}))}))}));try{yield Promise.all([this.acquireAudioContext(),...t.map((e=>(e.muted=!1,e.play())))]),this.handleAudioPlaybackStarted()}catch(e){throw this.handleAudioPlaybackFailed(e),e}})),this.startVideo=()=>Qn(this,void 0,void 0,(function*(){const e=[];for(const t of this.remoteParticipants.values())t.videoTrackPublications.forEach((t=>{var n;null===(n=t.track)||void 0===n||n.attachedElements.forEach((t=>{e.includes(t)||e.push(t)}))}));yield Promise.all(e.map((e=>e.play()))).then((()=>{this.handleVideoPlaybackStarted()})).catch((e=>{"NotAllowedError"===e.name?this.handleVideoPlaybackFailed():this.log.warn("Resuming video playback failed, make sure you call `startVideo` directly in a user gesture handler",this.logContext)}))})),this.handleRestarting=()=>{this.clearConnectionReconcile(),this.isResuming=!1;for(const e of this.remoteParticipants.values())this.handleParticipantDisconnected(e.identity,e);this.setAndEmitConnectionState(e.ConnectionState.Reconnecting)&&this.emit(e.RoomEvent.Reconnecting)},this.handleSignalRestarted=t=>Qn(this,void 0,void 0,(function*(){this.log.debug("signal reconnected to server, region ".concat(t.serverRegion),Object.assign(Object.assign({},this.logContext),{region:t.serverRegion})),this.bufferedEvents=[],this.applyJoinResponse(t);try{yield this.localParticipant.republishAllTracks(void 0,!0)}catch(e){this.log.error("error trying to re-publish tracks after reconnection",Object.assign(Object.assign({},this.logContext),{error:e}))}try{yield this.engine.waitForRestarted(),this.log.debug("fully reconnected to server",Object.assign(Object.assign({},this.logContext),{region:t.serverRegion}))}catch(e){return}this.setAndEmitConnectionState(e.ConnectionState.Connected),this.emit(e.RoomEvent.Reconnected),this.registerConnectionReconcile(),this.emitBufferedEvents()})),this.handleParticipantUpdates=e=>{e.forEach((e=>{var t;if(e.identity===this.localParticipant.identity)return void this.localParticipant.updateInfo(e);""===e.identity&&(e.identity=null!==(t=this.sidToIdentity.get(e.sid))&&void 0!==t?t:"");let n=this.remoteParticipants.get(e.identity);e.state===at.DISCONNECTED?this.handleParticipantDisconnected(e.identity,n):n=this.getOrCreateParticipant(e.identity,e)}))},this.handleActiveSpeakersUpdate=t=>{const n=[],i={};t.forEach((e=>{if(i[e.sid]=!0,e.sid===this.localParticipant.sid)this.localParticipant.audioLevel=e.level,this.localParticipant.setIsSpeaking(!0),n.push(this.localParticipant);else{const t=this.getRemoteParticipantBySid(e.sid);t&&(t.audioLevel=e.level,t.setIsSpeaking(!0),n.push(t))}})),i[this.localParticipant.sid]||(this.localParticipant.audioLevel=0,this.localParticipant.setIsSpeaking(!1)),this.remoteParticipants.forEach((e=>{i[e.sid]||(e.audioLevel=0,e.setIsSpeaking(!1))})),this.activeSpeakers=n,this.emitWhenConnected(e.RoomEvent.ActiveSpeakersChanged,n)},this.handleSpeakersChanged=t=>{const n=new Map;this.activeSpeakers.forEach((e=>{const t=this.remoteParticipants.get(e.identity);t&&t.sid!==e.sid||n.set(e.sid,e)})),t.forEach((e=>{let t=this.getRemoteParticipantBySid(e.sid);e.sid===this.localParticipant.sid&&(t=this.localParticipant),t&&(t.audioLevel=e.level,t.setIsSpeaking(e.active),e.active?n.set(e.sid,t):n.delete(e.sid))}));const i=Array.from(n.values());i.sort(((e,t)=>t.audioLevel-e.audioLevel)),this.activeSpeakers=i,this.emitWhenConnected(e.RoomEvent.ActiveSpeakersChanged,i)},this.handleStreamStateUpdate=t=>{t.streamStates.forEach((t=>{const n=this.getRemoteParticipantBySid(t.participantSid);if(!n)return;const i=n.getTrackPublicationBySid(t.trackSid);if(!i||!i.track)return;const s=Ks.streamStateFromProto(t.state);s!==i.track.streamState&&(i.track.streamState=s,n.emit(e.ParticipantEvent.TrackStreamStateChanged,i,i.track.streamState),this.emitWhenConnected(e.RoomEvent.TrackStreamStateChanged,i,i.track.streamState,n))}))},this.handleSubscriptionPermissionUpdate=e=>{const t=this.getRemoteParticipantBySid(e.participantSid);if(!t)return;const n=t.getTrackPublicationBySid(e.trackSid);n&&n.setAllowed(e.allowed)},this.handleSubscriptionError=e=>{const t=Array.from(this.remoteParticipants.values()).find((t=>t.trackPublications.has(e.trackSid)));if(!t)return;const n=t.getTrackPublicationBySid(e.trackSid);n&&n.setSubscriptionError(e.err)},this.handleDataPacket=e=>{const t=this.remoteParticipants.get(e.participantIdentity);if("user"===e.value.case)this.handleUserPacket(t,e.value.value,e.kind);else if("transcription"===e.value.case)this.handleTranscription(t,e.value.value);else if("sipDtmf"===e.value.case)this.handleSipDtmf(t,e.value.value);else if("chatMessage"===e.value.case)this.handleChatMessage(t,e.value.value);else if("metrics"===e.value.case)this.handleMetrics(e.value.value,t);else if("streamHeader"===e.value.case)this.handleStreamHeader(e.value.value,e.participantIdentity);else if("streamChunk"===e.value.case)this.handleStreamChunk(e.value.value);else if("streamTrailer"===e.value.case)this.handleStreamTrailer(e.value.value);else if("rpcRequest"===e.value.case){const t=e.value.value;this.handleIncomingRpcRequest(e.participantIdentity,t.id,t.method,t.payload,t.responseTimeoutMs,t.version)}},this.handleUserPacket=(t,n,i)=>{this.emit(e.RoomEvent.DataReceived,n.payload,t,i,n.topic),null==t||t.emit(e.ParticipantEvent.DataReceived,n.payload,i)},this.handleSipDtmf=(t,n)=>{this.emit(e.RoomEvent.SipDTMFReceived,n,t),null==t||t.emit(e.ParticipantEvent.SipDTMFReceived,n)},this.bufferedSegments=new Map,this.handleTranscription=(t,n)=>{const i=n.transcribedParticipantIdentity===this.localParticipant.identity?this.localParticipant:this.getParticipantByIdentity(n.transcribedParticipantIdentity),s=null==i?void 0:i.trackPublications.get(n.trackId),o=function(e,t){return e.segments.map((e=>{let{id:n,text:i,language:s,startTime:o,endTime:r,final:a}=e;var c;const d=null!==(c=t.get(n))&&void 0!==c?c:Date.now(),l=Date.now();return a?t.delete(n):t.set(n,d),{id:n,text:i,startTime:Number.parseInt(o.toString()),endTime:Number.parseInt(r.toString()),final:a,language:s,firstReceivedTime:d,lastReceivedTime:l}}))}(n,this.transcriptionReceivedTimes);null==s||s.emit(e.TrackEvent.TranscriptionReceived,o),null==i||i.emit(e.ParticipantEvent.TranscriptionReceived,o,s),this.emit(e.RoomEvent.TranscriptionReceived,o,i,s)},this.handleChatMessage=(t,n)=>{const i=function(e){const{id:t,timestamp:n,message:i,editTimestamp:s}=e;return{id:t,timestamp:Number.parseInt(n.toString()),editTimestamp:s?Number.parseInt(s.toString()):void 0,message:i}}(n);this.emit(e.RoomEvent.ChatMessage,i,t)},this.handleMetrics=(t,n)=>{this.emit(e.RoomEvent.MetricsReceived,t,n)},this.handleAudioPlaybackStarted=()=>{this.canPlaybackAudio||(this.audioEnabled=!0,this.emit(e.RoomEvent.AudioPlaybackStatusChanged,!0))},this.handleAudioPlaybackFailed=t=>{this.log.warn("could not playback audio",Object.assign(Object.assign({},this.logContext),{error:t})),this.canPlaybackAudio&&(this.audioEnabled=!1,this.emit(e.RoomEvent.AudioPlaybackStatusChanged,!1))},this.handleVideoPlaybackStarted=()=>{this.isVideoPlaybackBlocked&&(this.isVideoPlaybackBlocked=!1,this.emit(e.RoomEvent.VideoPlaybackStatusChanged,!0))},this.handleVideoPlaybackFailed=()=>{this.isVideoPlaybackBlocked||(this.isVideoPlaybackBlocked=!0,this.emit(e.RoomEvent.VideoPlaybackStatusChanged,!1))},this.handleDeviceChange=()=>Qn(this,void 0,void 0,(function*(){var t,n;const i=or.getInstance().previousDevices,s=yield or.getInstance().getDevices(void 0,!1),o=Ls();if("Chrome"===(null==o?void 0:o.name)&&"iOS"!==o.os)for(let t of s){const n=i.find((e=>e.deviceId===t.deviceId));n&&""!==n.label&&n.kind===t.kind&&n.label!==t.label&&"default"===this.getActiveDevice(t.kind)&&this.emit(e.RoomEvent.ActiveDeviceChanged,t.kind,t.deviceId)}const r=["audiooutput","audioinput","videoinput"];for(let e of r){const o=s.filter((t=>t.kind===e)),r=this.getActiveDevice(e);r===(null===(t=i.filter((t=>t.kind===e))[0])||void 0===t?void 0:t.deviceId)&&o.length>0&&(null===(n=o[0])||void 0===n?void 0:n.deviceId)!==r?yield this.switchActiveDevice(e,o[0].deviceId):"audioinput"===e&&!uo()||"videoinput"===e||o.length>0&&!o.find((t=>t.deviceId===this.getActiveDevice(e)))&&(yield this.switchActiveDevice(e,o[0].deviceId))}this.emit(e.RoomEvent.MediaDevicesChanged)})),this.handleRoomUpdate=t=>{const n=this.roomInfo;this.roomInfo=t,n&&n.metadata!==t.metadata&&this.emitWhenConnected(e.RoomEvent.RoomMetadataChanged,t.metadata),(null==n?void 0:n.activeRecording)!==t.activeRecording&&this.emitWhenConnected(e.RoomEvent.RecordingStatusChanged,t.activeRecording)},this.handleConnectionQualityUpdate=e=>{e.updates.forEach((e=>{if(e.participantSid===this.localParticipant.sid)return void this.localParticipant.setConnectionQuality(e.quality);const t=this.getRemoteParticipantBySid(e.participantSid);t&&t.setConnectionQuality(e.quality)}))},this.onLocalParticipantMetadataChanged=t=>{this.emit(e.RoomEvent.ParticipantMetadataChanged,t,this.localParticipant)},this.onLocalParticipantNameChanged=t=>{this.emit(e.RoomEvent.ParticipantNameChanged,t,this.localParticipant)},this.onLocalAttributesChanged=t=>{this.emit(e.RoomEvent.ParticipantAttributesChanged,t,this.localParticipant)},this.onLocalTrackMuted=t=>{this.emit(e.RoomEvent.TrackMuted,t,this.localParticipant)},this.onLocalTrackUnmuted=t=>{this.emit(e.RoomEvent.TrackUnmuted,t,this.localParticipant)},this.onTrackProcessorUpdate=e=>{var t;null===(t=null==e?void 0:e.onPublish)||void 0===t||t.call(e,this)},this.onLocalTrackPublished=t=>Qn(this,void 0,void 0,(function*(){var n,i,s,o,r,a;if(null===(n=t.track)||void 0===n||n.on(e.TrackEvent.TrackProcessorUpdate,this.onTrackProcessorUpdate),null===(i=t.track)||void 0===i||i.on(e.TrackEvent.Restarted,this.onLocalTrackRestarted),null===(r=null===(o=null===(s=t.track)||void 0===s?void 0:s.getProcessor())||void 0===o?void 0:o.onPublish)||void 0===r||r.call(o,this),this.emit(e.RoomEvent.LocalTrackPublished,t,this.localParticipant),Vo(t.track)){(yield t.track.checkForSilence())&&this.emit(e.RoomEvent.LocalAudioSilenceDetected,t)}const c=yield null===(a=t.track)||void 0===a?void 0:a.getDeviceId(!1),d=Xo(t.source);d&&c&&c!==this.localParticipant.activeDeviceMap.get(d)&&(this.localParticipant.activeDeviceMap.set(d,c),this.emit(e.RoomEvent.ActiveDeviceChanged,d,c))})),this.onLocalTrackUnpublished=t=>{var n,i;null===(n=t.track)||void 0===n||n.off(e.TrackEvent.TrackProcessorUpdate,this.onTrackProcessorUpdate),null===(i=t.track)||void 0===i||i.off(e.TrackEvent.Restarted,this.onLocalTrackRestarted),this.emit(e.RoomEvent.LocalTrackUnpublished,t,this.localParticipant)},this.onLocalTrackRestarted=t=>Qn(this,void 0,void 0,(function*(){const n=yield t.getDeviceId(!1),i=Xo(t.source);i&&n&&n!==this.localParticipant.activeDeviceMap.get(i)&&(this.log.debug("local track restarted, setting ".concat(i," ").concat(n," active"),this.logContext),this.localParticipant.activeDeviceMap.set(i,n),this.emit(e.RoomEvent.ActiveDeviceChanged,i,n))})),this.onLocalConnectionQualityChanged=t=>{this.emit(e.RoomEvent.ConnectionQualityChanged,t,this.localParticipant)},this.onMediaDevicesError=t=>{this.emit(e.RoomEvent.MediaDevicesError,t)},this.onLocalParticipantPermissionsChanged=t=>{this.emit(e.RoomEvent.ParticipantPermissionsChanged,t,this.localParticipant)},this.onLocalChatMessageSent=t=>{this.emit(e.RoomEvent.ChatMessage,t,this.localParticipant)},this.setMaxListeners(100),this.remoteParticipants=new Map,this.sidToIdentity=new Map,this.options=Object.assign(Object.assign({},Lr),t),this.log=Wn(null!==(i=this.options.loggerName)&&void 0!==i?i:e.LoggerNames.Room),this.transcriptionReceivedTimes=new Map,this.options.audioCaptureDefaults=Object.assign(Object.assign({},_r),null==t?void 0:t.audioCaptureDefaults),this.options.videoCaptureDefaults=Object.assign(Object.assign({},Ar),null==t?void 0:t.videoCaptureDefaults),this.options.publishDefaults=Object.assign(Object.assign({},Nr),null==t?void 0:t.publishDefaults),this.maybeCreateEngine(),this.disconnectLock=new s,this.localParticipant=new Ma("","",this.engine,this.options,this.rpcHandlers),this.options.videoCaptureDefaults.deviceId&&this.localParticipant.activeDeviceMap.set("videoinput",Mo(this.options.videoCaptureDefaults.deviceId)),this.options.audioCaptureDefaults.deviceId&&this.localParticipant.activeDeviceMap.set("audioinput",Mo(this.options.audioCaptureDefaults.deviceId)),(null===(o=this.options.audioOutput)||void 0===o?void 0:o.deviceId)&&this.switchActiveDevice("audiooutput",Mo(this.options.audioOutput.deviceId)).catch((e=>this.log.warn("Could not set audio output: ".concat(e.message),this.logContext))),this.options.e2ee&&this.setupE2EE(),mo()){const e=new AbortController;null===(r=navigator.mediaDevices)||void 0===r||r.addEventListener("devicechange",this.handleDeviceChange,{signal:e.signal}),La.cleanupRegistry&&La.cleanupRegistry.register(this,(()=>{e.abort()}))}}registerTextStreamHandler(e,t){if(this.textStreamHandlers.has(e))throw new TypeError('A text stream handler for topic "'.concat(e,'" has already been set.'));this.textStreamHandlers.set(e,t)}unregisterTextStreamHandler(e){this.textStreamHandlers.delete(e)}registerByteStreamHandler(e,t){if(this.byteStreamHandlers.has(e))throw new TypeError('A byte stream handler for topic "'.concat(e,'" has already been set.'));this.byteStreamHandlers.set(e,t)}unregisterByteStreamHandler(e){this.byteStreamHandlers.delete(e)}registerRpcMethod(e,t){if(this.rpcHandlers.has(e))throw Error("RPC handler already registered for method ".concat(e,", unregisterRpcMethod before trying to register again"));this.rpcHandlers.set(e,t)}unregisterRpcMethod(e){this.rpcHandlers.delete(e)}handleIncomingRpcRequest(e,t,n,i,s,o){return Qn(this,void 0,void 0,(function*(){if(yield this.engine.publishRpcAck(e,t),1!==o)return void(yield this.engine.publishRpcResponse(e,t,null,Br.builtIn("UNSUPPORTED_VERSION")));const r=this.rpcHandlers.get(n);if(!r)return void(yield this.engine.publishRpcResponse(e,t,null,Br.builtIn("UNSUPPORTED_METHOD")));let a=null,c=null;try{const o=yield r({requestId:t,callerIdentity:e,payload:i,responseTimeout:s});Vr(o)>15360?(a=Br.builtIn("RESPONSE_PAYLOAD_TOO_LARGE"),console.warn("RPC Response payload too large for ".concat(n))):c=o}catch(e){e instanceof Br?a=e:(console.warn("Uncaught error returned by RPC handler for ".concat(n,". Returning APPLICATION_ERROR instead."),e),a=Br.builtIn("APPLICATION_ERROR"))}yield this.engine.publishRpcResponse(e,t,c,a)}))}setE2EEEnabled(e){return Qn(this,void 0,void 0,(function*(){if(!this.e2eeManager)throw Error("e2ee not configured, please set e2ee settings within the room options");yield Promise.all([this.localParticipant.setE2EEEnabled(e)]),""!==this.localParticipant.identity&&this.e2eeManager.setParticipantCryptorEnabled(e,this.localParticipant.identity)}))}setupE2EE(){var t;this.options.e2ee&&("e2eeManager"in this.options.e2ee?this.e2eeManager=this.options.e2ee.e2eeManager:this.e2eeManager=new ir(this.options.e2ee),this.e2eeManager.on(e.EncryptionEvent.ParticipantEncryptionStatusChanged,((t,n)=>{Ho(n)&&(this.isE2EEEnabled=t),this.emit(e.RoomEvent.ParticipantEncryptionStatusChanged,t,n)})),this.e2eeManager.on(e.EncryptionEvent.EncryptionError,(t=>this.emit(e.RoomEvent.EncryptionError,t))),null===(t=this.e2eeManager)||void 0===t||t.setup(this))}get logContext(){var e;return{room:this.name,roomID:null===(e=this.roomInfo)||void 0===e?void 0:e.sid,participant:this.localParticipant.identity,pID:this.localParticipant.sid}}get isRecording(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.activeRecording)&&void 0!==t&&t}getSid(){return Qn(this,void 0,void 0,(function*(){return this.state===e.ConnectionState.Disconnected?"":this.roomInfo&&""!==this.roomInfo.sid?this.roomInfo.sid:new Promise(((t,n)=>{const i=n=>{""!==n.sid&&(this.engine.off(e.EngineEvent.RoomUpdate,i),t(n.sid))};this.engine.on(e.EngineEvent.RoomUpdate,i),this.once(e.RoomEvent.Disconnected,(()=>{this.engine.off(e.EngineEvent.RoomUpdate,i),n("Room disconnected before room server id was available")}))}))}))}get name(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.name)&&void 0!==t?t:""}get metadata(){var e;return null===(e=this.roomInfo)||void 0===e?void 0:e.metadata}get numParticipants(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.numParticipants)&&void 0!==t?t:0}get numPublishers(){var e,t;return null!==(t=null===(e=this.roomInfo)||void 0===e?void 0:e.numPublishers)&&void 0!==t?t:0}maybeCreateEngine(){this.engine&&!this.engine.isClosed||(this.engine=new ma(this.options),this.engine.on(e.EngineEvent.ParticipantUpdate,this.handleParticipantUpdates).on(e.EngineEvent.RoomUpdate,this.handleRoomUpdate).on(e.EngineEvent.SpeakersChanged,this.handleSpeakersChanged).on(e.EngineEvent.StreamStateChanged,this.handleStreamStateUpdate).on(e.EngineEvent.ConnectionQualityUpdate,this.handleConnectionQualityUpdate).on(e.EngineEvent.SubscriptionError,this.handleSubscriptionError).on(e.EngineEvent.SubscriptionPermissionUpdate,this.handleSubscriptionPermissionUpdate).on(e.EngineEvent.MediaTrackAdded,((e,t,n)=>{this.onTrackAdded(e,t,n)})).on(e.EngineEvent.Disconnected,(e=>{this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,e)})).on(e.EngineEvent.ActiveSpeakersUpdate,this.handleActiveSpeakersUpdate).on(e.EngineEvent.DataPacketReceived,this.handleDataPacket).on(e.EngineEvent.Resuming,(()=>{this.clearConnectionReconcile(),this.isResuming=!0,this.log.info("Resuming signal connection",this.logContext),this.setAndEmitConnectionState(e.ConnectionState.SignalReconnecting)&&this.emit(e.RoomEvent.SignalReconnecting)})).on(e.EngineEvent.Resumed,(()=>{this.registerConnectionReconcile(),this.isResuming=!1,this.log.info("Resumed signal connection",this.logContext),this.updateSubscriptions(),this.emitBufferedEvents(),this.setAndEmitConnectionState(e.ConnectionState.Connected)&&this.emit(e.RoomEvent.Reconnected)})).on(e.EngineEvent.SignalResumed,(()=>{this.bufferedEvents=[],(this.state===e.ConnectionState.Reconnecting||this.isResuming)&&this.sendSyncState()})).on(e.EngineEvent.Restarting,this.handleRestarting).on(e.EngineEvent.SignalRestarted,this.handleSignalRestarted).on(e.EngineEvent.Offline,(()=>{this.setAndEmitConnectionState(e.ConnectionState.Reconnecting)&&this.emit(e.RoomEvent.Reconnecting)})).on(e.EngineEvent.DCBufferStatusChanged,((t,n)=>{this.emit(e.RoomEvent.DCBufferStatusChanged,t,n)})).on(e.EngineEvent.LocalTrackSubscribed,(t=>{const n=this.localParticipant.getTrackPublications().find((e=>{let{trackSid:n}=e;return n===t}));n?(this.localParticipant.emit(e.ParticipantEvent.LocalTrackSubscribed,n),this.emitWhenConnected(e.RoomEvent.LocalTrackSubscribed,n,this.localParticipant)):this.log.warn("could not find local track subscription for subscribed event",this.logContext)})),this.localParticipant&&this.localParticipant.setupEngine(this.engine),this.e2eeManager&&this.e2eeManager.setupEngine(this.engine))}static getLocalDevices(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return or.getInstance().getDevices(e,t)}prepareConnection(t,n){return Qn(this,void 0,void 0,(function*(){if(this.state===e.ConnectionState.Disconnected){this.log.debug("prepareConnection to ".concat(t),this.logContext);try{if(vo(new URL(t))&&n){this.regionUrlProvider=new va(t,n);const i=yield this.regionUrlProvider.getNextBestRegionUrl();i&&this.state===e.ConnectionState.Disconnected&&(this.regionUrl=i,yield fetch(No(i),{method:"HEAD"}),this.log.debug("prepared connection to ".concat(i),this.logContext))}else yield fetch(No(t),{method:"HEAD"})}catch(e){this.log.warn("could not prepare connection",Object.assign(Object.assign({},this.logContext),{error:e}))}}}))}getParticipantByIdentity(e){return this.localParticipant.identity===e?this.localParticipant:this.remoteParticipants.get(e)}clearConnectionFutures(){this.connectFuture=void 0}simulateScenario(e,t){return Qn(this,void 0,void 0,(function*(){let n,i=()=>{};switch(e){case"signal-reconnect":yield this.engine.client.handleOnClose("simulate disconnect");break;case"speaker":n=new Pn({scenario:{case:"speakerUpdate",value:3}});break;case"node-failure":n=new Pn({scenario:{case:"nodeFailure",value:!0}});break;case"server-leave":n=new Pn({scenario:{case:"serverLeave",value:!0}});break;case"migration":n=new Pn({scenario:{case:"migration",value:!0}});break;case"resume-reconnect":this.engine.failNext(),yield this.engine.client.handleOnClose("simulate resume-disconnect");break;case"disconnect-signal-on-resume":i=()=>Qn(this,void 0,void 0,(function*(){yield this.engine.client.handleOnClose("simulate resume-disconnect")})),n=new Pn({scenario:{case:"disconnectSignalOnResume",value:!0}});break;case"disconnect-signal-on-resume-no-messages":i=()=>Qn(this,void 0,void 0,(function*(){yield this.engine.client.handleOnClose("simulate resume-disconnect")})),n=new Pn({scenario:{case:"disconnectSignalOnResumeNoMessages",value:!0}});break;case"full-reconnect":this.engine.fullReconnectOnNext=!0,yield this.engine.client.handleOnClose("simulate full-reconnect");break;case"force-tcp":case"force-tls":n=new Pn({scenario:{case:"switchCandidateProtocol",value:"force-tls"===e?2:1}}),i=()=>Qn(this,void 0,void 0,(function*(){const e=this.engine.client.onLeave;e&&e(new an({reason:$e.CLIENT_INITIATED,action:cn.RECONNECT}))}));break;case"subscriber-bandwidth":if(void 0===t||"number"!=typeof t)throw new Error("subscriber-bandwidth requires a number as argument");n=new Pn({scenario:{case:"subscriberBandwidth",value:Lo(t)}});break;case"leave-full-reconnect":n=new Pn({scenario:{case:"leaveRequestFullReconnect",value:!0}})}n&&(yield this.engine.client.sendSimulateScenario(n),yield i())}))}get canPlaybackAudio(){return this.audioEnabled}get canPlaybackVideo(){return!this.isVideoPlaybackBlocked}getActiveDevice(e){return this.localParticipant.activeDeviceMap.get(e)}switchActiveDevice(t,n){return Qn(this,arguments,void 0,(function(t,n){var i=this;let s=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return function*(){var o,r,a,c,d,l,u,h;let p=!0,m=!1;const g=s?{exact:n}:n;if("audioinput"===t){m=0===i.localParticipant.audioTrackPublications.size;const e=null!==(o=i.getActiveDevice(t))&&void 0!==o?o:i.options.audioCaptureDefaults.deviceId;i.options.audioCaptureDefaults.deviceId=g;const n=Array.from(i.localParticipant.audioTrackPublications.values()).filter((e=>e.source===Ks.Source.Microphone));try{p=(yield Promise.all(n.map((e=>{var t;return null===(t=e.audioTrack)||void 0===t?void 0:t.setDeviceId(g)})))).every((e=>!0===e))}catch(t){throw i.options.audioCaptureDefaults.deviceId=e,t}}else if("videoinput"===t){m=0===i.localParticipant.videoTrackPublications.size;const e=null!==(r=i.getActiveDevice(t))&&void 0!==r?r:i.options.videoCaptureDefaults.deviceId;i.options.videoCaptureDefaults.deviceId=g;const n=Array.from(i.localParticipant.videoTrackPublications.values()).filter((e=>e.source===Ks.Source.Camera));try{p=(yield Promise.all(n.map((e=>{var t;return null===(t=e.videoTrack)||void 0===t?void 0:t.setDeviceId(g)})))).every((e=>!0===e))}catch(t){throw i.options.videoCaptureDefaults.deviceId=e,t}}else if("audiooutput"===t){if(!ao()&&!i.options.webAudioMix||i.options.webAudioMix&&i.audioContext&&!("setSinkId"in i.audioContext))throw new Error("cannot switch audio output, setSinkId not supported");i.options.webAudioMix&&(n=null!==(a=yield or.getInstance().normalizeDeviceId("audiooutput",n))&&void 0!==a?a:""),null!==(c=(h=i.options).audioOutput)&&void 0!==c||(h.audioOutput={});const e=null!==(d=i.getActiveDevice(t))&&void 0!==d?d:i.options.audioOutput.deviceId;i.options.audioOutput.deviceId=n;try{i.options.webAudioMix&&(null===(l=i.audioContext)||void 0===l||l.setSinkId(n)),yield Promise.all(Array.from(i.remoteParticipants.values()).map((e=>e.setAudioOutput({deviceId:n}))))}catch(t){throw i.options.audioOutput.deviceId=e,t}}return(m||"audiooutput"===t)&&(i.localParticipant.activeDeviceMap.set(t,"audiooutput"===t&&(null===(u=i.options.audioOutput)||void 0===u?void 0:u.deviceId)||n),i.emit(e.RoomEvent.ActiveDeviceChanged,t,n)),p}()}))}setupLocalParticipantEvents(){this.localParticipant.on(e.ParticipantEvent.ParticipantMetadataChanged,this.onLocalParticipantMetadataChanged).on(e.ParticipantEvent.ParticipantNameChanged,this.onLocalParticipantNameChanged).on(e.ParticipantEvent.AttributesChanged,this.onLocalAttributesChanged).on(e.ParticipantEvent.TrackMuted,this.onLocalTrackMuted).on(e.ParticipantEvent.TrackUnmuted,this.onLocalTrackUnmuted).on(e.ParticipantEvent.LocalTrackPublished,this.onLocalTrackPublished).on(e.ParticipantEvent.LocalTrackUnpublished,this.onLocalTrackUnpublished).on(e.ParticipantEvent.ConnectionQualityChanged,this.onLocalConnectionQualityChanged).on(e.ParticipantEvent.MediaDevicesError,this.onMediaDevicesError).on(e.ParticipantEvent.AudioStreamAcquired,this.startAudio).on(e.ParticipantEvent.ChatMessage,this.onLocalChatMessageSent).on(e.ParticipantEvent.ParticipantPermissionsChanged,this.onLocalParticipantPermissionsChanged)}recreateEngine(){var e;null===(e=this.engine)||void 0===e||e.close(),this.engine=void 0,this.isResuming=!1,this.remoteParticipants.clear(),this.sidToIdentity.clear(),this.bufferedEvents=[],this.maybeCreateEngine()}onTrackAdded(t,n,i){if(this.state===e.ConnectionState.Connecting||this.state===e.ConnectionState.Reconnecting){const s=()=>{this.onTrackAdded(t,n,i),o()},o=()=>{this.off(e.RoomEvent.Reconnected,s),this.off(e.RoomEvent.Connected,s),this.off(e.RoomEvent.Disconnected,o)};return this.once(e.RoomEvent.Reconnected,s),this.once(e.RoomEvent.Connected,s),void this.once(e.RoomEvent.Disconnected,o)}if(this.state===e.ConnectionState.Disconnected)return void this.log.warn("skipping incoming track after Room disconnected",this.logContext);if("ended"===t.readyState)return void this.log.info("skipping incoming track as it already ended",this.logContext);const s=function(e){const t=e.split("|");return t.length>1?[t[0],e.substr(t[0].length+1)]:[e,""]}(n.id),o=s[0];let r=s[1],a=t.id;if(r&&r.startsWith("TR")&&(a=r),o===this.localParticipant.sid)return void this.log.warn("tried to create RemoteParticipant for local participant",this.logContext);const c=Array.from(this.remoteParticipants.values()).find((e=>e.sid===o));if(!c)return void this.log.error("Tried to add a track for a participant, that's not present. Sid: ".concat(o),this.logContext);let d;this.options.adaptiveStream&&(d="object"==typeof this.options.adaptiveStream?this.options.adaptiveStream:{}),c.addSubscribedMediaTrack(t,a,n,i,d)}handleDisconnect(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],n=arguments.length>1?arguments[1]:void 0;var i;if(this.clearConnectionReconcile(),this.isResuming=!1,this.bufferedEvents=[],this.transcriptionReceivedTimes.clear(),this.state!==e.ConnectionState.Disconnected){this.regionUrl=void 0;try{this.remoteParticipants.forEach((e=>{e.trackPublications.forEach((t=>{e.unpublishTrack(t.trackSid)}))})),this.localParticipant.trackPublications.forEach((e=>{var n,i,s;e.track&&this.localParticipant.unpublishTrack(e.track,t),t?(null===(n=e.track)||void 0===n||n.detach(),null===(i=e.track)||void 0===i||i.stop()):null===(s=e.track)||void 0===s||s.stopMonitor()})),this.localParticipant.off(e.ParticipantEvent.ParticipantMetadataChanged,this.onLocalParticipantMetadataChanged).off(e.ParticipantEvent.ParticipantNameChanged,this.onLocalParticipantNameChanged).off(e.ParticipantEvent.AttributesChanged,this.onLocalAttributesChanged).off(e.ParticipantEvent.TrackMuted,this.onLocalTrackMuted).off(e.ParticipantEvent.TrackUnmuted,this.onLocalTrackUnmuted).off(e.ParticipantEvent.LocalTrackPublished,this.onLocalTrackPublished).off(e.ParticipantEvent.LocalTrackUnpublished,this.onLocalTrackUnpublished).off(e.ParticipantEvent.ConnectionQualityChanged,this.onLocalConnectionQualityChanged).off(e.ParticipantEvent.MediaDevicesError,this.onMediaDevicesError).off(e.ParticipantEvent.AudioStreamAcquired,this.startAudio).off(e.ParticipantEvent.ChatMessage,this.onLocalChatMessageSent).off(e.ParticipantEvent.ParticipantPermissionsChanged,this.onLocalParticipantPermissionsChanged),this.localParticipant.trackPublications.clear(),this.localParticipant.videoTrackPublications.clear(),this.localParticipant.audioTrackPublications.clear(),this.remoteParticipants.clear(),this.sidToIdentity.clear(),this.activeSpeakers=[],this.audioContext&&"boolean"==typeof this.options.webAudioMix&&(this.audioContext.close(),this.audioContext=void 0),mo()&&(window.removeEventListener("beforeunload",this.onPageLeave),window.removeEventListener("pagehide",this.onPageLeave),window.removeEventListener("freeze",this.onPageLeave),null===(i=navigator.mediaDevices)||void 0===i||i.removeEventListener("devicechange",this.handleDeviceChange))}finally{this.setAndEmitConnectionState(e.ConnectionState.Disconnected),this.emit(e.RoomEvent.Disconnected,n)}}}handleParticipantDisconnected(t,n){var i;this.remoteParticipants.delete(t),n&&(n.trackPublications.forEach((e=>{n.unpublishTrack(e.trackSid,!0)})),this.emit(e.RoomEvent.ParticipantDisconnected,n),null===(i=this.localParticipant)||void 0===i||i.handleParticipantDisconnected(n.identity))}handleStreamHeader(e,t){return Qn(this,void 0,void 0,(function*(){var n;if("byteHeader"===e.contentHeader.case){const i=this.byteStreamHandlers.get(e.topic);if(!i)return void this.log.debug("ignoring incoming byte stream due to no handler for topic",e.topic);let s;const o={id:e.streamId,name:null!==(n=e.contentHeader.value.name)&&void 0!==n?n:"unknown",mimeType:e.mimeType,size:e.totalLength?Number(e.totalLength):void 0,topic:e.topic,timestamp:Ao(e.timestamp),attributes:e.attributes},r=new ReadableStream({start:t=>{s=t,this.byteStreamControllers.set(e.streamId,{info:o,controller:s,startTime:Date.now()})}});i(new ka(o,r,Ao(e.totalLength)),{identity:t})}else if("textHeader"===e.contentHeader.case){const n=this.textStreamHandlers.get(e.topic);if(!n)return void this.log.debug("ignoring incoming text stream due to no handler for topic",e.topic);let i;const s={id:e.streamId,mimeType:e.mimeType,size:e.totalLength?Number(e.totalLength):void 0,topic:e.topic,timestamp:Number(e.timestamp),attributes:e.attributes},o=new ReadableStream({start:t=>{i=t,this.textStreamControllers.set(e.streamId,{info:s,controller:i,startTime:Date.now()})}});n(new ba(s,o,Ao(e.totalLength)),{identity:t})}}))}handleStreamChunk(e){const t=this.byteStreamControllers.get(e.streamId);t&&e.content.length>0&&t.controller.enqueue(e);const n=this.textStreamControllers.get(e.streamId);n&&e.content.length>0&&n.controller.enqueue(e)}handleStreamTrailer(e){const t=this.textStreamControllers.get(e.streamId);t&&(t.info.attributes=Object.assign(Object.assign({},t.info.attributes),e.attributes),t.controller.close(),this.textStreamControllers.delete(e.streamId));const n=this.byteStreamControllers.get(e.streamId);n&&(n.info.attributes=Object.assign(Object.assign({},n.info.attributes),e.attributes),n.controller.close(),this.byteStreamControllers.delete(e.streamId))}acquireAudioContext(){return Qn(this,void 0,void 0,(function*(){var t,n;if("boolean"!=typeof this.options.webAudioMix&&this.options.webAudioMix.audioContext?this.audioContext=this.options.webAudioMix.audioContext:this.audioContext&&"closed"!==this.audioContext.state||(this.audioContext=null!==(t=Yo())&&void 0!==t?t:void 0),this.options.webAudioMix&&this.remoteParticipants.forEach((e=>e.setAudioContext(this.audioContext))),this.localParticipant.setAudioContext(this.audioContext),this.audioContext&&"suspended"===this.audioContext.state)try{yield Promise.race([this.audioContext.resume(),to(200)])}catch(e){this.log.warn("Could not resume audio context",Object.assign(Object.assign({},this.logContext),{error:e}))}const i="running"===(null===(n=this.audioContext)||void 0===n?void 0:n.state);i!==this.canPlaybackAudio&&(this.audioEnabled=i,this.emit(e.RoomEvent.AudioPlaybackStatusChanged,i))}))}createParticipant(e,t){var n;let i;return i=t?_a.fromParticipantInfo(this.engine.client,t,{loggerContextCb:()=>this.logContext,loggerName:this.options.loggerName}):new _a(this.engine.client,"",e,void 0,void 0,void 0,{loggerContextCb:()=>this.logContext,loggerName:this.options.loggerName}),this.options.webAudioMix&&i.setAudioContext(this.audioContext),(null===(n=this.options.audioOutput)||void 0===n?void 0:n.deviceId)&&i.setAudioOutput(this.options.audioOutput).catch((e=>this.log.warn("Could not set audio output: ".concat(e.message),this.logContext))),i}getOrCreateParticipant(t,n){if(this.remoteParticipants.has(t)){const e=this.remoteParticipants.get(t);if(n){e.updateInfo(n)&&this.sidToIdentity.set(n.sid,n.identity)}return e}const i=this.createParticipant(t,n);return this.remoteParticipants.set(t,i),this.sidToIdentity.set(n.sid,n.identity),this.emitWhenConnected(e.RoomEvent.ParticipantConnected,i),i.on(e.ParticipantEvent.TrackPublished,(t=>{this.emitWhenConnected(e.RoomEvent.TrackPublished,t,i)})).on(e.ParticipantEvent.TrackSubscribed,((t,n)=>{t.kind===Ks.Kind.Audio?(t.on(e.TrackEvent.AudioPlaybackStarted,this.handleAudioPlaybackStarted),t.on(e.TrackEvent.AudioPlaybackFailed,this.handleAudioPlaybackFailed)):t.kind===Ks.Kind.Video&&(t.on(e.TrackEvent.VideoPlaybackFailed,this.handleVideoPlaybackFailed),t.on(e.TrackEvent.VideoPlaybackStarted,this.handleVideoPlaybackStarted)),this.emit(e.RoomEvent.TrackSubscribed,t,n,i)})).on(e.ParticipantEvent.TrackUnpublished,(t=>{this.emit(e.RoomEvent.TrackUnpublished,t,i)})).on(e.ParticipantEvent.TrackUnsubscribed,((t,n)=>{this.emit(e.RoomEvent.TrackUnsubscribed,t,n,i)})).on(e.ParticipantEvent.TrackMuted,(t=>{this.emitWhenConnected(e.RoomEvent.TrackMuted,t,i)})).on(e.ParticipantEvent.TrackUnmuted,(t=>{this.emitWhenConnected(e.RoomEvent.TrackUnmuted,t,i)})).on(e.ParticipantEvent.ParticipantMetadataChanged,(t=>{this.emitWhenConnected(e.RoomEvent.ParticipantMetadataChanged,t,i)})).on(e.ParticipantEvent.ParticipantNameChanged,(t=>{this.emitWhenConnected(e.RoomEvent.ParticipantNameChanged,t,i)})).on(e.ParticipantEvent.AttributesChanged,(t=>{this.emitWhenConnected(e.RoomEvent.ParticipantAttributesChanged,t,i)})).on(e.ParticipantEvent.ConnectionQualityChanged,(t=>{this.emitWhenConnected(e.RoomEvent.ConnectionQualityChanged,t,i)})).on(e.ParticipantEvent.ParticipantPermissionsChanged,(t=>{this.emitWhenConnected(e.RoomEvent.ParticipantPermissionsChanged,t,i)})).on(e.ParticipantEvent.TrackSubscriptionStatusChanged,((t,n)=>{this.emitWhenConnected(e.RoomEvent.TrackSubscriptionStatusChanged,t,n,i)})).on(e.ParticipantEvent.TrackSubscriptionFailed,((t,n)=>{this.emit(e.RoomEvent.TrackSubscriptionFailed,t,i,n)})).on(e.ParticipantEvent.TrackSubscriptionPermissionChanged,((t,n)=>{this.emitWhenConnected(e.RoomEvent.TrackSubscriptionPermissionChanged,t,n,i)})),n&&i.updateInfo(n),i}sendSyncState(){const e=Array.from(this.remoteParticipants.values()).reduce(((e,t)=>(e.push(...t.getTrackPublications()),e)),[]),t=this.localParticipant.getTrackPublications();this.engine.sendSyncState(e,t)}updateSubscriptions(){for(const e of this.remoteParticipants.values())for(const t of e.videoTrackPublications.values())t.isSubscribed&&Ko(t)&&t.emitTrackUpdate()}getRemoteParticipantBySid(e){const t=this.sidToIdentity.get(e);if(t)return this.remoteParticipants.get(t)}registerConnectionReconcile(){this.clearConnectionReconcile();let e=0;this.connectionReconcileInterval=Vs.setInterval((()=>{this.engine&&!this.engine.isClosed&&this.engine.verifyTransport()?e=0:(e++,this.log.warn("detected connection state mismatch",Object.assign(Object.assign({},this.logContext),{numFailures:e,engine:this.engine?{closed:this.engine.isClosed,transportsConnected:this.engine.verifyTransport()}:void 0})),e>=3&&(this.recreateEngine(),this.handleDisconnect(this.options.stopLocalTrackOnUnpublish,$e.STATE_MISMATCH)))}),4e3)}clearConnectionReconcile(){this.connectionReconcileInterval&&Vs.clearInterval(this.connectionReconcileInterval)}setAndEmitConnectionState(t){return t!==this.state&&(this.state=t,this.emit(e.RoomEvent.ConnectionStateChanged,this.state),!0)}emitBufferedEvents(){this.bufferedEvents.forEach((e=>{let[t,n]=e;this.emit(t,...n)})),this.bufferedEvents=[]}emitWhenConnected(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),s=1;s<n;s++)i[s-1]=arguments[s];if(this.state===e.ConnectionState.Reconnecting||this.isResuming||!this.engine||this.engine.pendingReconnect)this.bufferedEvents.push([t,i]);else if(this.state===e.ConnectionState.Connected)return this.emit(t,...i);return!1}simulateParticipants(t){return Qn(this,void 0,void 0,(function*(){var n,i;const s=Object.assign({audio:!0,video:!0,useRealTracks:!1},t.publish),o=Object.assign({count:9,audio:!1,video:!0,aspectRatios:[1.66,1.7,1.3]},t.participants);if(this.handleDisconnect(),this.roomInfo=new it({sid:"RM_SIMULATED",name:"simulated-room",emptyTimeout:0,maxParticipants:0,creationTime:x.parse((new Date).getTime()),metadata:"",numParticipants:1,numPublishers:1,turnPassword:"",enabledCodecs:[],activeRecording:!1}),this.localParticipant.updateInfo(new rt({identity:"simulated-local",name:"local-name"})),this.setupLocalParticipantEvents(),this.emit(e.RoomEvent.SignalConnected),this.emit(e.RoomEvent.Connected),this.setAndEmitConnectionState(e.ConnectionState.Connected),s.video){const t=new Oa(Ks.Kind.Video,new ut({source:Qe.CAMERA,sid:Math.floor(1e4*Math.random()).toString(),type:Je.AUDIO,name:"video-dummy"}),new ra(s.useRealTracks?(yield window.navigator.mediaDevices.getUserMedia({video:!0})).getVideoTracks()[0]:Oo(160*(null!==(n=o.aspectRatios[0])&&void 0!==n?n:1),160,!0,!0),void 0,!1,{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext}),{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext});this.localParticipant.addTrackPublication(t),this.localParticipant.emit(e.ParticipantEvent.LocalTrackPublished,t)}if(s.audio){const t=new Oa(Ks.Kind.Audio,new ut({source:Qe.MICROPHONE,sid:Math.floor(1e4*Math.random()).toString(),type:Je.AUDIO}),new Gr(s.useRealTracks?(yield navigator.mediaDevices.getUserMedia({audio:!0})).getAudioTracks()[0]:Do(),void 0,!1,this.audioContext,{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext}),{loggerName:this.options.loggerName,loggerContextCb:()=>this.logContext});this.localParticipant.addTrackPublication(t),this.localParticipant.emit(e.ParticipantEvent.LocalTrackPublished,t)}for(let e=0;e<o.count-1;e+=1){let t=new rt({sid:Math.floor(1e4*Math.random()).toString(),identity:"simulated-".concat(e),state:at.ACTIVE,tracks:[],joinedAt:x.parse(Date.now())});const n=this.getOrCreateParticipant(t.identity,t);if(o.video){const s=Oo(160*(null!==(i=o.aspectRatios[e%o.aspectRatios.length])&&void 0!==i?i:1),160,!1,!0),r=new ut({source:Qe.CAMERA,sid:Math.floor(1e4*Math.random()).toString(),type:Je.AUDIO});n.addSubscribedMediaTrack(s,r.sid,new MediaStream([s]),new RTCRtpReceiver),t.tracks=[...t.tracks,r]}if(o.audio){const e=Do(),i=new ut({source:Qe.MICROPHONE,sid:Math.floor(1e4*Math.random()).toString(),type:Je.AUDIO});n.addSubscribedMediaTrack(e,i.sid,new MediaStream([e]),new RTCRtpReceiver),t.tracks=[...t.tracks,i]}n.updateInfo(t)}}))}emit(t){for(var n=arguments.length,i=new Array(n>1?n-1:0),s=1;s<n;s++)i[s-1]=arguments[s];if(t!==e.RoomEvent.ActiveSpeakersChanged&&t!==e.RoomEvent.TranscriptionReceived){const e=Ua(i).filter((e=>void 0!==e));this.log.debug("room event ".concat(t),Object.assign(Object.assign({},this.logContext),{event:t,args:e}))}return super.emit(t,...i)}}function Ua(e){return e.map((e=>{if(e)return Array.isArray(e)?Ua(e):"object"==typeof e?"logContext"in e?e.logContext:void 0:e}))}var ja;La.cleanupRegistry="undefined"!=typeof FinalizationRegistry&&new FinalizationRegistry((e=>{e()})),e.CheckStatus=void 0,(ja=e.CheckStatus||(e.CheckStatus={}))[ja.IDLE=0]="IDLE",ja[ja.RUNNING=1]="RUNNING",ja[ja.SKIPPED=2]="SKIPPED",ja[ja.SUCCESS=3]="SUCCESS",ja[ja.FAILED=4]="FAILED";class Fa extends ei.EventEmitter{constructor(t,n){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};super(),this.status=e.CheckStatus.IDLE,this.logs=[],this.options={},this.url=t,this.token=n,this.name=this.constructor.name,this.room=new La(i.roomOptions),this.connectOptions=i.connectOptions,this.options=i}run(t){return Qn(this,void 0,void 0,(function*(){if(this.status!==e.CheckStatus.IDLE)throw Error("check is running already");this.setStatus(e.CheckStatus.RUNNING);try{yield this.perform()}catch(e){e instanceof Error&&(this.options.errorsAsWarnings?this.appendWarning(e.message):this.appendError(e.message))}return yield this.disconnect(),yield new Promise((e=>setTimeout(e,500))),this.status!==e.CheckStatus.SKIPPED&&this.setStatus(this.isSuccess()?e.CheckStatus.SUCCESS:e.CheckStatus.FAILED),t&&t(),this.getInfo()}))}isSuccess(){return!this.logs.some((e=>"error"===e.level))}connect(t){return Qn(this,void 0,void 0,(function*(){return this.room.state===e.ConnectionState.Connected||(t||(t=this.url),yield this.room.connect(t,this.token,this.connectOptions)),this.room}))}disconnect(){return Qn(this,void 0,void 0,(function*(){this.room&&this.room.state!==e.ConnectionState.Disconnected&&(yield this.room.disconnect(),yield new Promise((e=>setTimeout(e,500))))}))}skip(){this.setStatus(e.CheckStatus.SKIPPED)}switchProtocol(t){return Qn(this,void 0,void 0,(function*(){let n=!1,i=!1;if(this.room.on(e.RoomEvent.Reconnecting,(()=>{n=!0})),this.room.once(e.RoomEvent.Reconnected,(()=>{i=!0})),this.room.simulateScenario("force-".concat(t)),yield new Promise((e=>setTimeout(e,1e3))),!n)return;const s=Date.now()+1e4;for(;Date.now()<s;){if(i)return;yield to(100)}throw new Error("Could not reconnect using ".concat(t," protocol after 10 seconds"))}))}appendMessage(e){this.logs.push({level:"info",message:e}),this.emit("update",this.getInfo())}appendWarning(e){this.logs.push({level:"warning",message:e}),this.emit("update",this.getInfo())}appendError(e){this.logs.push({level:"error",message:e}),this.emit("update",this.getInfo())}setStatus(e){this.status=e,this.emit("update",this.getInfo())}get engine(){var e;return null===(e=this.room)||void 0===e?void 0:e.engine}getInfo(){return{logs:this.logs,name:this.name,status:this.status,description:this.description}}}class Ba extends Fa{get description(){return"Cloud regions"}perform(){return Qn(this,void 0,void 0,(function*(){const e=new va(this.url,this.token);if(!e.isCloud())return void this.skip();const t=[],n=new Set;for(let i=0;i<3;i++){const i=yield e.getNextBestRegionUrl();if(!i)break;if(n.has(i))continue;n.add(i);const s=yield this.checkCloudRegion(i);this.appendMessage("".concat(s.region," RTT: ").concat(s.rtt,"ms, duration: ").concat(s.duration,"ms")),t.push(s)}t.sort(((e,t)=>.5*(e.duration-t.duration)+.5*(e.rtt-t.rtt)));const i=t[0];this.bestStats=i,this.appendMessage("best Cloud region: ".concat(i.region))}))}getInfo(){const e=super.getInfo();return e.data=this.bestStats,e}checkCloudRegion(e){return Qn(this,void 0,void 0,(function*(){var t,n;yield this.connect(e),"tcp"===this.options.protocol&&(yield this.switchProtocol("tcp"));const i=null===(t=this.room.serverInfo)||void 0===t?void 0:t.region;if(!i)throw new Error("Region not found");const s=yield this.room.localParticipant.streamText({topic:"test"}),o="A".repeat(1e3),r=Date.now();for(let e=0;e<1e3;e++)yield s.write(o);yield s.close();const a=Date.now(),c=yield null===(n=this.room.engine.pcManager)||void 0===n?void 0:n.publisher.getStats(),d={region:i,rtt:1e4,duration:a-r};return null==c||c.forEach((e=>{"candidate-pair"===e.type&&e.nominated&&(d.rtt=1e3*e.currentRoundTripTime)})),yield this.disconnect(),d}))}}const Va=1e4;class qa extends Fa{get description(){return"Connection via UDP vs TCP"}perform(){return Qn(this,void 0,void 0,(function*(){const e=yield this.checkConnectionProtocol("udp"),t=yield this.checkConnectionProtocol("tcp");this.bestStats=e,e.qualityLimitationDurations.bandwidth-t.qualityLimitationDurations.bandwidth>.5||(e.packetsLost-t.packetsLost)/e.packetsSent>.01?(this.appendMessage("best connection quality via tcp"),this.bestStats=t):this.appendMessage("best connection quality via udp");const n=this.bestStats;this.appendMessage("upstream bitrate: ".concat((n.bitrateTotal/n.count/1e3/1e3).toFixed(2)," mbps")),this.appendMessage("RTT: ".concat((n.rttTotal/n.count*1e3).toFixed(2)," ms")),this.appendMessage("jitter: ".concat((n.jitterTotal/n.count*1e3).toFixed(2)," ms")),n.packetsLost>0&&this.appendWarning("packets lost: ".concat((n.packetsLost/n.packetsSent*100).toFixed(2),"%")),n.qualityLimitationDurations.bandwidth>1&&this.appendWarning("bandwidth limited ".concat((n.qualityLimitationDurations.bandwidth/10*100).toFixed(2),"%")),n.qualityLimitationDurations.cpu>0&&this.appendWarning("cpu limited ".concat((n.qualityLimitationDurations.cpu/10*100).toFixed(2),"%"))}))}getInfo(){const e=super.getInfo();return e.data=this.bestStats,e}checkConnectionProtocol(e){return Qn(this,void 0,void 0,(function*(){yield this.connect(),"tcp"===e?yield this.switchProtocol("tcp"):yield this.switchProtocol("udp");const t=document.createElement("canvas");t.width=1280,t.height=720;const n=t.getContext("2d");if(!n)throw new Error("Could not get canvas context");let i=0;const s=()=>{i=(i+1)%360,n.fillStyle="hsl(".concat(i,", 100%, 50%)"),n.fillRect(0,0,t.width,t.height),requestAnimationFrame(s)};s();const o=t.captureStream(30).getVideoTracks()[0],r=(yield this.room.localParticipant.publishTrack(o,{simulcast:!1,degradationPreference:"maintain-resolution",videoEncoding:{maxBitrate:2e6}})).track,a={protocol:e,packetsLost:0,packetsSent:0,qualityLimitationDurations:{},rttTotal:0,jitterTotal:0,bitrateTotal:0,count:0},c=setInterval((()=>Qn(this,void 0,void 0,(function*(){const e=yield r.getRTCStatsReport();null==e||e.forEach((e=>{"outbound-rtp"===e.type?(a.packetsSent=e.packetsSent,a.qualityLimitationDurations=e.qualityLimitationDurations,a.bitrateTotal+=e.targetBitrate,a.count++):"remote-inbound-rtp"===e.type&&(a.packetsLost=e.packetsLost,a.rttTotal+=e.roundTripTime,a.jitterTotal+=e.jitter)}))}))),1e3);return yield new Promise((e=>setTimeout(e,Va))),clearInterval(c),o.stop(),t.remove(),yield this.disconnect(),a}))}}function Ka(e){return Qn(this,void 0,void 0,(function*(){var t,n;null!=e||(e={}),null!==(t=e.audio)&&void 0!==t||(e.audio={deviceId:"default"}),null!==(n=e.video)&&void 0!==n||(e.video={deviceId:"default"});const{audioProcessor:i,videoProcessor:s}=nr(e),o=Go(e,_r,Ar),r=Jo(o),a=navigator.mediaDevices.getUserMedia(r);e.audio&&(or.userMediaPromiseMap.set("audioinput",a),a.catch((()=>or.userMediaPromiseMap.delete("audioinput")))),e.video&&(or.userMediaPromiseMap.set("videoinput",a),a.catch((()=>or.userMediaPromiseMap.delete("videoinput"))));const c=yield a;return Promise.all(c.getTracks().map((e=>Qn(this,void 0,void 0,(function*(){const t="audio"===e.kind;let n;t?o.audio:o.video;const a=t?r.audio:r.video;"boolean"!=typeof a&&(n=a);const d=e.getSettings().deviceId;(null==n?void 0:n.deviceId)&&Mo(n.deviceId)!==d?n.deviceId=d:n||(n={deviceId:d});const l=zr(e,n);return l.kind===Ks.Kind.Video?l.source=Ks.Source.Camera:l.kind===Ks.Kind.Audio&&(l.source=Ks.Source.Microphone),l.mediaStream=c,jo(l)&&i?yield l.setProcessor(i):Fo(l)&&s&&(yield l.setProcessor(s)),l})))))}))}function Wa(e){return Qn(this,void 0,void 0,(function*(){return(yield Ka({audio:!1,video:e}))[0]}))}function Ha(e){return Qn(this,void 0,void 0,(function*(){return(yield Ka({audio:e,video:!1}))[0]}))}class Ga extends Fa{get description(){return"Can publish audio"}perform(){return Qn(this,void 0,void 0,(function*(){var e;const t=yield this.connect(),n=yield Ha();if(yield Qo(n,1e3))throw new Error("unable to detect audio from microphone");this.appendMessage("detected audio from microphone"),t.localParticipant.publishTrack(n),yield new Promise((e=>setTimeout(e,3e3)));const i=yield null===(e=n.sender)||void 0===e?void 0:e.getStats();if(!i)throw new Error("Could not get RTCStats");let s=0;if(i.forEach((e=>{"outbound-rtp"!==e.type||"audio"!==e.kind&&(e.kind||"audio"!==e.mediaType)||(s=e.packetsSent)})),0===s)throw new Error("Could not determine packets are sent");this.appendMessage("published ".concat(s," audio packets"))}))}}class za extends Fa{get description(){return"Can publish video"}perform(){return Qn(this,void 0,void 0,(function*(){var e;const t=yield this.connect(),n=yield Wa();yield this.checkForVideo(n.mediaStreamTrack),t.localParticipant.publishTrack(n),yield new Promise((e=>setTimeout(e,5e3)));const i=yield null===(e=n.sender)||void 0===e?void 0:e.getStats();if(!i)throw new Error("Could not get RTCStats");let s=0;if(i.forEach((e=>{"outbound-rtp"!==e.type||"video"!==e.kind&&(e.kind||"video"!==e.mediaType)||(s+=e.packetsSent)})),0===s)throw new Error("Could not determine packets are sent");this.appendMessage("published ".concat(s," video packets"))}))}checkForVideo(e){return Qn(this,void 0,void 0,(function*(){const t=new MediaStream;t.addTrack(e.clone());const n=document.createElement("video");n.srcObject=t,n.muted=!0,yield new Promise((t=>{n.onplay=()=>{setTimeout((()=>{var i,s,o,r;const a=document.createElement("canvas"),c=e.getSettings(),d=null!==(s=null!==(i=c.width)&&void 0!==i?i:n.videoWidth)&&void 0!==s?s:1280,l=null!==(r=null!==(o=c.height)&&void 0!==o?o:n.videoHeight)&&void 0!==r?r:720;a.width=d,a.height=l;const u=a.getContext("2d");u.drawImage(n,0,0);const h=u.getImageData(0,0,a.width,a.height).data;let p=!0;for(let e=0;e<h.length;e+=4)if(0!==h[e]||0!==h[e+1]||0!==h[e+2]){p=!1;break}p?this.appendError("camera appears to be producing only black frames"):this.appendMessage("received video frames"),t()}),1e3)},n.play()})),n.remove()}))}}class Ja extends Fa{get description(){return"Resuming connection after interruption"}perform(){return Qn(this,void 0,void 0,(function*(){var t;const n=yield this.connect();let i,s=!1,o=!1;const r=new Promise((e=>{setTimeout(e,5e3),i=e})),a=()=>{s=!0};n.on(e.RoomEvent.SignalReconnecting,a).on(e.RoomEvent.Reconnecting,a).on(e.RoomEvent.Reconnected,(()=>{o=!0,i(!0)})),null===(t=n.engine.client.ws)||void 0===t||t.close();const c=n.engine.client.onClose;if(c&&c(""),yield r,!s)throw new Error("Did not attempt to reconnect");if(!o||n.state!==e.ConnectionState.Connected)throw this.appendWarning("reconnection is only possible in Redis-based configurations"),new Error("Not able to reconnect")}))}}class Qa extends Fa{get description(){return"Can connect via TURN"}perform(){return Qn(this,void 0,void 0,(function*(){var e,t;const n=new lr,i=yield n.join(this.url,this.token,{autoSubscribe:!0,maxRetries:0,e2eeEnabled:!1,websocketTimeout:15e3});let s=!1,o=!1,r=!1;for(let e of i.iceServers)for(let t of e.urls)t.startsWith("turn:")?(o=!0,r=!0):t.startsWith("turns:")&&(o=!0,r=!0,s=!0),t.startsWith("stun:")&&(r=!0);r?o&&!s&&this.appendWarning("TURN is configured server side, but TURN/TLS is unavailable."):this.appendWarning("No STUN servers configured on server side."),yield n.close(),(null===(t=null===(e=this.connectOptions)||void 0===e?void 0:e.rtcConfig)||void 0===t?void 0:t.iceServers)||o?yield this.room.connect(this.url,this.token,{rtcConfig:{iceTransportPolicy:"relay"}}):(this.appendWarning("No TURN servers configured."),this.skip(),yield new Promise((e=>setTimeout(e,0))))}))}}class Ya extends Fa{get description(){return"Establishing WebRTC connection"}perform(){return Qn(this,void 0,void 0,(function*(){let t=!1,n=!1;this.room.on(e.RoomEvent.SignalConnected,(()=>{const e=this.room.engine.client.onTrickle;this.room.engine.client.onTrickle=(i,s)=>{if(i.candidate){const e=new RTCIceCandidate(i);let s="".concat(e.protocol," ").concat(e.address,":").concat(e.port," ").concat(e.type);e.address&&(!function(e){const t=e.split(".");if(4===t.length){if("10"===t[0])return!0;if("192"===t[0]&&"168"===t[1])return!0;if("172"===t[0]){const e=parseInt(t[1],10);if(e>=16&&e<=31)return!0}}return!1}(e.address)?"tcp"===e.protocol&&"passive"===e.tcpType?(t=!0,s+=" (passive)"):"udp"===e.protocol&&(n=!0):s+=" (private)"),this.appendMessage(s)}e&&e(i,s)},this.room.engine.pcManager&&(this.room.engine.pcManager.subscriber.onIceCandidateError=e=>{e instanceof RTCPeerConnectionIceErrorEvent&&this.appendWarning("error with ICE candidate: ".concat(e.errorCode," ").concat(e.errorText," ").concat(e.url))})}));try{yield this.connect(),qn.info("now the room is connected")}catch(e){throw this.appendWarning("ports need to be open on firewall in order to connect."),e}t||this.appendWarning("Server is not configured for ICE/TCP"),n||this.appendWarning("No public IPv4 UDP candidates were found. Your server is likely not configured correctly")}))}}class Xa extends Fa{get description(){return"Connecting to signal connection via WebSocket"}perform(){return Qn(this,void 0,void 0,(function*(){var e,t,n;(this.url.startsWith("ws:")||this.url.startsWith("http:"))&&this.appendWarning("Server is insecure, clients may block connections to it");let i=new lr;const s=yield i.join(this.url,this.token,{autoSubscribe:!0,maxRetries:0,e2eeEnabled:!1,websocketTimeout:15e3});this.appendMessage("Connected to server, version ".concat(s.serverVersion,".")),(null===(e=s.serverInfo)||void 0===e?void 0:e.edition)===It.Cloud&&(null===(t=s.serverInfo)||void 0===t?void 0:t.region)&&this.appendMessage("LiveKit Cloud: ".concat(null===(n=s.serverInfo)||void 0===n?void 0:n.region)),yield i.close()}))}}class Za extends ei.EventEmitter{constructor(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};super(),this.options={},this.checkResults=new Map,this.url=e,this.token=t,this.options=n}getNextCheckId(){const t=this.checkResults.size;return this.checkResults.set(t,{logs:[],status:e.CheckStatus.IDLE,name:"",description:""}),t}updateCheck(e,t){this.checkResults.set(e,t),this.emit("checkUpdate",e,t)}isSuccess(){return Array.from(this.checkResults.values()).every((t=>t.status!==e.CheckStatus.FAILED))}getResults(){return Array.from(this.checkResults.values())}createAndRunCheck(e){return Qn(this,void 0,void 0,(function*(){const t=this.getNextCheckId(),n=new e(this.url,this.token,this.options),i=e=>{this.updateCheck(t,e)};n.on("update",i);const s=yield n.run();return n.off("update",i),s}))}checkWebsocket(){return Qn(this,void 0,void 0,(function*(){return this.createAndRunCheck(Xa)}))}checkWebRTC(){return Qn(this,void 0,void 0,(function*(){return this.createAndRunCheck(Ya)}))}checkTURN(){return Qn(this,void 0,void 0,(function*(){return this.createAndRunCheck(Qa)}))}checkReconnect(){return Qn(this,void 0,void 0,(function*(){return this.createAndRunCheck(Ja)}))}checkPublishAudio(){return Qn(this,void 0,void 0,(function*(){return this.createAndRunCheck(Ga)}))}checkPublishVideo(){return Qn(this,void 0,void 0,(function*(){return this.createAndRunCheck(za)}))}checkConnectionProtocol(){return Qn(this,void 0,void 0,(function*(){const e=yield this.createAndRunCheck(qa);if(e.data&&"protocol"in e.data){const t=e.data;this.options.protocol=t.protocol}return e}))}checkCloudRegion(){return Qn(this,void 0,void 0,(function*(){return this.createAndRunCheck(Ba)}))}}const $a=new Map([["obs virtual camera",{facingMode:"environment",confidence:"medium"}]]),ec=new Map([["iphone",{facingMode:"environment",confidence:"medium"}],["ipad",{facingMode:"environment",confidence:"medium"}]]);function tc(e){var t;const n=e.trim().toLowerCase();if(""!==n)return $a.has(n)?$a.get(n):null===(t=Array.from(ec.entries()).find((e=>{let[t]=e;return n.includes(t)})))||void 0===t?void 0:t[1]}e.BaseKeyProvider=fs,e.Checker=Fa,e.ConnectionCheck=Za,e.ConnectionError=Ps,e.CriticalTimers=Vs,e.CryptorError=class extends ks{constructor(t){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.CryptorErrorReason.InternalError,i=arguments.length>2?arguments[2]:void 0;super(40,t),this.reason=n,this.participantIdentity=i}},e.DataPacket_Kind=mt,e.DefaultReconnectPolicy=Jn,e.DeviceUnsupportedError=Rs,e.DisconnectReason=$e,e.ExternalE2EEKeyProvider=class extends fs{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super(Object.assign(Object.assign({},e),{sharedKey:!0,ratchetWindowSize:0,failureTolerance:-1}))}setKey(e){return Qn(this,void 0,void 0,(function*(){const t="string"==typeof e?yield ms(e):yield gs(e);this.onSetEncryptionKey(t)}))}},e.LivekitError=ks,e.LocalAudioTrack=Gr,e.LocalParticipant=Ma,e.LocalTrack=Hr,e.LocalTrackPublication=Oa,e.LocalVideoTrack=ra,e.Mutex=s,e.NegotiationError=xs,e.Participant=Da,e.ParticipantKind=ct,e.PublishDataError=class extends ks{constructor(e){super(14,null!=e?e:"unable to publish data"),this.name="PublishDataError"}},e.PublishTrackError=Ms,e.RemoteAudioTrack=Sa,e.RemoteParticipant=_a,e.RemoteTrack=Ca,e.RemoteTrackPublication=Na,e.RemoteVideoTrack=Ea,e.Room=La,e.RpcError=Br,e.ScreenSharePresets=$s,e.SignalRequestError=Ns,e.SubscriptionError=tt,e.Track=Ks,e.TrackInvalidError=Is,e.TrackPublication=Ia,e.TrackType=Je,e.UnexpectedConnectionState=Ds,e.UnsupportedServer=Os,e.VideoPreset=Gs,e.VideoPresets=Xs,e.VideoPresets43=Zs,e.attachToElement=Ws,e.compareVersions=yo,e.createAudioAnalyser=function(e,t){const n=Object.assign({cloneTrack:!1,fftSize:2048,smoothingTimeConstant:.8,minDecibels:-100,maxDecibels:-80},t),i=Yo();if(!i)throw new Error("Audio Context not supported on this browser");const s=n.cloneTrack?e.mediaStreamTrack.clone():e.mediaStreamTrack,o=i.createMediaStreamSource(new MediaStream([s])),r=i.createAnalyser();r.minDecibels=n.minDecibels,r.maxDecibels=n.maxDecibels,r.fftSize=n.fftSize,r.smoothingTimeConstant=n.smoothingTimeConstant,o.connect(r);const a=new Uint8Array(r.frequencyBinCount);return{calculateVolume:()=>{r.getByteFrequencyData(a);let e=0;for(const t of a)e+=Math.pow(t/255,2);return Math.sqrt(e/a.length)},analyser:r,cleanup:()=>Qn(this,void 0,void 0,(function*(){yield i.close(),n.cloneTrack&&s.stop()}))}},e.createE2EEKey=function(){return window.crypto.getRandomValues(new Uint8Array(32))},e.createKeyMaterialFromBuffer=gs,e.createKeyMaterialFromString=ms,e.createLocalAudioTrack=Ha,e.createLocalScreenTracks=function(e){return Qn(this,void 0,void 0,(function*(){if(void 0===e&&(e={}),void 0!==e.resolution||ho()||(e.resolution=$s.h1080fps30.resolution),void 0===navigator.mediaDevices.getDisplayMedia)throw new Rs("getDisplayMedia not supported");const t=Zo(e),n=yield navigator.mediaDevices.getDisplayMedia(t),i=n.getVideoTracks();if(0===i.length)throw new Is("no video track found");const s=new ra(i[0],void 0,!1);s.source=Ks.Source.ScreenShare;const o=[s];if(n.getAudioTracks().length>0){const e=new Gr(n.getAudioTracks()[0],void 0,!1);e.source=Ks.Source.ScreenShareAudio,o.push(e)}return o}))},e.createLocalTracks=Ka,e.createLocalVideoTrack=Wa,e.deriveKeys=function(e,t){return Qn(this,void 0,void 0,(function*(){const n=vs(e.algorithm.name,t),i=yield crypto.subtle.deriveKey(n,e,{name:rs,length:128},!1,["encrypt","decrypt"]);return{material:e,encryptionKey:i}}))},e.detachTrack=Hs,e.facingModeFromDeviceLabel=tc,e.facingModeFromLocalTrack=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var n;const i=Uo(e)?e.mediaStreamTrack:e,s=i.getSettings();let o={facingMode:null!==(n=t.defaultFacingMode)&&void 0!==n?n:"user",confidence:"low"};if("facingMode"in s){const e=s.facingMode;qn.trace("rawFacingMode",{rawFacingMode:e}),e&&"string"==typeof e&&function(e){const t=["user","environment","left","right"];return void 0===e||t.includes(e)}(e)&&(o={facingMode:e,confidence:"high"})}if(["low","medium"].includes(o.confidence)){qn.trace("Try to get facing mode from device label: (".concat(i.label,")"));const e=tc(i.label);void 0!==e&&(o=e)}return o},e.getBrowser=Ls,e.getEmptyAudioStreamTrack=Do,e.getEmptyVideoStreamTrack=function(){return Ro||(Ro=Oo()),Ro.clone()},e.getLogger=Wn,e.importKey=function(e){return Qn(this,arguments,void 0,(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{name:rs},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"encrypt";return function*(){return crypto.subtle.importKey("raw",e,t,!1,"derive"===n?["deriveBits","deriveKey"]:["encrypt","decrypt"])}()}))},e.isAudioTrack=jo,e.isBackupCodec=Qs,e.isBrowserSupported=co,e.isE2EESupported=us,e.isInsertableStreamSupported=ps,e.isLocalParticipant=Ho,e.isLocalTrack=Uo,e.isRemoteParticipant=function(e){return!e.isLocal},e.isRemoteTrack=qo,e.isScriptTransformSupported=hs,e.isVideoFrame=function(e){return"type"in e},e.isVideoTrack=Fo,e.needsRbspUnescaping=function(e){for(var t=0;t<e.length-3;t++)if(0==e[t]&&0==e[t+1]&&3==e[t+2])return!0;return!1},e.parseRbsp=function(e){const t=[];for(var n=e.length,i=0;i<e.length;)n-i>=3&&!e[i]&&!e[i+1]&&3==e[i+2]?(t.push(e[i++]),t.push(e[i++]),i++):t.push(e[i++]);return new Uint8Array(t)},e.protocolVersion=15,e.ratchet=function(e,t){return Qn(this,void 0,void 0,(function*(){const n=vs(e.algorithm.name,t);return crypto.subtle.deriveBits(n,e,256)}))},e.setLogExtension=function(t,n){(n?[n]:Kn).forEach((n=>{const i=n.methodFactory;n.methodFactory=(n,s,o)=>{const r=i(n,s,o),a=e.LogLevel[n],c=a>=s&&a<e.LogLevel.silent;return(e,n)=>{n?r(e,n):r(e),c&&t(a,e,n)}},n.setLevel(n.getLevel())}))},e.setLogLevel=function(e,t){if(t)Vn.getLogger(t).setLevel(e);else for(const t of Kn)t.setLevel(e)},e.supportsAV1=so,e.supportsAdaptiveStream=function(){return void 0!==typeof ResizeObserver&&void 0!==typeof IntersectionObserver},e.supportsDynacast=function(){return no()},e.supportsVP9=oo,e.version=Bs,e.videoCodecs=Js,e.writeRbsp=function(e){const t=[];for(var n=0,i=0;i<e.length;++i){var s=e[i];s<=3&&n>=2&&(t.push(3),n=0),t.push(s),0==s?++n:n=0}return new Uint8Array(t)}}));
//# sourceMappingURL=livekit-client.umd.js.map
