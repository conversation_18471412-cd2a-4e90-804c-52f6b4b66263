/**
 * Voice AI Shopping Assistant for Shopify
 * 
 * This script provides voice recognition and natural language processing
 * to allow customers to search for products and get recommendations
 * using their voice with an interactive visualizer.
 * 
 * Enhanced with autonomous tool calling for browser automation and web research.
 */

// Import modules
import { VoiceAssistantVisualizer } from './voice-assistant-visualizer.js';
import { VoiceAssistantUI } from './voice-assistant-ui.js';
import { VoiceAssistantIntegration } from './voice-assistant-integration.js';

/**
 * Main Voice Assistant Controller
 * This class orchestrates the UI, visualization, and integration components
 */
class VoiceAssistant {
  constructor() {
    // Core components
    this.ui = null;
    this.visualizer = null;
    this.integration = null;
    
    // State
    this.isInitialized = false;
    this.container = null;
    this.shopDomain = '';
    this.recordingTimeout = null;
    
    // Tool execution state
    this.isExecutingTools = false;
    
    // Bind methods to ensure proper 'this' context
    this.init = this.init.bind(this);
    this.startListening = this.startListening.bind(this);
    this.stopListening = this.stopListening.bind(this);
    this.destroy = this.destroy.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleError = this.handleError.bind(this);
  }
  
  /**
   * Initialize the Voice Assistant components
   */
  init() {
    // Prevent double initialization
    if (this.isInitialized) {
      console.warn('[VA] Already initialized. Skipping init.');
      return;
    }
    
    console.log('[VA] Initializing Enhanced Voice Assistant with tool calling...');
    
    // Get main container
    this.container = document.getElementById('voice-assistant');
    if (!this.container) {
      console.error('[VA] Container element not found. Cannot initialize.');
      return;
    }
    
    // Get shop domain from data attribute
    this.shopDomain = this.container.dataset.shopDomain;
    if (!this.shopDomain) {
      console.warn('[VA] Shop domain not provided in data attribute.');
    }
    
    // Initialize UI component
    this.ui = new VoiceAssistantUI();
    const uiInitialized = this.ui.init({
      container: this.container,
      onRecordStart: this.startListening,
      onRecordStop: this.stopListening
    });
    
    if (!uiInitialized) {
      console.error('[VA] Failed to initialize UI component.');
      return;
    }
    
    // Initialize visualizer component with canvas
    const canvas = document.getElementById('voice-assistant-canvas');
    this.visualizer = new VoiceAssistantVisualizer(canvas);
    this.visualizer.init();
    
    // Initialize integration with a direct, static import
    this.initializeIntegration();
    
    // Set up event listeners for tool execution
    this.setupEventListeners();
    
    // Wire up the floating mic button to the main controller
    this.setupFloatingMicListener();
    
    // Mark as initialized
    this.isInitialized = true;
    console.log('[VA] Enhanced initialization complete with tool calling support.');
  }
  
  /**
   * Initialize the integration with a direct, static import
   */
  initializeIntegration() {
    try {
      this.integration = new VoiceAssistantIntegration(this.shopDomain, this.ui);
      this.setupVisualizationBridge();
    } catch (error) {
      console.error('[VA] Error initializing integration:', error);
      this.handleError('Could not initialize voice service. Please try refreshing the page.');
    }
  }
  
  /**
   * Set up bridge between integration audio data and visualizer
   */
  setupVisualizationBridge() {
    if (!this.integration || !this.visualizer) return;
    
    // Set up callback to receive frequency data from integration
    this.integration.setVisualizerDataCallback(data => {
      this.visualizer.updateWithFrequencyData(data);
    });
  }
  
  /**
   * Set up event listeners for integration events
   */
  setupEventListeners() {
    // Listen for messages from the integration
    document.addEventListener('voice-assistant-message', this.handleMessage);
    
    // Listen for errors from the integration
    document.addEventListener('voice-assistant-error', this.handleError);
    
    // Listen for status updates
    document.addEventListener('voice-assistant-status', (event) => {
      console.log('[VA] Status update:', event.detail);
      // You can add UI updates here based on status
    });
  }
  
  /**
   * Handle messages from the integration
   */
  handleMessage(event) {
    const { message, type, timestamp } = event.detail;
    console.log('[VA] Received message:', type, message);
    
    // Update UI with the message
    if (this.ui) {
      this.ui.addMessage(message, type);
    }
    
    // Update chat bubble if it's an assistant message
    if (type === 'assistant' || type === 'system') {
      const chatMessage = document.getElementById('voice-assistant-chat-message');
      if (chatMessage) {
        chatMessage.textContent = message;
      }
    }
  }
  
  /**
   * Handle errors from the integration
   */
  handleError(event) {
    let errorMessage = 'An error occurred';
    
    if (event && event.detail) {
      errorMessage = event.detail.message || event.detail;
    } else if (typeof event === 'string') {
      errorMessage = event;
    }
    
    console.error('[VA] Error:', errorMessage);
    
    // Update UI with error
    if (this.ui) {
      this.ui.handleError(errorMessage);
    }
    
    // Reset any active states
    this.visualizer?.setListeningState(false);
    this.isExecutingTools = false;
  }
  
  /**
   * Wires up the floating mic button to the main start/stop listening methods.
   */
  setupFloatingMicListener() {
    const floatingMic = this.container.querySelector('.voice-assistant-floating-mic');
    if (!floatingMic) {
      console.warn('[VA] Floating mic button not found, cannot attach listener.');
      return;
    }
    
    const start = (e) => {
      e.preventDefault();
      this.startListening();
    };
    
    const stop = (e) => {
      e.preventDefault();
      this.stopListening();
    };
    
    // Use mouse and touch events for broad compatibility
    floatingMic.addEventListener('mousedown', start);
    floatingMic.addEventListener('mouseup', stop);
    floatingMic.addEventListener('mouseleave', stop);
    
    floatingMic.addEventListener('touchstart', start, { passive: false });
    floatingMic.addEventListener('touchend', stop);

    console.log('[VA] Floating mic listener attached successfully.');
  }
  
  /**
   * Start listening for voice input
   */
  async startListening() {
    try {
      console.log('[VA] Starting voice listening with tool support...');
      
      // Show listening state in visualizer
      this.visualizer.setListeningState(true);
      
      // Update UI state
      if (this.ui) {
        this.ui.setListeningState(true);
      }
      
      // Update chat message
      const chatMessage = document.getElementById('voice-assistant-chat-message');
      if (chatMessage) {
        chatMessage.textContent = 'Listening...';
      }
      
      // Start audio through the integration
      const success = await this.integration.startListening();
      
      if (!success) {
        throw new Error('Failed to start audio capture');
      }
      
      // Set a timeout to automatically stop listening after 10 seconds
      this.recordingTimeout = setTimeout(() => {
        console.log('[VA] Recording timeout reached (10s).');
        if (this.ui && this.ui.isListening) { // Check state before stopping
          this.stopListening();
        }
      }, 10000);
      
    } catch (error) {
      console.error('[VA] Error starting voice listening:', error);
      this.handleError('Could not access microphone. Please check your permissions.');
      this.visualizer.setListeningState(false);
      
      if (this.ui) {
        this.ui.setListeningState(false);
      }
    }
  }
  
  /**
   * Stop listening for voice input
   */
  stopListening() {
    console.log('[VA] Stopping voice listening...');
    
    // Clear the automatic stop timeout if it exists
    if (this.recordingTimeout) {
      clearTimeout(this.recordingTimeout);
      this.recordingTimeout = null;
    }
    
    // Update visualizer state
    this.visualizer.setListeningState(false);
    
    // Update UI state
    if (this.ui) {
      this.ui.setListeningState(false);
    }
    
    // Update chat message
    const chatMessage = document.getElementById('voice-assistant-chat-message');
    if (chatMessage) {
      chatMessage.textContent = 'Processing...';
    }
    
    // Show loading state
    const loadingIndicator = document.getElementById('voice-assistant-loading');
    if (loadingIndicator) {
      loadingIndicator.classList.remove('hidden');
    }
    
    // Stop the integration
    if (this.integration) {
      this.integration.stopListening();
    } else {
      console.warn('[VA] Integration not available for stopListening.');
    }
    
    // Hide loading state after a delay (the integration will handle the actual response)
    setTimeout(() => {
      if (loadingIndicator) {
        loadingIndicator.classList.add('hidden');
      }
      if (chatMessage && chatMessage.textContent === 'Processing...') {
        chatMessage.textContent = 'How can I help you shop today?';
      }
    }, 5000);
  }
  
  /**
   * Clean up resources
   */
  destroy() {
    console.log('[VA] Destroying Voice Assistant...');
    
    // Clear any active timeouts
    if (this.recordingTimeout) {
      clearTimeout(this.recordingTimeout);
      this.recordingTimeout = null;
    }
    
    // Remove event listeners
    document.removeEventListener('voice-assistant-message', this.handleMessage);
    document.removeEventListener('voice-assistant-error', this.handleError);
    
    // Destroy components
    if (this.ui) {
      this.ui.destroy();
      this.ui = null;
    }
    
    if (this.visualizer) {
      this.visualizer.destroy();
      this.visualizer = null;
    }
    
    // Stop the integration if active
    if (this.integration) {
      this.integration.stopListening();
      this.integration = null;
    }
    
    this.isInitialized = false;
    console.log('[VA] Voice assistant resources cleaned up.');
  }
}

// Initialize the Voice Assistant when the DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  // Add a global error handler to identify where errors are coming from
  window.addEventListener('error', (event) => {
    console.error('Global error caught:', event);
    if (event.message && event.message.includes('language-not-supported')) {
      console.error('Speech recognition error source:', event.filename, 'Line:', event.lineno);
      // Prevent the error from propagating
      event.preventDefault();
      return true;
    }
  });
  
  // Create and initialize the Voice Assistant
  const voiceAssistant = new VoiceAssistant();
  
  // Initialize the voice assistant only if not already done
  if (!window.voiceAssistantInitialized) {
    voiceAssistant.init();
    window.voiceAssistantInitialized = true;
    
    // Store instance for potential external access
    window.voiceAssistant = voiceAssistant;
  } else {
    console.warn('[VA] Attempted to initialize Voice Assistant again. Skipping.');
  }
  
  // Handle page unload to clean up resources
  window.addEventListener('beforeunload', () => {
    if (voiceAssistant.isInitialized) {
      voiceAssistant.destroy();
    }
  });
});