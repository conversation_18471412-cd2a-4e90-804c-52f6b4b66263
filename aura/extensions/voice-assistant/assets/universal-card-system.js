/**
 * Universal Card System - Renders all types of voice assistant response cards
 * Handles product, cart, order, shipping, FAQ, recommendation, navigation, and status cards
 */

export class UniversalCardSystem {
  constructor(container, glassUtils) {
    this.container = container;
    this.glassUtils = glassUtils;
    this.renderers = new Map();
    this.registerDefaultRenderers();
  }

  registerDefaultRenderers() {
    this.renderers.set('product', ProductCardRenderer);
    this.renderers.set('cart', CartCardRenderer);
    this.renderers.set('order', OrderCardRenderer);
    this.renderers.set('shipping', ShippingCardRenderer);
    this.renderers.set('faq', FAQCardRenderer);
    this.renderers.set('recommendation', RecommendationCardRenderer);
    this.renderers.set('navigation', NavigationCardRenderer);
    this.renderers.set('status', StatusCardRenderer);
  }

  renderCards(cards) {
    if (!Array.isArray(cards) || cards.length === 0) {
      return this.createEmptyContainer();
    }

    // Sort by priority
    const sortedCards = [...cards].sort((a, b) => (a.priority || 0) - (b.priority || 0));
    
    const cardsContainer = this.createCardsContainer();
    
    sortedCards.forEach(cardData => {
      try {
        const renderer = this.renderers.get(cardData.type);
        if (renderer) {
          const cardElement = new renderer(cardData, this.glassUtils).render();
          cardsContainer.appendChild(cardElement);
        } else {
          console.warn('[UniversalCardSystem] No renderer found for card type:', cardData.type);
          // Fallback to generic card renderer
          const fallbackCard = new GenericCardRenderer(cardData, this.glassUtils).render();
          cardsContainer.appendChild(fallbackCard);
        }
      } catch (error) {
        console.error('[UniversalCardSystem] Error rendering card:', cardData.type, error);
      }
    });
    
    return cardsContainer;
  }

  createCardsContainer() {
    const container = document.createElement('div');
    container.className = 'chat-bubble-cards';
    return container;
  }

  createEmptyContainer() {
    const container = document.createElement('div');
    container.className = 'chat-bubble-cards empty';
    return container;
  }
}

/**
 * Base Card Renderer - Provides common functionality for all card types
 */
class BaseCardRenderer {
  constructor(cardData, glassUtils) {
    this.cardData = cardData;
    this.glassUtils = glassUtils;
  }

  createCardWrapper() {
    const wrapper = document.createElement('div');
    wrapper.className = `voice-card voice-card--${this.cardData.type}`;
    
    // Add layout classes
    if (this.cardData.layout) {
      wrapper.classList.add(`voice-card--layout-${this.cardData.layout}`);
    }

    // Apply glass morphism effect
    if (this.glassUtils) {
      this.glassUtils.applyGlassMorphism(wrapper, {
        blur: 20,
        transparency: 0.1,
        borderRadius: 12,
        borderWidth: 1
      });
    }

    return wrapper;
  }

  createHeader() {
    if (!this.cardData.title && !this.cardData.subtitle) {
      return null;
    }

    const header = document.createElement('div');
    header.className = 'voice-card__header';

    if (this.cardData.title) {
      const title = document.createElement('h3');
      title.className = 'voice-card__title';
      title.textContent = this.cardData.title;
      header.appendChild(title);
    }

    if (this.cardData.subtitle) {
      const subtitle = document.createElement('p');
      subtitle.className = 'voice-card__subtitle';
      subtitle.textContent = this.cardData.subtitle;
      header.appendChild(subtitle);
    }

    return header;
  }

  createActions() {
    if (!this.cardData.actions || this.cardData.actions.length === 0) {
      return null;
    }

    const actionsContainer = document.createElement('div');
    actionsContainer.className = 'voice-card__actions';

    this.cardData.actions.forEach(action => {
      const actionElement = this.createActionElement(action);
      if (actionElement) {
        actionsContainer.appendChild(actionElement);
      }
    });

    return actionsContainer;
  }

  createActionElement(action) {
    switch (action.type) {
      case 'button':
        return this.createButton(action);
      case 'link':
        return this.createLink(action);
      case 'toggle':
        return this.createToggle(action);
      case 'input':
        return this.createInput(action);
      default:
        console.warn('[BaseCardRenderer] Unknown action type:', action.type);
        return null;
    }
  }

  createButton(action) {
    const button = document.createElement('button');
    button.className = `voice-card__button voice-card__button--${action.style || 'secondary'}`;
    button.textContent = action.label;
    
    button.addEventListener('click', () => {
      this.handleAction(action);
    });

    // Apply glass effect to buttons
    if (this.glassUtils) {
      this.glassUtils.applyGlassMorphism(button, {
        blur: 10,
        transparency: 0.2,
        borderRadius: 8
      });
    }

    return button;
  }

  createLink(action) {
    const link = document.createElement('a');
    link.className = 'voice-card__link';
    link.textContent = action.label;
    link.href = action.action;
    
    if (action.action.startsWith('http')) {
      link.target = '_blank';
      link.rel = 'noopener noreferrer';
    }

    return link;
  }

  createToggle(action) {
    const wrapper = document.createElement('div');
    wrapper.className = 'voice-card__toggle-wrapper';

    const toggle = document.createElement('input');
    toggle.type = 'checkbox';
    toggle.className = 'voice-card__toggle';
    toggle.id = `toggle-${Math.random().toString(36).substr(2, 9)}`;

    const label = document.createElement('label');
    label.htmlFor = toggle.id;
    label.textContent = action.label;
    label.className = 'voice-card__toggle-label';

    toggle.addEventListener('change', () => {
      this.handleAction({
        ...action,
        data: { ...action.data, checked: toggle.checked }
      });
    });

    wrapper.appendChild(toggle);
    wrapper.appendChild(label);
    return wrapper;
  }

  createInput(action) {
    const wrapper = document.createElement('div');
    wrapper.className = 'voice-card__input-wrapper';

    const input = document.createElement('input');
    input.type = 'text';
    input.className = 'voice-card__input';
    input.placeholder = action.label;

    const submitBtn = document.createElement('button');
    submitBtn.className = 'voice-card__input-submit';
    submitBtn.textContent = 'Submit';

    const handleSubmit = () => {
      if (input.value.trim()) {
        this.handleAction({
          ...action,
          data: { ...action.data, value: input.value.trim() }
        });
        input.value = '';
      }
    };

    submitBtn.addEventListener('click', handleSubmit);
    input.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        handleSubmit();
      }
    });

    wrapper.appendChild(input);
    wrapper.appendChild(submitBtn);
    return wrapper;
  }

  handleAction(action) {
    console.log('[BaseCardRenderer] Action triggered:', action);

    // Emit custom event for action handling
    const event = new CustomEvent('voiceCardAction', {
      detail: {
        action: action.action,
        data: action.data,
        cardType: this.cardData.type
      },
      bubbles: true
    });

    document.dispatchEvent(event);
  }

  render() {
    // To be implemented by subclasses
    throw new Error('render() method must be implemented by subclass');
  }
}

/**
 * Product Card Renderer - Displays product information with images, prices, and actions
 */
class ProductCardRenderer extends BaseCardRenderer {
  render() {
    const wrapper = this.createCardWrapper();
    const header = this.createHeader();
    
    if (header) {
      wrapper.appendChild(header);
    }

    const content = this.createProductContent();
    wrapper.appendChild(content);

    const actions = this.createActions();
    if (actions) {
      wrapper.appendChild(actions);
    }

    return wrapper;
  }

  createProductContent() {
    const content = document.createElement('div');
    content.className = 'voice-card__content voice-card__products';

    const products = this.cardData.data.products || [];
    const maxItems = this.cardData.data.maxItems || 3;
    const showAddToCart = this.cardData.data.showAddToCart !== false;

    products.slice(0, maxItems).forEach(product => {
      const productElement = this.createProductElement(product, showAddToCart);
      content.appendChild(productElement);
    });

    return content;
  }

  createProductElement(product, showAddToCart) {
    const productDiv = document.createElement('div');
    productDiv.className = 'voice-card__product';

    // Product image
    if (product.image) {
      const img = document.createElement('img');
      img.src = product.image;
      img.alt = product.title;
      img.className = 'voice-card__product-image';
      img.loading = 'lazy';
      productDiv.appendChild(img);
    }

    // Product info
    const info = document.createElement('div');
    info.className = 'voice-card__product-info';

    const title = document.createElement('h4');
    title.className = 'voice-card__product-title';
    title.textContent = product.title;
    info.appendChild(title);

    // Price information
    const priceContainer = document.createElement('div');
    priceContainer.className = 'voice-card__product-price';

    if (product.compareAtPrice && product.price !== product.compareAtPrice) {
      const originalPrice = document.createElement('span');
      originalPrice.className = 'voice-card__product-price--original';
      originalPrice.textContent = product.compareAtPrice;
      priceContainer.appendChild(originalPrice);
    }

    const currentPrice = document.createElement('span');
    currentPrice.className = 'voice-card__product-price--current';
    currentPrice.textContent = product.price;
    priceContainer.appendChild(currentPrice);

    info.appendChild(priceContainer);

    // Rating and reviews
    if (product.rating || product.reviewCount) {
      const reviewInfo = document.createElement('div');
      reviewInfo.className = 'voice-card__product-reviews';

      if (product.rating) {
        const rating = document.createElement('span');
        rating.className = 'voice-card__product-rating';
        rating.textContent = `★${product.rating}`;
        reviewInfo.appendChild(rating);
      }

      if (product.reviewCount) {
        const reviewCount = document.createElement('span');
        reviewCount.className = 'voice-card__product-review-count';
        reviewCount.textContent = `(${product.reviewCount} reviews)`;
        reviewInfo.appendChild(reviewCount);
      }

      info.appendChild(reviewInfo);
    }

    // Badges
    if (product.badges && product.badges.length > 0) {
      const badges = document.createElement('div');
      badges.className = 'voice-card__product-badges';

      product.badges.forEach(badge => {
        const badgeSpan = document.createElement('span');
        badgeSpan.className = `voice-card__product-badge voice-card__product-badge--${badge.toLowerCase()}`;
        badgeSpan.textContent = badge;
        badges.appendChild(badgeSpan);
      });

      info.appendChild(badges);
    }

    productDiv.appendChild(info);

    // Add to cart button
    if (showAddToCart) {
      const addToCartBtn = document.createElement('button');
      addToCartBtn.className = 'voice-card__add-to-cart-btn';
      addToCartBtn.textContent = 'Add to Cart';
      
      addToCartBtn.addEventListener('click', () => {
        this.handleAction({
          action: 'addToCart',
          data: { productId: product.id, variantId: product.variants?.[0]?.id }
        });
      });

      if (this.glassUtils) {
        this.glassUtils.applyGlassMorphism(addToCartBtn, {
          blur: 10,
          transparency: 0.2,
          borderRadius: 6
        });
      }

      productDiv.appendChild(addToCartBtn);
    }

    return productDiv;
  }
}

/**
 * Cart Card Renderer - Displays cart contents and totals
 */
class CartCardRenderer extends BaseCardRenderer {
  render() {
    const wrapper = this.createCardWrapper();
    const header = this.createHeader();
    
    if (header) {
      wrapper.appendChild(header);
    }

    const content = this.createCartContent();
    wrapper.appendChild(content);

    const actions = this.createActions();
    if (actions) {
      wrapper.appendChild(actions);
    }

    return wrapper;
  }

  createCartContent() {
    const content = document.createElement('div');
    content.className = 'voice-card__content voice-card__cart';

    const cartData = this.cardData.data;
    const items = cartData.items || [];

    // Cart items
    if (items.length > 0) {
      const itemsList = document.createElement('div');
      itemsList.className = 'voice-card__cart-items';

      items.forEach(item => {
        const itemElement = this.createCartItemElement(item);
        itemsList.appendChild(itemElement);
      });

      content.appendChild(itemsList);
    }

    // Cart totals
    const totals = this.createCartTotals(cartData);
    content.appendChild(totals);

    return content;
  }

  createCartItemElement(item) {
    const itemDiv = document.createElement('div');
    itemDiv.className = 'voice-card__cart-item';

    if (item.image) {
      const img = document.createElement('img');
      img.src = item.image;
      img.alt = item.title;
      img.className = 'voice-card__cart-item-image';
      img.loading = 'lazy';
      itemDiv.appendChild(img);
    }

    const info = document.createElement('div');
    info.className = 'voice-card__cart-item-info';

    const title = document.createElement('div');
    title.className = 'voice-card__cart-item-title';
    title.textContent = item.title;
    info.appendChild(title);

    const details = document.createElement('div');
    details.className = 'voice-card__cart-item-details';
    details.innerHTML = `Qty: ${item.quantity} × ${item.price} = ${item.lineTotal}`;
    info.appendChild(details);

    itemDiv.appendChild(info);

    return itemDiv;
  }

  createCartTotals(cartData) {
    const totals = document.createElement('div');
    totals.className = 'voice-card__cart-totals';

    if (cartData.subtotal) {
      this.addTotalLine(totals, 'Subtotal', cartData.subtotal);
    }

    if (cartData.discounts && cartData.discounts.length > 0) {
      cartData.discounts.forEach(discount => {
        this.addTotalLine(totals, discount.title, `-${discount.amount}`, 'discount');
      });
    }

    if (cartData.tax) {
      this.addTotalLine(totals, 'Tax', cartData.tax);
    }

    if (cartData.shipping) {
      this.addTotalLine(totals, 'Shipping', cartData.shipping);
    }

    this.addTotalLine(totals, 'Total', cartData.total, 'total');

    return totals;
  }

  addTotalLine(container, label, value, className = '') {
    const line = document.createElement('div');
    line.className = `voice-card__cart-total-line ${className}`;
    
    const labelSpan = document.createElement('span');
    labelSpan.textContent = label;
    
    const valueSpan = document.createElement('span');
    valueSpan.textContent = value;
    
    line.appendChild(labelSpan);
    line.appendChild(valueSpan);
    container.appendChild(line);
  }
}

/**
 * Order Card Renderer - Displays order history and tracking information
 */
class OrderCardRenderer extends BaseCardRenderer {
  render() {
    const wrapper = this.createCardWrapper();
    const header = this.createHeader();
    
    if (header) {
      wrapper.appendChild(header);
    }

    const content = this.createOrderContent();
    wrapper.appendChild(content);

    const actions = this.createActions();
    if (actions) {
      wrapper.appendChild(actions);
    }

    return wrapper;
  }

  createOrderContent() {
    const content = document.createElement('div');
    content.className = 'voice-card__content voice-card__orders';

    const orders = this.cardData.data.orders || [];

    orders.forEach(order => {
      const orderElement = this.createOrderElement(order);
      content.appendChild(orderElement);
    });

    return content;
  }

  createOrderElement(order) {
    const orderDiv = document.createElement('div');
    orderDiv.className = 'voice-card__order';

    // Order header
    const header = document.createElement('div');
    header.className = 'voice-card__order-header';

    const orderNumber = document.createElement('div');
    orderNumber.className = 'voice-card__order-number';
    orderNumber.textContent = `Order #${order.orderNumber}`;
    header.appendChild(orderNumber);

    const orderDate = document.createElement('div');
    orderDate.className = 'voice-card__order-date';
    orderDate.textContent = new Date(order.date).toLocaleDateString();
    header.appendChild(orderDate);

    orderDiv.appendChild(header);

    // Order status
    const status = document.createElement('div');
    status.className = `voice-card__order-status voice-card__order-status--${order.status}`;
    status.textContent = order.status.charAt(0).toUpperCase() + order.status.slice(1);
    orderDiv.appendChild(status);

    // Order items preview
    if (order.items && order.items.length > 0) {
      const itemsPreview = document.createElement('div');
      itemsPreview.className = 'voice-card__order-items-preview';
      
      const itemText = order.items.length === 1 
        ? `${order.items[0].title} (${order.items[0].quantity})`
        : `${order.items.length} items`;
      
      itemsPreview.textContent = itemText;
      orderDiv.appendChild(itemsPreview);
    }

    // Order total
    const total = document.createElement('div');
    total.className = 'voice-card__order-total';
    total.textContent = order.total;
    orderDiv.appendChild(total);

    // Tracking information
    if (order.trackingNumber) {
      const tracking = document.createElement('div');
      tracking.className = 'voice-card__order-tracking';
      tracking.textContent = `Tracking: ${order.trackingNumber}`;
      orderDiv.appendChild(tracking);
    }

    return orderDiv;
  }
}

/**
 * FAQ Card Renderer - Displays frequently asked questions and answers
 */
class FAQCardRenderer extends BaseCardRenderer {
  render() {
    const wrapper = this.createCardWrapper();
    const header = this.createHeader();
    
    if (header) {
      wrapper.appendChild(header);
    }

    const content = this.createFAQContent();
    wrapper.appendChild(content);

    const actions = this.createActions();
    if (actions) {
      wrapper.appendChild(actions);
    }

    return wrapper;
  }

  createFAQContent() {
    const content = document.createElement('div');
    content.className = 'voice-card__content voice-card__faq';

    const questions = this.cardData.data.questions || [];

    questions.forEach(faq => {
      const faqElement = this.createFAQElement(faq);
      content.appendChild(faqElement);
    });

    return content;
  }

  createFAQElement(faq) {
    const faqDiv = document.createElement('div');
    faqDiv.className = 'voice-card__faq-item';

    const question = document.createElement('button');
    question.className = 'voice-card__faq-question';
    question.textContent = faq.question;

    const answer = document.createElement('div');
    answer.className = 'voice-card__faq-answer';
    answer.textContent = faq.answer;
    answer.style.display = 'none';

    question.addEventListener('click', () => {
      const isOpen = answer.style.display !== 'none';
      answer.style.display = isOpen ? 'none' : 'block';
      question.classList.toggle('active', !isOpen);
    });

    faqDiv.appendChild(question);
    faqDiv.appendChild(answer);

    return faqDiv;
  }
}

/**
 * Status Card Renderer - Displays status messages, notifications, and system updates
 */
class StatusCardRenderer extends BaseCardRenderer {
  render() {
    const wrapper = this.createCardWrapper();
    const header = this.createHeader();
    
    if (header) {
      wrapper.appendChild(header);
    }

    const content = this.createStatusContent();
    wrapper.appendChild(content);

    const actions = this.createActions();
    if (actions) {
      wrapper.appendChild(actions);
    }

    return wrapper;
  }

  createStatusContent() {
    const content = document.createElement('div');
    content.className = 'voice-card__content voice-card__status';

    const statusData = this.cardData.data;
    const statusType = statusData.status || 'info';

    content.classList.add(`voice-card__status--${statusType}`);

    const message = document.createElement('div');
    message.className = 'voice-card__status-message';
    message.textContent = statusData.message || 'Status update';

    content.appendChild(message);

    return content;
  }
}

/**
 * Generic Card Renderer - Fallback for unknown card types
 */
class GenericCardRenderer extends BaseCardRenderer {
  render() {
    const wrapper = this.createCardWrapper();
    const header = this.createHeader();
    
    if (header) {
      wrapper.appendChild(header);
    }

    const content = document.createElement('div');
    content.className = 'voice-card__content voice-card__generic';
    content.textContent = JSON.stringify(this.cardData.data, null, 2);

    wrapper.appendChild(content);

    const actions = this.createActions();
    if (actions) {
      wrapper.appendChild(actions);
    }

    return wrapper;
  }
}

// Alias renderers for missing types
class RecommendationCardRenderer extends ProductCardRenderer {}
class ShippingCardRenderer extends StatusCardRenderer {}
class NavigationCardRenderer extends StatusCardRenderer {}
