/**
 * Liquid Glass Utils
 * 
 * A comprehensive utility system for creating liquid glass effects in web applications.
 * Based on the liquid-glass-react library patterns but implemented in vanilla JavaScript.
 */

/**
 * Core liquid glass utilities for creating glass effects
 */
export class LiquidGlassUtils {
  
  /**
   * Generate shader displacement map for glass distortion effect
   * @param {number} width - Width of the displacement map
   * @param {number} height - Height of the displacement map
   * @param {number} intensity - Intensity of the displacement (0-1)
   * @returns {ImageData} Generated displacement map
   */
  static generateShaderDisplacementMap(width = 256, height = 256, intensity = 0.5) {
    const canvas = document.createElement('canvas');
    canvas.width = width;
    canvas.height = height;
    const ctx = canvas.getContext('2d');
    
    const imageData = ctx.createImageData(width, height);
    const data = imageData.data;
    
    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * 4;
        
        // Generate smooth noise for displacement
        const noiseX = Math.sin(x * 0.02) * Math.cos(y * 0.02) * intensity;
        const noiseY = Math.cos(x * 0.02) * Math.sin(y * 0.02) * intensity;
        
        // Convert to 0-255 range
        data[index] = (noiseX + 1) * 127.5;     // Red channel - X displacement
        data[index + 1] = (noiseY + 1) * 127.5; // Green channel - Y displacement
        data[index + 2] = 128;                   // Blue channel - unused
        data[index + 3] = 255;                   // Alpha channel
      }
    }
    
    return imageData;
  }
  
  /**
   * Create SVG filter for glass chromatic aberration effect
   * @param {string} filterId - Unique ID for the filter
   * @param {number} intensity - Intensity of chromatic aberration (0-5)
   * @returns {SVGFilterElement} Created SVG filter
   */
  static createGlassFilter(filterId, intensity = 2) {
    // Remove existing filter if it exists
    const existingFilter = document.getElementById(filterId);
    if (existingFilter) {
      existingFilter.remove();
    }
    
    // Create SVG namespace
    const svgNS = 'http://www.w3.org/2000/svg';
    
    // Create SVG element
    let svg = document.getElementById('liquid-glass-filters');
    if (!svg) {
      svg = document.createElementNS(svgNS, 'svg');
      svg.id = 'liquid-glass-filters';
      svg.style.position = 'absolute';
      svg.style.width = '0';
      svg.style.height = '0';
      svg.style.pointerEvents = 'none';
      document.body.appendChild(svg);
    }
    
    // Create filter
    const filter = document.createElementNS(svgNS, 'filter');
    filter.id = filterId;
    filter.setAttribute('x', '-50%');
    filter.setAttribute('y', '-50%');
    filter.setAttribute('width', '200%');
    filter.setAttribute('height', '200%');
    
    // Create chromatic aberration effect
    const feOffset1 = document.createElementNS(svgNS, 'feOffset');
    feOffset1.setAttribute('in', 'SourceGraphic');
    feOffset1.setAttribute('dx', intensity.toString());
    feOffset1.setAttribute('dy', '0');
    feOffset1.setAttribute('result', 'red');
    
    const feOffset2 = document.createElementNS(svgNS, 'feOffset');
    feOffset2.setAttribute('in', 'SourceGraphic');
    feOffset2.setAttribute('dx', (-intensity).toString());
    feOffset2.setAttribute('dy', '0');
    feOffset2.setAttribute('result', 'blue');
    
    const feColorMatrix1 = document.createElementNS(svgNS, 'feColorMatrix');
    feColorMatrix1.setAttribute('in', 'red');
    feColorMatrix1.setAttribute('type', 'matrix');
    feColorMatrix1.setAttribute('values', '1 0 0 0 0  0 0 0 0 0  0 0 0 0 0  0 0 0 1 0');
    feColorMatrix1.setAttribute('result', 'redChannel');
    
    const feColorMatrix2 = document.createElementNS(svgNS, 'feColorMatrix');
    feColorMatrix2.setAttribute('in', 'blue');
    feColorMatrix2.setAttribute('type', 'matrix');
    feColorMatrix2.setAttribute('values', '0 0 0 0 0  0 0 0 0 0  0 0 1 0 0  0 0 0 1 0');
    feColorMatrix2.setAttribute('result', 'blueChannel');
    
    const feColorMatrix3 = document.createElementNS(svgNS, 'feColorMatrix');
    feColorMatrix3.setAttribute('in', 'SourceGraphic');
    feColorMatrix3.setAttribute('type', 'matrix');
    feColorMatrix3.setAttribute('values', '0 0 0 0 0  0 1 0 0 0  0 0 0 0 0  0 0 0 1 0');
    feColorMatrix3.setAttribute('result', 'greenChannel');
    
    const feComposite = document.createElementNS(svgNS, 'feComposite');
    feComposite.setAttribute('in', 'redChannel');
    feComposite.setAttribute('in2', 'greenChannel');
    feComposite.setAttribute('operator', 'arithmetic');
    feComposite.setAttribute('k1', '0');
    feComposite.setAttribute('k2', '1');
    feComposite.setAttribute('k3', '1');
    feComposite.setAttribute('k4', '0');
    feComposite.setAttribute('result', 'redGreen');
    
    const feComposite2 = document.createElementNS(svgNS, 'feComposite');
    feComposite2.setAttribute('in', 'redGreen');
    feComposite2.setAttribute('in2', 'blueChannel');
    feComposite2.setAttribute('operator', 'arithmetic');
    feComposite2.setAttribute('k1', '0');
    feComposite2.setAttribute('k2', '1');
    feComposite2.setAttribute('k3', '1');
    feComposite2.setAttribute('k4', '0');
    
    // Add elements to filter
    filter.appendChild(feOffset1);
    filter.appendChild(feOffset2);
    filter.appendChild(feColorMatrix1);
    filter.appendChild(feColorMatrix2);
    filter.appendChild(feColorMatrix3);
    filter.appendChild(feComposite);
    filter.appendChild(feComposite2);
    
    svg.appendChild(filter);
    
    return filter;
  }
  
  /**
   * Apply glass effect to an element
   * @param {HTMLElement} element - Target element
   * @param {Object} options - Glass effect options
   * @returns {Object} Glass effect controller
   */
  static applyGlassEffect(element, options = {}) {
    const {
      blurAmount = 0.1,
      saturation = 120,
      brightness = 110,
      opacity = 0.8,
      cornerRadius = 12,
      borderOpacity = 0.2,
      shadowOpacity = 0.3
    } = options;
    
    const styles = {
      backdropFilter: `blur(${blurAmount * 100}px) saturate(${saturation}%) brightness(${brightness}%)`,
      WebkitBackdropFilter: `blur(${blurAmount * 100}px) saturate(${saturation}%) brightness(${brightness}%)`,
      background: `rgba(255, 255, 255, ${opacity * 0.4})`,
      border: `1px solid rgba(255, 255, 255, ${borderOpacity})`,
      borderRadius: `${cornerRadius}px`,
      boxShadow: `
        0 0 0 1px rgba(255, 255, 255, ${borderOpacity * 0.5}) inset,
        0 8px 32px rgba(0, 0, 0, ${shadowOpacity * 0.2}),
        0 4px 16px rgba(0, 0, 0, ${shadowOpacity * 0.1})
      `
    };
    
    // Store original styles
    const originalStyles = {};
    Object.keys(styles).forEach(key => {
      originalStyles[key] = element.style[key] || '';
    });
    
    // Apply glass styles
    Object.assign(element.style, styles);
    
    return {
      element,
      destroy: () => {
        Object.assign(element.style, originalStyles);
      }
    };
  }
  
  /**
   * Add glass borders to an element
   * @param {HTMLElement} element - Target element
   * @param {Object} options - Border options
   * @returns {Object} Border effect controller
   */
  static addGlassBorders(element, options = {}) {
    const {
      borderWidth = 1,
      borderOpacity = 0.2,
      cornerRadius = 12,
      gradientStops = ['rgba(255,255,255,0.3)', 'rgba(255,255,255,0.1)']
    } = options;
    
    const borderStyle = {
      border: `${borderWidth}px solid transparent`,
      borderRadius: `${cornerRadius}px`,
      background: `linear-gradient(135deg, ${gradientStops.join(', ')}) border-box`,
      WebkitMask: 'linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)',
      WebkitMaskComposite: 'xor',
      maskComposite: 'exclude'
    };
    
    const originalStyles = {};
    Object.keys(borderStyle).forEach(key => {
      originalStyles[key] = element.style[key] || '';
    });
    
    Object.assign(element.style, borderStyle);
    
    return {
      element,
      destroy: () => {
        Object.assign(element.style, originalStyles);
      }
    };
  }
  
  /**
   * Create a glass component with mouse tracking
   * @param {HTMLElement} element - Target element
   * @param {Object} options - Glass component options
   * @returns {Object} Glass component controller
   */
  static createGlassComponent(element, options = {}) {
    const {
      cornerRadius = 12,
      blurAmount = 0.1,
      saturation = 120,
      overLight = false,
      enableMouseTracking = true
    } = options;
    
    const baseOpacity = overLight ? 0.3 : 0.8;
    const borderOpacity = overLight ? 0.3 : 0.2;
    
    // Apply base glass effect
    const glassEffect = LiquidGlassUtils.applyGlassEffect(element, {
      blurAmount,
      saturation,
      opacity: baseOpacity,
      cornerRadius,
      borderOpacity
    });
    
    let mouseTracker = null;
    
    if (enableMouseTracking) {
      mouseTracker = LiquidGlassUtils.addMouseTracking(element, {
        intensity: 0.3,
        cornerRadius
      });
    }
    
    return {
      element,
      glassEffect,
      mouseTracker,
      destroy: () => {
        glassEffect.destroy();
        if (mouseTracker) {
          mouseTracker.destroy();
        }
      }
    };
  }
  
  /**
   * Add mouse tracking effect to an element
   * @param {HTMLElement} element - Target element
   * @param {Object} options - Mouse tracking options
   * @returns {Object} Mouse tracking controller
   */
  static addMouseTracking(element, options = {}) {
    const {
      intensity = 0.3,
      cornerRadius = 12
    } = options;
    
    let isHovering = false;
    
    const handleMouseMove = (e) => {
      if (!isHovering) return;
      
      const rect = element.getBoundingClientRect();
      const x = ((e.clientX - rect.left) / rect.width - 0.5) * 2;
      const y = ((e.clientY - rect.top) / rect.height - 0.5) * 2;
      
      const transformX = x * intensity * 5;
      const transformY = y * intensity * 5;
      
      element.style.transform = `perspective(1000px) rotateY(${transformX}deg) rotateX(${-transformY}deg) scale(1.02)`;
      element.style.boxShadow = `
        0 0 0 1px rgba(255, 255, 255, 0.3) inset,
        ${x * 20}px ${y * 20}px 40px rgba(0, 0, 0, 0.2),
        0 8px 32px rgba(139, 92, 246, 0.3)
      `;
    };
    
    const handleMouseEnter = () => {
      isHovering = true;
      element.style.transition = 'transform 0.1s ease, box-shadow 0.1s ease';
    };
    
    const handleMouseLeave = () => {
      isHovering = false;
      element.style.transform = 'perspective(1000px) rotateY(0deg) rotateX(0deg) scale(1)';
      element.style.boxShadow = `
        0 0 0 1px rgba(255, 255, 255, 0.2) inset,
        0 8px 32px rgba(139, 92, 246, 0.2),
        0 4px 16px rgba(0, 0, 0, 0.1)
      `;
      element.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';
    };
    
    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    
    return {
      element,
      destroy: () => {
        element.removeEventListener('mousemove', handleMouseMove);
        element.removeEventListener('mouseenter', handleMouseEnter);
        element.removeEventListener('mouseleave', handleMouseLeave);
        element.style.transform = '';
        element.style.transition = '';
      }
    };
  }
}

/**
 * Pre-built glass UI components
 */
export class GlassUIComponents {
  
  /**
   * Create a glass modal component
   * @param {Object} options - Modal options
   * @returns {Object} Modal component with element and controller
   */
  static createModal(options = {}) {
    const {
      title = 'Modal Title',
      content = 'Modal content goes here',
      width = '400px',
      height = 'auto',
      overLight = false
    } = options;
    
    const modal = document.createElement('div');
    modal.className = 'glass-modal';
    modal.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: ${width};
      height: ${height};
      z-index: 1000;
      padding: 24px;
      box-sizing: border-box;
    `;
    
    const header = document.createElement('div');
    header.className = 'glass-modal-header';
    header.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      font-weight: 600;
      font-size: 18px;
    `;
    header.textContent = title;
    
    const closeButton = document.createElement('button');
    closeButton.className = 'glass-modal-close';
    closeButton.textContent = '×';
    closeButton.style.cssText = `
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      padding: 0;
      color: inherit;
      opacity: 0.7;
      transition: opacity 0.2s ease;
    `;
    closeButton.addEventListener('mouseenter', () => closeButton.style.opacity = '1');
    closeButton.addEventListener('mouseleave', () => closeButton.style.opacity = '0.7');
    
    const contentEl = document.createElement('div');
    contentEl.className = 'glass-modal-content';
    contentEl.textContent = content;
    
    header.appendChild(closeButton);
    modal.appendChild(header);
    modal.appendChild(contentEl);
    
    const glassEffect = LiquidGlassUtils.createGlassComponent(modal, {
      cornerRadius: 20,
      blurAmount: 0.1,
      overLight
    });
    
    return {
      modal,
      header,
      content: contentEl,
      closeButton,
      glassEffect,
      show: () => {
        document.body.appendChild(modal);
      },
      hide: () => {
        if (modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      },
      destroy: () => {
        glassEffect.destroy();
        if (modal.parentNode) {
          modal.parentNode.removeChild(modal);
        }
      }
    };
  }
  
  /**
   * Create a glass chat bubble
   * @param {Object} options - Chat bubble options
   * @returns {Object} Chat bubble component
   */
  static createChatBubble(options = {}) {
    const {
      message = 'Hello!',
      sender = 'user',
      overLight = false,
      products = null,
      showActions = true
    } = options;
    
    const bubble = document.createElement('div');
    bubble.className = `glass-chat-bubble glass-chat-${sender}`;
    
    const isUser = sender === 'user';
    const alignment = isUser ? 'flex-end' : 'flex-start';
    const bgColor = isUser ? 'rgba(139, 92, 246, 0.2)' : 'rgba(255, 255, 255, 0.45)';
    
    bubble.style.cssText = `
      max-width: ${products && products.length > 0 ? '90%' : '80%'};
      margin: 8px 0;
      padding: ${products && products.length > 0 ? '16px' : '12px 16px'};
      align-self: ${alignment};
      background: ${bgColor};
      word-wrap: break-word;
      line-height: 1.4;
      display: flex;
      flex-direction: column;
      gap: 12px;
    `;
    
    // Add message text
    if (message && message.trim()) {
      const textElement = document.createElement('div');
      textElement.className = 'chat-bubble-text';
      textElement.textContent = message;
      textElement.style.cssText = `
        font-size: 14px;
        color: ${overLight ? 'rgba(0,0,0,0.8)' : 'rgba(255,255,255,0.9)'};
      `;
      bubble.appendChild(textElement);
    }
    
    // Add products if provided
    if (products && Array.isArray(products) && products.length > 0) {
      const productContainer = GlassUIComponents.createProductGrid(products, { 
        compact: true, 
        overLight,
        showActions 
      });
      bubble.appendChild(productContainer.container);
    }
    
    const glassEffect = LiquidGlassUtils.createGlassComponent(bubble, {
      cornerRadius: 18,
      blurAmount: 0.05,
      overLight,
      enableMouseTracking: false
    });
    
    return {
      bubble,
      glassEffect,
      setText: (text) => {
        const textEl = bubble.querySelector('.chat-bubble-text');
        if (textEl) {
          textEl.textContent = text;
        }
      },
      setProducts: (newProducts) => {
        const existing = bubble.querySelector('.product-grid-container');
        if (existing) existing.remove();
        
        if (newProducts && newProducts.length > 0) {
          const productContainer = GlassUIComponents.createProductGrid(newProducts, { 
            compact: true, 
            overLight,
            showActions 
          });
          bubble.appendChild(productContainer.container);
        }
      },
      destroy: () => {
        glassEffect.destroy();
        if (bubble.parentNode) {
          bubble.parentNode.removeChild(bubble);
        }
      }
    };
  }

  /**
   * Create modern product grid for chat bubbles
   * @param {Array} products - Product array
   * @param {Object} options - Display options
   * @returns {Object} Product grid component
   */
  static createProductGrid(products, options = {}) {
    const {
      compact = false,
      overLight = false,
      showActions = true,
      maxItems = 3
    } = options;
    
    const container = document.createElement('div');
    container.className = 'product-grid-container';
    container.style.cssText = `
      display: flex;
      flex-direction: column;
      gap: 12px;
      width: 100%;
    `;
    
    const displayProducts = products.slice(0, maxItems);
    
    displayProducts.forEach((product, index) => {
      const productCard = GlassUIComponents.createProductCard(product, {
        compact,
        overLight,
        showActions,
        animationDelay: index * 100
      });
      container.appendChild(productCard.card);
    });
    
    return {
      container,
      updateProducts: (newProducts) => {
        container.innerHTML = '';
        const displayProducts = newProducts.slice(0, maxItems);
        displayProducts.forEach((product, index) => {
          const productCard = GlassUIComponents.createProductCard(product, {
            compact,
            overLight,
            showActions,
            animationDelay: index * 100
          });
          container.appendChild(productCard.card);
        });
      }
    };
  }

  /**
   * Create modern product card
   * @param {Object} product - Product data
   * @param {Object} options - Card options
   * @returns {Object} Product card component
   */
  static createProductCard(product, options = {}) {
    const {
      compact = false,
      overLight = false,
      showActions = true,
      animationDelay = 0
    } = options;
    
    const card = document.createElement('div');
    card.className = 'glass-product-card';
    card.style.cssText = `
      display: flex;
      gap: 12px;
      padding: 12px;
      background: ${overLight ? 'rgba(255,255,255,0.1)' : 'rgba(255,255,255,0.05)'};
      border-radius: 12px;
      transition: all 0.3s ease;
      cursor: pointer;
      animation: slideInUp 0.6s ease ${animationDelay}ms both;
      position: relative;
      overflow: hidden;
    `;
    
    // Product image
    const imageContainer = document.createElement('div');
    imageContainer.className = 'product-image-container';
    imageContainer.style.cssText = `
      flex-shrink: 0;
      width: ${compact ? '60px' : '80px'};
      height: ${compact ? '60px' : '80px'};
      border-radius: 8px;
      background: rgba(255,255,255,0.1);
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      position: relative;
    `;
    
    if (product.image_url) {
      const img = document.createElement('img');
      img.src = product.image_url;
      img.alt = product.title || 'Product';
      img.style.cssText = `
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 8px;
        transition: transform 0.3s ease;
      `;
      imageContainer.appendChild(img);
    } else {
      imageContainer.innerHTML = `
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <circle cx="8.5" cy="8.5" r="1.5"/>
          <polyline points="21,15 16,10 5,21"/>
        </svg>
      `;
    }
    
    // Product details
    const detailsContainer = document.createElement('div');
    detailsContainer.className = 'product-details';
    detailsContainer.style.cssText = `
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-width: 0;
    `;
    
    // Title and price
    const titleElement = document.createElement('h4');
    titleElement.className = 'product-title';
    titleElement.textContent = product.title || 'Product';
    titleElement.style.cssText = `
      margin: 0 0 4px 0;
      font-size: ${compact ? '14px' : '16px'};
      font-weight: 600;
      color: ${overLight ? 'rgba(0,0,0,0.9)' : 'rgba(255,255,255,0.9)'};
      line-height: 1.3;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    `;
    
    const priceElement = document.createElement('div');
    priceElement.className = 'product-price';
    const price = product.price_range?.min || product.price || '0';
    const currency = product.price_range?.currency || 'USD';
    priceElement.textContent = `${currency} $${price}`;
    priceElement.style.cssText = `
      font-size: ${compact ? '16px' : '18px'};
      font-weight: 700;
      color: #10B981;
      margin-bottom: ${showActions ? '8px' : '0'};
    `;
    
    detailsContainer.appendChild(titleElement);
    detailsContainer.appendChild(priceElement);
    
    // Action buttons
    if (showActions) {
      const actionsContainer = document.createElement('div');
      actionsContainer.className = 'product-actions';
      actionsContainer.style.cssText = `
        display: flex;
        gap: 8px;
        align-items: center;
      `;
      
      // Add to cart button
      const addToCartBtn = document.createElement('button');
      addToCartBtn.className = 'product-add-to-cart';
      addToCartBtn.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6"/>
        </svg>
        Add to Cart
      `;
      addToCartBtn.style.cssText = `
        flex: 1;
        padding: 8px 12px;
        background: linear-gradient(135deg, #10B981, #059669);
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
      `;
      
      // Quick view button
      const quickViewBtn = document.createElement('button');
      quickViewBtn.className = 'product-quick-view';
      quickViewBtn.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
          <circle cx="12" cy="12" r="3"/>
        </svg>
      `;
      quickViewBtn.style.cssText = `
        padding: 8px;
        background: rgba(255,255,255,0.45);
        color: ${overLight ? 'rgba(0,0,0,0.7)' : 'rgba(255,255,255,0.7)'};
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;
      `;
      
      actionsContainer.appendChild(addToCartBtn);
      actionsContainer.appendChild(quickViewBtn);
      detailsContainer.appendChild(actionsContainer);
      
      // Event listeners
      addToCartBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        GlassUIComponents.handleAddToCart(product, addToCartBtn);
      });
      
      quickViewBtn.addEventListener('click', (e) => {
        e.stopPropagation();
        GlassUIComponents.showProductModal(product);
      });
    }
    
    card.appendChild(imageContainer);
    card.appendChild(detailsContainer);
    
    // Card hover effects
    card.addEventListener('mouseenter', () => {
      card.style.transform = 'translateY(-2px)';
      card.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
      
      const img = card.querySelector('img');
      if (img) img.style.transform = 'scale(1.05)';
    });
    
    card.addEventListener('mouseleave', () => {
      card.style.transform = 'translateY(0)';
      card.style.boxShadow = 'none';
      
      const img = card.querySelector('img');
      if (img) img.style.transform = 'scale(1)';
    });
    
    // Add CSS animations
    if (!document.querySelector('#glass-animations')) {
      const style = document.createElement('style');
      style.id = 'glass-animations';
      style.textContent = `
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        @keyframes addToCartSuccess {
          0% { transform: scale(1); }
          50% { transform: scale(0.95); background: #059669; }
          100% { transform: scale(1); }
        }
      `;
      document.head.appendChild(style);
    }
    
    return {
      card,
      updateProduct: (newProduct) => {
        const title = card.querySelector('.product-title');
        const price = card.querySelector('.product-price');
        const img = card.querySelector('img');
        
        if (title) title.textContent = newProduct.title || 'Product';
        if (price) {
          const newPrice = newProduct.price_range?.min || newProduct.price || '0';
          const newCurrency = newProduct.price_range?.currency || 'USD';
          price.textContent = `${newCurrency} $${newPrice}`;
        }
        if (img && newProduct.image_url) {
          img.src = newProduct.image_url;
          img.alt = newProduct.title || 'Product';
        }
      }
    };
  }

  /**
   * Handle add to cart action
   * @param {Object} product - Product data
   * @param {Element} button - Button element
   */
  static handleAddToCart(product, button) {
    // Visual feedback
    button.style.animation = 'addToCartSuccess 0.3s ease';
    const originalText = button.innerHTML;
    
    button.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="20,6 9,17 4,12"/>
      </svg>
      Added!
    `;
    
    // Dispatch custom event for cart handling
    document.dispatchEvent(new CustomEvent('voice-assistant-add-to-cart', {
      detail: { product }
    }));
    
    setTimeout(() => {
      button.innerHTML = originalText;
      button.style.animation = '';
    }, 2000);
  }

  /**
   * Show product modal/quick view
   * @param {Object} product - Product data
   */
  static showProductModal(product) {
    // Dispatch custom event for modal handling
    document.dispatchEvent(new CustomEvent('voice-assistant-product-modal', {
      detail: { product }
    }));
  }
  
  /**
   * Create a glass product card
   * @param {Object} options - Product card options
   * @returns {Object} Product card component
   */
  static createProductCard(options = {}) {
    const {
      title = 'Product Title',
      price = '$0.00',
      image = null,
      overLight = false
    } = options;
    
    const card = document.createElement('div');
    card.className = 'glass-product-card';
    card.style.cssText = `
      width: 200px;
      cursor: pointer;
      transition: transform 0.2s ease;
      flex-shrink: 0;
    `;
    
    if (image) {
      const imageEl = document.createElement('img');
      imageEl.src = image;
      imageEl.alt = title;
      imageEl.style.cssText = `
        width: 100%;
        height: 140px;
        object-fit: cover;
        border-radius: 12px 12px 0 0;
      `;
      card.appendChild(imageEl);
    }
    
    const content = document.createElement('div');
    content.style.cssText = `
      padding: 16px;
    `;
    
    const titleEl = document.createElement('h3');
    titleEl.textContent = title;
    titleEl.style.cssText = `
      margin: 0 0 8px 0;
      font-size: 14px;
      font-weight: 600;
      color: inherit;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    `;
    
    const priceEl = document.createElement('p');
    priceEl.textContent = price;
    priceEl.style.cssText = `
      margin: 0;
      font-size: 16px;
      font-weight: 700;
      color: #8b5cf6;
    `;
    
    content.appendChild(titleEl);
    content.appendChild(priceEl);
    card.appendChild(content);
    
    const glassEffect = LiquidGlassUtils.createGlassComponent(card, {
      cornerRadius: 12,
      blurAmount: 0.05,
      overLight,
      enableMouseTracking: true
    });
    
    card.addEventListener('mouseenter', () => {
      card.style.transform = 'translateY(-4px)';
    });
    
    card.addEventListener('mouseleave', () => {
      card.style.transform = 'translateY(0)';
    });
    
    return {
      card,
      titleEl,
      priceEl,
      glassEffect,
      destroy: () => {
        glassEffect.destroy();
        if (card.parentNode) {
          card.parentNode.removeChild(card);
        }
      }
    };
  }
  
  /**
   * Create a glass recording indicator
   * @param {Object} options - Recording indicator options
   * @returns {Object} Recording indicator component
   */
  static createRecordingIndicator(options = {}) {
    const {
      overLight = false
    } = options;
    
    const indicator = document.createElement('div');
    indicator.className = 'glass-recording-indicator';
    indicator.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;
      z-index: 10;
    `;
    
    const pulse = document.createElement('div');
    pulse.className = 'glass-recording-pulse';
    pulse.style.cssText = `
      width: 20px;
      height: 20px;
      background: #ef4444;
      border-radius: 50%;
      animation: glass-recording-pulse 1.5s infinite;
    `;
    
    // Add pulse animation styles
    const style = document.createElement('style');
    style.textContent = `
      @keyframes glass-recording-pulse {
        0% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.2); opacity: 0.7; }
        100% { transform: scale(1); opacity: 1; }
      }
    `;
    document.head.appendChild(style);
    
    indicator.appendChild(pulse);
    
    const glassEffect = LiquidGlassUtils.createGlassComponent(indicator, {
      cornerRadius: 999,
      blurAmount: 0.05,
      overLight,
      enableMouseTracking: false
    });
    
    return {
      indicator,
      pulse,
      glassEffect,
      show: () => {
        indicator.style.opacity = '1';
      },
      hide: () => {
        indicator.style.opacity = '0';
      },
      destroy: () => {
        glassEffect.destroy();
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator);
        }
        if (style.parentNode) {
          style.parentNode.removeChild(style);
        }
      }
    };
  }
}