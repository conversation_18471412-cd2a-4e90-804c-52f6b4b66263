'use strict';

/**
 * Executes automation commands received from the backend.
 */
export class CommandExecutor {
  constructor(virtualCursor) {
    this.cursor = virtualCursor;
  }

  /**
   * Finds an element on the page using its data-localid attribute.
   * @param {number} localId - The ID assigned during context capture.
   * @returns {HTMLElement|null}
   * @private
   */
  _getElementByLocalId(localId) {
    return document.querySelector(`[data-localid="${localId}"]`);
  }

  async _executeClick(localId) {
    const element = this._getElementByLocalId(localId);
    if (!element) throw new Error(`Element with localId ${localId} not found.`);
    
    await this.cursor.moveToElement(element);
    this.cursor.click();
    element.click();
    await new Promise(resolve => setTimeout(resolve, 500)); // Pause after click
  }

  async _executeType(localId, value) {
    const element = this._getElementByLocalId(localId);
    if (!element) throw new Error(`Element with localId ${localId} not found.`);
    
    await this.cursor.moveToElement(element);
    this.cursor.click();
    
    if (typeof element.value !== 'undefined') {
      element.value = value;
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
    } else {
        throw new Error(`Element with localId ${localId} does not support typing.`);
    }
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  async _executeNavigate(url) {
    this.cursor.hide();
    window.location.href = url;
  }

  /**
   * Public method to execute a command object.
   * @param {object} command - The command to execute { action, elementId, value }.
   */
  async executeCommand(command) {
    console.log(`[Executor] Executing:`, command);
    if (!command || !command.action) {
      throw new Error('Invalid command received.');
    }

    try {
      switch (command.action) {
        case 'click':
          await this._executeClick(command.elementId);
          break;
        case 'type':
          await this._executeType(command.elementId, command.value);
          break;
        case 'navigate':
            await this._executeNavigate(command.url);
            break;
        default:
          throw new Error(`Unknown command action: ${command.action}`);
      }
    } catch (error) {
      console.error(`[Executor] Error executing command:`, command, error);
      this.cursor.hide();
      throw error; // Re-throw to be handled by the caller
    }
  }
} 