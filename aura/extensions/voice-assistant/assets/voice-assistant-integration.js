/**
 * Simplified Voice Assistant Integration with Tool Calling Support
 * 
 * This module provides direct HTTP communication with the backend NLU service
 * and includes DOM context capture and tool execution for autonomous assistance.
 */

import { VoiceAssistantUI } from './voice-assistant-ui.js';
import { VirtualCursor } from './virtual-cursor.js';
import { CommandExecutor } from './command-executor.js';
import { RnnoiseWorkletNode, loadRnnoise } from '@sapphi-red/web-noise-suppressor';
// Use local copies of RNNoise assets
const rnnoiseWorkletUrl = './workletProcessor.js';
const rnnoiseWasmUrl = './rnnoise.wasm';
const rnnoiseSimdUrl = './rnnoise_simd.wasm';
import * as html2canvasModule from 'html2canvas';
const html2canvas = html2canvasModule.default || html2canvasModule;

/**
 * Voice Assistant Integration using direct HTTP calls
 * 
 * Main integration class that captures audio, sends it with page context,
 * and executes returned tool calls autonomously.
 */
export class VoiceAssistantIntegration {
  constructor(shopDomain, ui = null) { 
    this.shopDomain = shopDomain;
    
    // Audio recording properties
    this.mediaRecorder = null;
    this.rawStream = null;
    this.audioStream = null;
    this.audioChunks = [];
    this.isRecording = false;
    
    // Visualization properties
    this.onVisualizerDataCallback = null;
    this.visualizerInterval = null;
    this.audioContext = null;
    this.analyserNode = null;
    this.frequencyDataArray = null;
    
    // Backend configuration
    this.backendUrl = 'https://aura-backend-worker.feisty-agency.workers.dev';

    // Text-to-speech
    this.speechSynthesis = window.speechSynthesis;
    this.currentAudio = null; // Track current Cartesia audio
    this.isSpeaking = false; // Track if currently speaking
    this.currentResponseText = null; // Store current response text for product display

    // Noise suppression pipeline
    this.processedStream = null;
    this.processedDestination = null;

    // Memory and knowledge configuration - USE PERSISTENT USER ID!
    this.userId = this.generateOrGetUserId();
    this.sessionId = this.userId; // Use persistent userId for memory consistency
    this.enableMemory = true;
    this.enableKnowledge = true;

    // Conversation state management
    this.lastResponse = null;
    this.lastResponseTime = 0;
    this.isProcessing = false;
    this.requestQueue = [];
    this.debounceTimeout = null;

    // Make sure shopDomain doesn't include protocol
    if (this.shopDomain) {
      this.shopDomain = this.shopDomain.replace(/^https?:\/\//, '');
    }
    
    console.log('[VA-Integration] Initialized with shop:', shopDomain);
    console.log('[VA-Integration] User ID:', this.userId);
    console.log('[VA-Integration] Memory enabled:', this.enableMemory);
    console.log('[VA-Integration] Knowledge enabled:', this.enableKnowledge);

    // Use provided UI instance or create a new one (for backward compatibility)
    this.ui = ui || new VoiceAssistantUI();
    this.cursor = new VirtualCursor();
    this.executor = new CommandExecutor(this.cursor);

    // ===== Load lightweight catalog context from Liquid script =====
    this.shopCatalog = { products: [], collections: [] };
    try {
      // More specific selector to avoid conflicts with other scripts
      const catalogElem = document.querySelector('.shopify-app-block #shop-catalog');
      if (catalogElem && catalogElem.textContent) {
        this.shopCatalog = JSON.parse(catalogElem.textContent.trim());
        console.log('[VA-Integration] Loaded local shop catalog:', this.shopCatalog);
      } else {
        console.warn('[VA-Integration] Could not find the correct shop-catalog script tag.');
      }
    } catch (catErr) {
      console.warn('[VA-Integration] Failed to parse shop-catalog JSON:', catErr);
    }

    // Load user profile on initialization
    this.loadUserProfile();

    // Set up text query event listener
    this.setupTextQueryHandler();
    
    // Set up audio stopping event listener
    this.setupAudioStopHandler();
  }

  /**
   * Create a WAV file from PCM data
   * @param {Int16Array} pcmData - The 16-bit PCM audio data
   * @param {number} sampleRate - Sample rate (e.g., 44100, 48000)
   * @param {number} channels - Number of channels (1 for mono, 2 for stereo)
   * @param {number} bitsPerSample - Bits per sample (16)
   * @returns {Uint8Array} The WAV file data
   */
  createWavFile(pcmData, sampleRate, channels, bitsPerSample) {
    const bytesPerSample = bitsPerSample / 8;
    const blockAlign = channels * bytesPerSample;
    const dataSize = pcmData.length * bytesPerSample;
    const fileSize = 44 + dataSize; // WAV header is 44 bytes

    const buffer = new ArrayBuffer(fileSize);
    const view = new DataView(buffer);
    
    // WAV file header
    let offset = 0;
    
    // RIFF chunk descriptor
    this.writeString(view, offset, 'RIFF'); offset += 4;
    view.setUint32(offset, fileSize - 8, true); offset += 4; // File size - 8
    this.writeString(view, offset, 'WAVE'); offset += 4;
    
    // fmt sub-chunk
    this.writeString(view, offset, 'fmt '); offset += 4;
    view.setUint32(offset, 16, true); offset += 4; // Sub-chunk size (16 for PCM)
    view.setUint16(offset, 1, true); offset += 2; // Audio format (1 for PCM)
    view.setUint16(offset, channels, true); offset += 2; // Number of channels
    view.setUint32(offset, sampleRate, true); offset += 4; // Sample rate
    view.setUint32(offset, sampleRate * blockAlign, true); offset += 4; // Byte rate
    view.setUint16(offset, blockAlign, true); offset += 2; // Block align
    view.setUint16(offset, bitsPerSample, true); offset += 2; // Bits per sample
    
    // data sub-chunk
    this.writeString(view, offset, 'data'); offset += 4;
    view.setUint32(offset, dataSize, true); offset += 4; // Data size
    
    // PCM data
    for (let i = 0; i < pcmData.length; i++) {
      view.setInt16(offset, pcmData[i], true);
      offset += 2;
    }
    
    return new Uint8Array(buffer);
  }

  /**
   * Write a string to a DataView at the specified offset
   * @param {DataView} view - The DataView to write to
   * @param {number} offset - The byte offset
   * @param {string} string - The string to write
   */
  writeString(view, offset, string) {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i));
    }
  }

  /**
   * Start listening for voice input
   */
  async startListening() {
    try {
      console.log('[VA-Integration] Starting high-fidelity voice capture...');
      
      // STOP ANY CURRENTLY PLAYING CARTESIA AUDIO
      this.stopCurrentAudio();
      
      // FIX VoD: Stop any ongoing speech synthesis
      this.stopCurrentSpeech();
      
      // Enforce 16kHz mono audio for optimal STT accuracy, as per research
      const constraints = {
        audio: {
          sampleRate: 16000,
          sampleSize: 16,
          channelCount: 1,
          echoCancellation: true, // Use browser-native AEC
          noiseSuppression: true, // Use browser-native NS
          autoGainControl: false, // Disable AGC to prevent volume fluctuations
        }
      };
      
      this.rawStream = await navigator.mediaDevices.getUserMedia(constraints);
      
      // Verify actual stream settings to guard against browser overrides
      const track = this.rawStream.getAudioTracks()[0];
      const settings = track.getSettings();
      console.log('[VA-Integration] Audio track settings:', settings);
      if (settings.sampleRate !== 16000 || settings.channelCount !== 1) {
        console.warn(`[VA-Integration] Browser overrode constraints. Actual SR: ${settings.sampleRate}, Channels: ${settings.channelCount}`);
      }

      this.audioStream = this.rawStream;
      console.log('[VA-Integration] Using browser-native echo cancellation and noise suppression.');
      
      // Set up audio context for visualization and VAD
      this.setupAudioVisualization();
      
      // Use ScriptProcessorNode for direct PCM access, replacing MediaRecorder
      const bufferSize = 4096;
      this.scriptNode = this.audioContext.createScriptProcessor(bufferSize, 1, 1);
      this.audioStream.getAudioTracks().forEach(track => {
        const source = this.audioContext.createMediaStreamSource(new MediaStream([track]));
        source.connect(this.scriptNode);
        this.scriptNode.connect(this.audioContext.destination);
      });

      this.audioChunks = []; // This will now store raw PCM chunks

      this.scriptNode.onaudioprocess = (event) => {
        if (!this.isRecording) return;
        // Get the raw PCM data
        const pcmChunk = event.inputBuffer.getChannelData(0);
        this.audioChunks.push(new Float32Array(pcmChunk));
      };
      
      this.isRecording = true;
      console.log('[VA-Integration] Recording started successfully using ScriptProcessorNode for PCM capture.');
      return true;

    } catch (error) {
      console.error('[VA-Integration] Error starting recording:', error);
      return false;
    }
  }

  /**
   * Stop listening and process the recording
   */
  async stopListening() {
    if (!this.isRecording) {
      console.warn('[VA-Integration] Not currently recording');
      return;
    }
    
    console.log('[VA-Integration] Stopping recording...');
    this.isRecording = false;
    
    // Disconnect the ScriptProcessorNode to stop processing
    if (this.scriptNode) {
      this.scriptNode.disconnect();
      this.scriptNode.onaudioprocess = null;
      this.scriptNode = null;
    }
    
    // Process the collected PCM data
    this.processRecording();
    
    // Clean up audio stream
    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop());
      this.audioStream = null;
    }

    // Clean up visualization
    this.cleanupVisualization();
  }

  /**
   * Process the recorded audio with page context and handle tool calls
   */
  async processRecording() {
    try {
      console.log('[VA-Integration] Processing PCM recording with WAV encoding...');
      
      // Prevent duplicate processing
      if (this.isProcessing) {
        console.log('[VA-Integration] Already processing, skipping duplicate recording');
        return;
      }

      this.isProcessing = true;
      
      // 1. Concatenate PCM chunks
      const totalLength = this.audioChunks.reduce((acc, chunk) => acc + chunk.length, 0);
      const pcmData = new Float32Array(totalLength);
      let offset = 0;
      for (const chunk of this.audioChunks) {
        pcmData.set(chunk, offset);
        offset += chunk.length;
      }
      this.audioChunks = []; // Clear chunks after processing

      // 2. Encode PCM to WAV (much simpler and more reliable than FLAC)
      const sampleRate = this.audioContext?.sampleRate || 16000;
      const channels = 1;
      const bitsPerSample = 16;

      // Convert Float32 PCM to 16-bit PCM for WAV
      const pcm16 = new Int16Array(pcmData.length);
      for (let i = 0; i < pcmData.length; i++) {
        // Clamp and scale Float32 [-1, 1] to Int16 [-32768, 32767]
        const sample = Math.max(-1, Math.min(1, pcmData[i]));
        pcm16[i] = Math.round(sample * 0x7FFF);
      }

      // Create WAV file
      const wavData = this.createWavFile(pcm16, sampleRate, channels, bitsPerSample);
      
      if (!wavData || wavData.length === 0) {
        throw new Error('WAV encoding failed - no data returned.');
      }

      // 3. Create Blob and Base64 encode
      const audioBlob = new Blob([wavData], { type: 'audio/wav' });
      const audioBuffer = await audioBlob.arrayBuffer();
      const audioBase64 = this.arrayBufferToBase64(audioBuffer);
      
      // Gather page context for tool execution
      const pageContext = this.gatherPageContext();
      
      // Prepare request payload (without screenshot initially)
      const payload = {
        audio: audioBase64,
        elements: pageContext.elements,
        screenshot: pageContext.screenshot,
        sampleRate: sampleRate,
        channels: channels,
        mimeType: 'audio/wav', // Using WAV instead of FLAC
        sessionId: this.sessionId,
        storeId: this.shopDomain,
        enableMemory: this.enableMemory,
        enableKnowledge: this.enableKnowledge,
        participantIdentity: this.userId,
        shopCatalog: this.shopCatalog
      };
      
      console.log('[VA-Integration] Sending request with:', {
        audioSize: audioBuffer.byteLength,
        elementsCount: pageContext.elements.length,
        hasScreenshot: !!pageContext.screenshot,
        sessionId: this.sessionId,
        participantIdentity: this.userId
      });

      // Send to backend for initial processing
      const response = await fetch(`${this.backendUrl}/nlu-enhanced`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        throw new Error(`Backend request failed: ${response.status}`);
      }
      
      const result = await response.json();

      console.log('[VA-Integration] Received response:', {
        text: result.text,
        toolCallsCount: result.toolCalls?.length || 0,
        error: result.error
      });
      
      // Check if we need to capture screenshot for browser automation
      const needsScreenshot = result.toolCalls?.some(call => call.tool === 'browserAutomation');
      
      if (needsScreenshot) {
        console.log('[VA-Integration] Browser automation detected, capturing screenshot...');
        
        try {
          // Capture real screenshot
          const screenshot = await this.captureScreenshot();
          
          if (screenshot) {
            console.log('[VA-Integration] Screenshot captured, re-sending with screenshot...');
            
            // Re-send request with real screenshot
            const payloadWithScreenshot = {
              ...payload,
              screenshot: screenshot
            };
            
            const screenshotResponse = await fetch(`${this.backendUrl}/nlu-enhanced`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(payloadWithScreenshot)
            });
            
            if (screenshotResponse.ok) {
              const screenshotResult = await screenshotResponse.json();
              console.log('[VA-Integration] Received response with screenshot:', screenshotResult);
              
              // Use the result with screenshot
              await this.handleResponse(screenshotResult);
              return;
            } else {
              console.warn('[VA-Integration] Screenshot request failed, using original result');
            }
          } else {
            console.warn('[VA-Integration] Screenshot capture failed, using original result');
          }
        } catch (screenshotError) {
          console.error('[VA-Integration] Screenshot capture error:', screenshotError);
        }
      }
      
      // Record product or collection interests if provided
      if (Array.isArray(result.productSuggestions)) {
        for (const prod of result.productSuggestions) {
          if (prod && prod.handle) {
            this.recordInterest('product', prod.handle).catch(() => {});
          }
        }
      }
      
      // Execute any tool calls
      if (result.toolCalls && result.toolCalls.length > 0) {
        console.log('[VA-Integration] Executing tool calls:', result.toolCalls);
        
        for (const toolCall of result.toolCalls) {
          await this.executeToolCall(toolCall);
        }
      }
      
      // Handle the response (either original or if screenshot failed)
      await this.handleResponse(result);
      
    } catch (error) {
      console.error('[VA-Integration] Error processing recording:', error);
      this.handleError(`Processing failed: ${error.message}`);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Handle the response from the backend, including tool execution
   */
  async handleResponse(result) {
    // Log memory and knowledge usage
    if (result.memoryFactsUsed !== undefined) {
      console.log('[VA-Integration] 🧠 Memory facts used:', result.memoryFactsUsed);
    }
    if (result.knowledgeChunksUsed !== undefined) {
      console.log('[VA-Integration] 📚 Knowledge chunks used:', result.knowledgeChunksUsed);
    }
    
    // Display/speak the text response if available
    if (result.text || result.message) {
      const responseText = result.text || result.message;
      this.currentResponseText = responseText; // Store for later use with products
      this.displayMessage(responseText, 'assistant');
      
      // ALWAYS use the new cards system
      if (result.cards && result.cards.length > 0) {
        console.log('[VA-Integration] Using NEW CARDS SYSTEM:', result.cards);
        this.handleCards(result.cards, responseText);
      }
      // Handle legacy products format if no cards are present
      else if (result.products && result.products.length > 0) {
        console.log('[VA-Integration] Using LEGACY PRODUCTS FORMAT');
        this.ui.displayChatBubbleContent({
          text: responseText,
          products: result.products
        });
      }
      // DEFAULT: Text only response
      else {
        this.ui.updateChatBubbleMessage(responseText);
      }
      
      // Use async TTS but don't block the UI
      this.speakText(responseText).catch(error => {
        console.warn('[VA-Integration] TTS playback failed:', error);
      });
    }
    
    // Handle errors
    if (result.error) {
      this.handleError(result.error);
    }
  }

  /**
   * Execute a tool call (browser automation or web research results)
   */
  async executeToolCall(toolCall) {
    try {
      console.log('[VA-Integration] Executing tool call:', toolCall);
      
      if (toolCall.tool === 'browserAutomation') {
        if (toolCall.steps && toolCall.steps.length > 0) {
          this.ui.setThinkingState(true, 'Running automation...');
          for (const step of toolCall.steps) {
            await this.executor.executeCommand(step);
          }
          this.ui.setThinkingState(false, 'Automation complete!');
          this.cursor.hide();
        } else if (toolCall.error) {
          this.handleError(`Automation failed: ${toolCall.error}`);
        } else {
          this.ui.updateChatBubbleMessage("I'm ready to help, but I didn't receive any steps.");
        }
      } else if (toolCall.tool === 'webResearch') {
        // Handle research results
        if (toolCall.content) {
          this.ui.updateChatBubbleMessage(toolCall.content);
        } else if (toolCall.error) {
          this.handleError(`Research failed: ${toolCall.error}`);
        }
      } else if (toolCall.tool === 'searchProducts') {
        // Handle product search results
        console.log('[VA-Integration] Processing product search results:', toolCall);
        if (toolCall.products && toolCall.products.length > 0) {
          // --- Normalize product objects for UI ---
          const normalized = toolCall.products.map((p) => {
            const handleFromId = p.product_id && typeof p.product_id === 'string' && p.product_id.includes('/Product/')
              ? p.product_id.split('/Product/')[1]
              : undefined;
            return {
              id: p.id || p.product_id || handleFromId || p.handle || p.title,
              title: p.title,
              handle: p.handle || handleFromId,
              image: p.image || p.image_url,
              price: p.price || (p.price_range && p.price_range.min ? `${p.price_range.min} ${p.price_range.currency || ''}` : undefined)
            };
          });

          // Display results via the UI
          const message = `Found ${normalized.length} product(s).`;
          this.ui.showProductRecommendations(normalized);
          this.ui.displayChatBubbleContent({
            text: this.currentResponseText || message,
            products: normalized
          });

          // --- Feedback loop: record interest for each product ---
          for (const p of normalized) {
            if (p && p.handle) {
              this.recordInterest('product', p.handle).catch(() => {});
            }
          }
        } else if (toolCall.error) {
          console.error('[VA-Integration] Product search failed:', toolCall);
          this.handleError(`Product search failed: ${toolCall.error}`);
        } else {
          this.ui.updateChatBubbleMessage("No products found for your search.");
        }
      }
    } catch (error) {
      console.error('[VA-Integration] Error executing tool call:', error);
      this.handleError(`Execution error: ${error.message}`);
      this.cursor.hide();
    }
  }

  /**
   * Gather page context including DOM elements and screenshot
   */
  gatherPageContext() {
    console.log('[VA-Integration] Gathering page context...');
    
    // Gather interactive elements
    const elements = [];
    const interactiveSelectors = [
      'button',
      'a[href]',
      'input',
      'textarea',
      'select',
      '[role="button"]',
      '[onclick]',
      '.btn',
      '.button'
    ];
    
    const foundElements = document.querySelectorAll(interactiveSelectors.join(','));
    
    foundElements.forEach((element, index) => {
      // Assign local ID for reference
      element.setAttribute('data-localid', index.toString());
      
      // Get computed style and bounds for proper formatting
      const computedStyle = window.getComputedStyle(element);
      const rect = element.getBoundingClientRect();
      
      // Extract element info in the format expected by automation worker
      const elementInfo = {
        localId: index,
        type: element.type || '',
        text: element.innerText?.trim().substring(0, 100) || '',
        tag: element.tagName.toLowerCase(),
        placeholder: element.placeholder || '',
        value: element.value || '',
        attributes: {
          type: element.type || '',
          class: element.className || '',
          style: {
            position: computedStyle.position,
            display: computedStyle.display,
            visibility: computedStyle.visibility
          }
        },
        bounds: {
          x: rect.x,
          y: rect.y,
          width: rect.width,
          height: rect.height
        }
      };
      
      elements.push(elementInfo);
    });
    
    console.log('[VA-Integration] Found', elements.length, 'interactive elements');
    
    return {
      elements,
      screenshot: null // Will be captured when needed
    };
  }

  /**
   * Capture screenshot (prefer DOM-to-canvas, fallback to screen-capture prompt)
   */
  async captureScreenshot() {
    // ----- 1. Try DOM-based capture with html2canvas (no permission prompt) -----
    try {
      if (typeof window.html2canvas !== 'function') {
        throw new Error('html2canvas not available');
      }

      console.log('[VA-Integration] Attempting DOM screenshot via html2canvas…');

      const fullCanvas = await window.html2canvas(document.body, {
        useCORS: false,                  // don't attempt CORS fetches
        allowTaint: false,
        foreignObjectRendering: false,
        ignoreElements: el => el.tagName === 'IMG', // skip all <img>, avoids taint from CDN images
        backgroundColor: null,
        windowWidth: document.documentElement.scrollWidth,
        windowHeight: document.documentElement.scrollHeight
      });

      const maxDimension = 800;
      let targetWidth = fullCanvas.width;
      let targetHeight = fullCanvas.height;

      if (targetWidth > maxDimension || targetHeight > maxDimension) {
        if (targetWidth > targetHeight) {
          targetHeight = Math.round((targetHeight / targetWidth) * maxDimension);
          targetWidth = maxDimension;
        } else {
          targetWidth = Math.round((targetWidth / targetHeight) * maxDimension);
          targetHeight = maxDimension;
        }
      }

      // Resize onto a smaller canvas for bandwidth
      const canvas = document.createElement('canvas');
      canvas.width = targetWidth;
      canvas.height = targetHeight;
      const ctx = canvas.getContext('2d');
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';
      ctx.drawImage(fullCanvas, 0, 0, targetWidth, targetHeight);

      const blob = await new Promise(res => canvas.toBlob(res, 'image/png', 0.9));
      if (!blob) throw new Error('blob creation failed');

      const base64Data = await new Promise(res => {
        const r = new FileReader();
        r.onload = () => res(r.result.split(',')[1]);
        r.readAsDataURL(blob);
      });

      console.log('[VA-Integration] DOM screenshot created, base64 length:', base64Data.length);

      return { width: targetWidth, height: targetHeight, data: base64Data, format: 'png' };
    } catch (domErr) {
      console.warn('[VA-Integration] DOM screenshot failed, falling back to screen-capture:', domErr);
    }

    // No fallback: avoid prompting the user for permissions. If DOM capture failed, we simply return null.
    return null;
  }

  /**
   * Set up audio visualization
   */
  setupAudioVisualization() {
    try {
      if (!this.audioContext) {
        const AudioContextClass = window.AudioContext || window.webkitAudioContext;
        this.audioContext = new AudioContextClass();
      }
      const source = this.audioContext.createMediaStreamSource(this.audioStream);
      this.analyserNode = this.audioContext.createAnalyser();
      
      this.analyserNode.fftSize = 256;
      source.connect(this.analyserNode);
      
      this.frequencyDataArray = new Uint8Array(this.analyserNode.frequencyBinCount);
      
      // Start visualization updates
      this.startVisualizerUpdates();
      
    } catch (error) {
      console.warn('[VA-Integration] Visualization setup failed:', error);
    }
  }
  
  /**
   * Start visualization updates
   */
  startVisualizerUpdates() {
    if (!this.analyserNode || !this.frequencyDataArray) return;
    
    this.visualizerInterval = setInterval(() => {
      if (this.onVisualizerDataCallback && this.isRecording) {
        this.analyserNode.getByteFrequencyData(this.frequencyDataArray);
        this.onVisualizerDataCallback(this.frequencyDataArray);
      }
    }, 16); // ~60fps
    }
    
  /**
   * Clean up visualization resources
   */
  cleanupVisualization() {
    if (this.visualizerInterval) {
      clearInterval(this.visualizerInterval);
      this.visualizerInterval = null;
    }
    
    if (this.audioContext) {
      try { this.audioContext.close(); } catch (_) {}
      this.audioContext = null;
    }
  }
  
  /**
   * Set callback for visualization data
   */
  setVisualizerDataCallback(callback) {
    this.onVisualizerDataCallback = callback;
  }
  
  /**
   * Display a message in the UI
   */
  displayMessage(message, type = 'assistant') {
    console.log(`[VA-Integration] Message (${type}):`, message);
    
    // Dispatch custom event for UI handling
    document.dispatchEvent(new CustomEvent('voice-assistant-message', {
      detail: { message, type, timestamp: Date.now() }
    }));
  }
  
  /**
   * Speak text using Cartesia TTS
   */
  async speakText(text) {
    try {
      if (!text || text.trim().length === 0) {
        console.warn('[VA-Integration] No text to speak');
        return;
      }

      // Stop any currently playing audio before starting new one
      this.stopCurrentAudio();
      this.stopCurrentSpeech();

      console.log('[VA-Integration] 🔊 Generating speech with Cartesia TTS...');
      this.isSpeaking = true;
      
      // Use the high-quality Cartesia TTS service
      const response = await fetch(`${this.backendUrl}/api/tts`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text: text.trim(),
          voice: 'professional-female',  // High-quality female voice
          speed: 1.0,
          format: 'wav',
          quality: 'high'
        })
      });

      if (!response.ok) {
        throw new Error(`TTS API error: ${response.status}`);
      }

      const result = await response.json();
      
      if (result.error) {
        throw new Error(result.error);
      }

      console.log('[VA-Integration] ✅ Generated Cartesia audio:', {
        size: `${(result.size / 1024).toFixed(1)}KB`,
        mimeType: result.mimeType
      });

      // Convert base64 to audio blob (handle potential decoding issues)
      let audioArray;
      try {
        // Try direct decoding first
        const audioData = atob(result.audioData);
        audioArray = new Uint8Array(audioData.length);
        for (let i = 0; i < audioData.length; i++) {
          audioArray[i] = audioData.charCodeAt(i);
        }
      } catch (decodeError) {
        console.log('[VA-Integration] Direct base64 decode failed, trying fetch approach:', decodeError);
        
        // Alternative: Use fetch with data URL
        const dataUrl = `data:${result.mimeType};base64,${result.audioData}`;
        const response = await fetch(dataUrl);
        const arrayBuffer = await response.arrayBuffer();
        audioArray = new Uint8Array(arrayBuffer);
      }
      
      const audioBlob = new Blob([audioArray], { type: result.mimeType });
      const audioUrl = URL.createObjectURL(audioBlob);
      
      // Create and play audio element
      const audio = new Audio(audioUrl);
      audio.volume = 0.8;
      this.currentAudio = audio; // Track current audio
      
      return new Promise((resolve, reject) => {
        audio.onended = () => {
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          this.isSpeaking = false;
          console.log('[VA-Integration] ✅ Cartesia TTS playback finished');
          resolve();
        };
        
        audio.onerror = (error) => {
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          this.isSpeaking = false;
          console.error('[VA-Integration] Audio playback error:', error);
          reject(error);
        };
        
        // Check if audio was stopped before playing
        if (!this.isSpeaking) {
          URL.revokeObjectURL(audioUrl);
          this.currentAudio = null;
          resolve();
          return;
        }
        
        audio.play().catch(reject);
      });

    } catch (error) {
      this.isSpeaking = false;
      console.warn('[VA-Integration] Cartesia TTS failed, falling back to browser TTS:', error);
      
      // Fallback to browser TTS if Cartesia fails
      this.fallbackToNativeTts(text);
    }
  }

  /**
   * Stop any currently playing Cartesia audio
   */
  stopCurrentAudio() {
    if (this.currentAudio) {
      console.log('[VA-Integration] 🔇 Stopping current Cartesia audio');
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
    }
    this.isSpeaking = false;
  }

  /**
   * Stop any currently playing speech synthesis
   */
  stopCurrentSpeech() {
    if (this.speechSynthesis && this.speechSynthesis.speaking) {
      console.log('[VA-Integration] 🔇 Stopping current speech synthesis');
      this.speechSynthesis.cancel();
    }
  }

  /**
   * Fallback to native browser TTS
   */
  fallbackToNativeTts(text) {
    try {
      if (this.speechSynthesis && text) {
        // Cancel any ongoing speech
        this.speechSynthesis.cancel();
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 1.0;
        utterance.pitch = 1.0;
        utterance.volume = 0.8;
        
        // Track speaking state
        this.isSpeaking = true;
        utterance.onend = () => {
          this.isSpeaking = false;
        };
        utterance.onerror = () => {
          this.isSpeaking = false;
        };
        
        // Try to use a pleasant voice
        const voices = this.speechSynthesis.getVoices();
        const preferredVoice = voices.find(voice => 
          voice.name.includes('Google') || 
          voice.name.includes('Samantha') ||
          voice.name.includes('Alex')
        );
        
        if (preferredVoice) {
          utterance.voice = preferredVoice;
        }
        
        this.speechSynthesis.speak(utterance);
      }
    } catch (error) {
      this.isSpeaking = false;
      console.error('[VA-Integration] Native TTS also failed:', error);
    }
  }

  /**
   * Handle cards from enhanced NLU response
   */
  handleCards(cards, responseText) {
    console.log('[VA-Integration] Processing cards:', cards);
    
    if (!cards || cards.length === 0) {
      // Text only response
      this.ui.updateChatBubbleMessage(responseText);
      return;
    }

    // Process each card type
    cards.forEach(card => {
      try {
        switch (card.type) {
          case 'product':
            if (card.data && card.data.products && card.data.products.length > 0) {
              console.log('[VA-Integration] Displaying product cards:', card.data.products);
              
              // Normalize product data for UI compatibility
              const normalizedProducts = card.data.products.map(p => ({
                id: p.id || p.product_id || p.handle || p.title,
                title: p.title,
                handle: p.handle,
                image: p.image || p.image_url,
                price: p.price || (p.price_range && p.price_range.min ? 
                  `${p.price_range.min} ${p.price_range.currency || 'AUD'}` : undefined)
              }));

              // Display product cards in chat bubble
              this.ui.displayChatBubbleContent({
                text: responseText,
                products: normalizedProducts
              });

              // Record interest for each product
              normalizedProducts.forEach(product => {
                if (product.handle) {
                  this.recordInterest('product', product.handle).catch(() => {});
                }
              });
            }
            break;

          case 'cart':
            console.log('[VA-Integration] Displaying cart card:', card.data);
            // Cart cards could show cart summary in the chat bubble
            this.ui.updateChatBubbleMessage(responseText);
            break;

          case 'order':
            console.log('[VA-Integration] Displaying order card:', card.data);
            // Order cards could show order history
            this.ui.updateChatBubbleMessage(responseText);
            break;

          case 'status':
            console.log('[VA-Integration] Displaying status card:', card.data);
            // Status cards for general information
            this.ui.updateChatBubbleMessage(responseText);
            break;

          default:
            console.warn('[VA-Integration] Unknown card type:', card.type);
            this.ui.updateChatBubbleMessage(responseText);
        }
      } catch (error) {
        console.error('[VA-Integration] Error processing card:', card.type, error);
        // Fallback to text only
        this.ui.updateChatBubbleMessage(responseText);
      }
    });
  }

  /**
   * Handle errors
   */
  handleError(message) {
    console.error('[VA-Integration] Error:', message);
    
    // Dispatch error event for UI handling
    document.dispatchEvent(new CustomEvent('voice-assistant-error', {
      detail: { message, timestamp: Date.now() }
    }));
  }

  /**
   * Convert ArrayBuffer to base64
   */
  arrayBufferToBase64(buffer) {
    let binary = '';
    const bytes = new Uint8Array(buffer);
    const chunkSize = 0x8000; // 32KB chunks
    
    for (let i = 0; i < bytes.length; i += chunkSize) {
      binary += String.fromCharCode(...bytes.subarray(i, i + chunkSize));
    }
    
    return btoa(binary);
    }

  /**
   * Apply client-side RNNoise noise suppression. Returns a MediaStream.
   */
  async applyNoiseSuppression(inputStream) {
    try {
      const AudioContextClass = window.AudioContext || window.webkitAudioContext;
      this.audioContext = new AudioContextClass();

      // Load worklet script into audioWorklet
      await this.audioContext.audioWorklet.addModule(rnnoiseWorkletUrl);

      // Fetch appropriate wasm (SIMD if available)
      const wasmBinary = await loadRnnoise({ url: rnnoiseWasmUrl, simdUrl: rnnoiseSimdUrl });

      const source = this.audioContext.createMediaStreamSource(inputStream);
      const rnnoiseNode = new RnnoiseWorkletNode(this.audioContext, {
        wasmBinary,
        maxChannels: 1
      });

      // Destination that will feed MediaRecorder
      this.processedDestination = this.audioContext.createMediaStreamDestination();
      source.connect(rnnoiseNode).connect(this.processedDestination);

      this.processedStream = this.processedDestination.stream;

      console.log('[VA-Integration] RNNoise noise suppression pipeline initialized');
    } catch (err) {
      console.error('[VA-Integration] RNNoise init failed:', err);
      throw err;
    }
  }

  /**
   * Generate or retrieve persistent user ID
   */
  generateOrGetUserId() {
    const storageKey = 'aura-voice-user-id';
    let userId = localStorage.getItem(storageKey);
    
    if (!userId) {
      userId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
      localStorage.setItem(storageKey, userId);
      console.log('[VA-Integration] Generated new user ID:', userId);
    } else {
      console.log('[VA-Integration] Retrieved existing user ID:', userId);
    }
    
    return userId;
  }

  /**
   * Load user profile from memory system
   */
  async loadUserProfile() {
    try {
      console.log('[VA-Integration] Loading user profile...');
      
      const response = await fetch(`${this.backendUrl}/api/memory/profile/${encodeURIComponent(this.userId)}`);
      
      if (response.ok) {
        const profile = await response.json();
        console.log('[VA-Integration] User profile loaded:', profile);
        
        // Display welcome message if user has a name
        if (profile.persona && profile.persona.length > 0) {
          const names = profile.persona.filter(p => /^[A-Z][a-z]+$/.test(p));
          if (names.length > 0) {
            const name = names[0];
            console.log('[VA-Integration] Welcome back message for:', name);
            this.displayWelcomeMessage(name, profile);
          }
        }
        
        this.userProfile = profile;
      } else {
        console.log('[VA-Integration] No existing profile found for user');
      }
    } catch (error) {
      console.error('[VA-Integration] Error loading user profile:', error);
    }
  }

  /**
   * Display personalized welcome message
   */
  displayWelcomeMessage(name, profile) {
    let message = `Welcome back, ${name}!`;
    
    if (profile.preferences && profile.preferences.length > 0) {
      const recentPreference = profile.preferences[0];
      message += ` I remember you like ${recentPreference}.`;
    }
    
    if (profile.goals && profile.goals.length > 0) {
      const recentGoal = profile.goals[0];
      message += ` Last time you were looking for ${recentGoal}.`;
    }
    
    // Display the welcome message briefly
    this.ui.updateChatBubbleMessage(message);
    
    // Speak the welcome message
    setTimeout(() => {
      this.speakText(`Welcome back, ${name}! How can I help you today?`).catch(error => {
        console.warn('[VA-Integration] Welcome TTS failed:', error);
      });
    }, 1000);
  }

  /**
   * Set up text query handler for typed queries
   */
  setupTextQueryHandler() {
    document.addEventListener('voice-assistant-text-query', (event) => {
      const { text } = event.detail;
      if (text && text.trim()) {
        console.log('[VA-Integration] Processing text query:', text);
        this.debouncedProcessTextQuery(text.trim());
      }
    });
  }

  /**
   * Set up audio stop handler for when recording starts
   */
  setupAudioStopHandler() {
    document.addEventListener('voice-assistant-stop-audio', () => {
      console.log('[VA-Integration] Received stop audio event');
      this.stopCurrentAudio();
      this.stopCurrentSpeech();
    });
  }

  /**
   * Debounced version of processTextQuery to prevent rapid-fire requests
   */
  debouncedProcessTextQuery(text) {
    if (this.debounceTimeout) {
      clearTimeout(this.debounceTimeout);
    }
    
    this.debounceTimeout = setTimeout(() => {
      this.processTextQuery(text);
    }, 300); // 300ms debounce
  }

  /**
   * Process text query (non-audio input)
   */
  async processTextQuery(text) {
    try {
      console.log('[VA-Integration] Processing text query:', text);
      
      // SILENCE ASSISTANT: Stop any ongoing audio/speech when new command comes in
      this.stopCurrentAudio();
      this.stopCurrentSpeech();
      
      // Prevent duplicate requests
      if (this.isProcessing) {
        console.log('[VA-Integration] Already processing a request, ignoring duplicate');
        return;
      }

      // Check for recent duplicate requests
      const now = Date.now();
      if (this.lastResponse === text && (now - this.lastResponseTime) < 3000) {
        console.log('[VA-Integration] Duplicate request detected, ignoring');
        return;
      }

      this.isProcessing = true;
      this.lastResponseTime = now;
      
      // Show loading state
      this.ui.setLoading(true);
      this.ui.updateChatBubbleMessage('Processing...');
      
      // Add user message to conversation
      this.ui.addMessage(text, 'user');
      
      // Gather page context for tool execution
      const pageContext = this.gatherPageContext();
      
      // Prepare request payload for text query
      const payload = {
        text: text, // Send text instead of audio
        elements: pageContext.elements,
        screenshot: pageContext.screenshot,
        sessionId: this.sessionId,
        storeId: this.shopDomain,
        enableMemory: this.enableMemory,
        enableKnowledge: this.enableKnowledge,
        participantIdentity: this.userId,
        shopCatalog: this.shopCatalog
      };
      
      console.log('[VA-Integration] Sending text query with context:', {
        textLength: text.length,
        elementsCount: pageContext.elements.length,
        hasScreenshot: !!pageContext.screenshot,
        sessionId: this.sessionId,
        participantIdentity: this.userId
      });

      // Send to backend for processing
      const response = await fetch(`${this.backendUrl}/nlu-enhanced`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });
      
      if (!response.ok) {
        throw new Error(`Backend request failed: ${response.status}`);
      }
      
      const result = await response.json();

      console.log('[VA-Integration] Received text query response:', {
        text: result.text,
        toolCallsCount: result.toolCalls?.length || 0,
        error: result.error
      });
      
      // Check if we need to capture screenshot for browser automation
      const needsScreenshot = result.toolCalls?.some(call => call.tool === 'browserAutomation');
      
      if (needsScreenshot) {
        console.log('[VA-Integration] Browser automation detected, capturing screenshot...');
        
        try {
          // Capture real screenshot
          const screenshot = await this.captureScreenshot();
          
          if (screenshot) {
            console.log('[VA-Integration] Screenshot captured, re-sending with screenshot...');
            
            // Re-send request with real screenshot
            const payloadWithScreenshot = {
              ...payload,
              screenshot: screenshot
            };
            
            const screenshotResponse = await fetch(`${this.backendUrl}/nlu-enhanced`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(payloadWithScreenshot)
            });
            
            if (screenshotResponse.ok) {
              const screenshotResult = await screenshotResponse.json();
              console.log('[VA-Integration] Received response with screenshot:', screenshotResult);
              
              // Use the result with screenshot
              await this.handleResponse(screenshotResult);
              return;
            } else {
              console.warn('[VA-Integration] Screenshot request failed, using original result');
            }
          } else {
            console.warn('[VA-Integration] Screenshot capture failed, using original result');
          }
        } catch (screenshotError) {
          console.error('[VA-Integration] Screenshot capture error:', screenshotError);
        }
      }
      
      // Record product or collection interests if provided
      if (Array.isArray(result.productSuggestions)) {
        for (const prod of result.productSuggestions) {
          if (prod && prod.handle) {
            this.recordInterest('product', prod.handle).catch(() => {});
          }
        }
      }
      
      // Execute any tool calls
      if (result.toolCalls && result.toolCalls.length > 0) {
        console.log('[VA-Integration] Executing tool calls:', result.toolCalls);
        
        for (const toolCall of result.toolCalls) {
          await this.executeToolCall(toolCall);
        }
      }
      
      // Handle the response
      await this.handleResponse(result);
      
    } catch (error) {
      console.error('[VA-Integration] Error processing text query:', error);
      this.handleError(`Processing failed: ${error.message}`);
    } finally {
      this.ui.setLoading(false);
      this.isProcessing = false;
    }
  }

  /**
   * Record interest for a product or collection
   */
  async recordInterest(type, handle) {
    try {
      console.log(`[VA-Integration] Recording interest for ${type}: ${handle}`);
      
      const response = await fetch(`${this.backendUrl}/recordInterest`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          handle,
          userId: this.userId,
          sessionId: this.sessionId,
          storeId: this.shopDomain,
          enableMemory: this.enableMemory,
          enableKnowledge: this.enableKnowledge
        })
      });
      
      if (!response.ok) {
        throw new Error(`Interest recording failed: ${response.status}`);
      }
      
      const result = await response.json();
      
      console.log('[VA-Integration] Interest recorded:', result);
    } catch (error) {
      console.error('[VA-Integration] Error recording interest:', error);
      this.handleError(`Recording interest failed: ${error.message}`);
    }
  }
}

// Expose to window for compatibility
window.VoiceAssistantIntegration = VoiceAssistantIntegration;
