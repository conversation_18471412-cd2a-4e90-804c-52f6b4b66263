{% schema %}
{
  "name": "Voice AI Assistant",
  "target": "body",
  "stylesheet": "voice-assistant.css",

  "settings": [
    {
      "type": "checkbox",
      "id": "enabled",
      "label": "Enable Voice Assistant",
      "default": true
    },
    {
      "type": "select",
      "id": "position",
      "label": "Assistant Position",
      "options": [
        {
          "value": "bottom-right",
          "label": "Bottom Right"
        },
        {
          "value": "bottom-left",
          "label": "Bottom Left"
        }
      ],
      "default": "bottom-right"
    },
    {
      "type": "color",
      "id": "assistant_color",
      "label": "Assistant Color",
      "default": "#000000"
    }
  ]
}
{% endschema %}

<script src="https://cdn.jsdelivr.net/npm/libflac.js@1.0.3/dist/libflac.min.js" defer></script>

{%- comment -%} Lightweight shop catalog context for client-side assistant awareness {%- endcomment -%}
<script type="application/json" id="shop-catalog" data-limit="50">
  {
    "collections": [
      {%- for collection in collections limit: 20 -%}
        { "handle": "{{ collection.handle }}", "title": {{ collection.title | json }} }{%- unless forloop.last -%},{%- endunless -%}
      {%- endfor -%}
    ],
    "products": [
      {%- for product in all_products limit: 30 -%}
        { "handle": "{{ product.handle }}", "title": {{ product.title | json }}, "price": {{ product.price | money_without_currency }} }{%- unless forloop.last -%},{%- endunless -%}
      {%- endfor -%}
    ]
  }
</script>

<script>
  // Store the full bundle URL in a global variable
  window.voiceAssistantBundleUrl = "{{ 'voice-assistant-bundle.js' | asset_url }}";
  
  // Load enhanced product UI after bundle
  window.enhancedProductUIUrl = "{{ 'enhanced-product-ui.js' | asset_url }}";
</script>

{% comment %} Render the main UI snippet {% endcomment %}
{% render 'voice-assistant', block_settings: block.settings %}

{% comment %}
  REMOVED Manual Script Loading Block
{% endcomment %}