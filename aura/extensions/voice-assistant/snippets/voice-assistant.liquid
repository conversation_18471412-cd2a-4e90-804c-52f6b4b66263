{% comment %} Use the explicitly passed settings object {% endcomment %}
{% if block_settings.enabled %}
  <div id="voice-assistant" 
       data-shop-domain="{{ shop.domain }}"
       data-position="{{ block_settings.position }}"
       data-color="{{ block_settings.assistant_color }}">
    <!-- Visualizer Container -->
    <div class="voice-assistant-visualizer-container">
      <!-- Floating Mic <PERSON> -->
      <button class="voice-assistant-floating-mic" aria-label="Record audio for instant analysis" tabindex="0">
        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="12" fill="#fff" opacity="0.8"/>
          <path d="M12 15.5c1.3 0 2.5-1.13 2.5-2.5V8a2.5 2.5 0 10-5 0v5c0 1.37 1.2 2.5 2.5 2.5z" fill="#8b5cf6"/>
          <path d="M16 11.5V13c0 2.21-1.79 4-4 4s-4-1.79-4-4v-1.5" stroke="#8b5cf6" stroke-width="1.5" stroke-linecap="round"/>
          <rect x="11" y="18" width="2" height="3" rx="1" fill="#8b5cf6"/>
        </svg>
      </button>
      <!-- Visualizer Canvas -->
      <div class="voice-assistant-visualizer">
         <!-- Shopping bag icon overlay -->
        <div class="voice-assistant-icon-overlay">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z" stroke="currentColor" stroke-width="2" fill="none"/>
            <line x1="3" y1="6" x2="21" y2="6" stroke="currentColor" stroke-width="2"/>
            <path d="m16 10a4 4 0 0 1-8 0" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
        </div>

        <canvas id="voice-assistant-canvas" width="280" height="280"></canvas>
        
     
        
        <div id="voice-assistant-loading" class="voice-assistant-loading hidden">
       
        </div>
      </div>
      
      <!-- Chat Messages -->
      <div class="voice-assistant-chat-bubble-container">
        <div class="voice-assistant-chat-bubble">
          <div id="voice-assistant-chat-message">How can I help you shop today?</div>
        </div>
      </div>
      
      <!-- Assistant Name -->
      <div class="voice-assistant-name-display">
        <h2 id="voice-assistant-name">Shopping Assistant</h2>
      </div>
    </div>
    
    <!-- Dialog Modal (remains the same) -->
    <div class="voice-assistant-modal">
      <div class="voice-assistant-header">
        <h2>Voice Shopping Assistant</h2>
        <button class="voice-assistant-close" aria-label="Close voice assistant">×</button>
      </div>
      <div class="voice-assistant-content">
        <div class="voice-assistant-messages">
          <div class="message assistant">
            How can I help you shop today?
          </div>
        </div>
        <div class="voice-assistant-controls">
          <button class="voice-assistant-record">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 15C13.66 15 15 13.66 15 12V6C15 4.34 13.66 3 12 3C10.34 3 9 4.34 9 6V12C9 13.66 10.34 15 12 15Z" fill="currentColor"/>
              <path d="M17 12C17 14.76 14.76 17 12 17C9.24 17 7 14.76 7 12H5C5 15.53 7.61 18.43 11 18.93V21H13V18.93C16.39 18.43 19 15.53 19 12H17Z" fill="currentColor"/>
            </svg>
            <span>Tap to speak</span>
          </button>
        </div>
      </div>
    </div>
  </div>
  <link rel="stylesheet" href="{{ 'voice-assistant.css' | asset_url }}">
  <script src="{{ 'voice-assistant-bundle.js' | asset_url }}"></script>

  <script id="shop-catalog" type="application/json">
    {
      "products": [
        {% for product in collections.all.products limit: 50 %}
          {
            "handle": "{{ product.handle }}",
            "title": "{{ product.title | escape }}"
          }{% unless forloop.last %},{% endunless %}
        {% endfor %}
      ],
      "collections": [
        {% for collection in collections limit: 50 %}
          {
            "handle": "{{ collection.handle }}",
            "title": "{{ collection.title | escape }}"
          }{% unless forloop.last %},{% endunless %}
        {% endfor %}
      ]
    }
  </script>
{% endif %}
