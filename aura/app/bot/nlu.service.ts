/**
 * NLU Service
 * 
 * Handles interaction with the Natural Language Understanding model (Ultravox via Replicate).
 */
import { Buffer } from 'buffer';

// Define configuration interface specific to this service
interface NluConfig {
  replicateApiToken?: string; 
}

// Define the expected structure for NLU results
export interface NluResult {
    text: string;
    toolCalls: any[]; // Define a more specific type based on expected tool call structure
    error?: string; // Optional error message
}

interface NluContext {
  participantIdentity: string;
  roomName: string;
}

export class NluService {
  private config: NluConfig;
  private geminiApiKey: string;
  private geminiApiBase: string = 'https://generativelanguage.googleapis.com/v1beta';

  constructor() {
    const replicateToken = process.env.REPLICATE_API_TOKEN;
    // TODO: Integrate with Gemini or backend-worker for NLU
    
    this.config = {
        replicateApiToken: replicateToken,
    };

    // Cloudflare Workers: Use environment variable for Gemini API key
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('GEMINI_API_KEY environment variable is required');
    }
    this.geminiApiKey = process.env.GEMINI_API_KEY;

    console.log("[NluService] Initialized.");
  }

  /**
   * Sends buffered audio data to Replicate (Ultravox) for NLU processing.
   * @param audioBuffer The complete audio buffer.
   * @param participantIdentity The identity of the speaker.
   * @param roomName The room name.
   * @param sampleRate The audio sample rate.
   * @param channels The number of audio channels.
   * @returns {Promise<NluResult>} The parsed NLU result or an error indication.
   */
  async processAudio(
    audioBuffer: Uint8Array,
    sampleRate: number,
    channels: number,
    context: NluContext
  ): Promise<NluResult> {
    try {
      // 1. Encode audio as WAV (Uint8Array)
      const wavBytes = this.encodeWav(audioBuffer, sampleRate, channels);

      // 2. Upload audio to Gemini Files API
      const fileUri = await this.uploadAudioToGemini(wavBytes);
      if (!fileUri) throw new Error('Failed to upload audio to Gemini Files API');

      // 3. Compose multimodal prompt and call Gemini generateContent
      const prompt = [
        { text: 'You are a helpful shopping assistant. Transcribe and understand the user audio.' },
        { fileData: { mimeType: 'audio/wav', fileUri } }
      ];
      const geminiResponse = await this.generateContent(prompt);

      // 4. Parse Gemini response into NluResult
      const text = geminiResponse?.candidates?.[0]?.content?.parts?.[0]?.text || '';
      // TODO: Parse toolCalls if Gemini supports function calling in this context
      return { text, toolCalls: [] };
    } catch (error: any) {
      return { text: '', toolCalls: [], error: error.message || String(error) };
    }
  }

  /**
   * Encode PCM audio as WAV (Uint8Array, browser/edge compatible)
   */
  private encodeWav(pcm: Uint8Array, sampleRate: number, channels: number): Uint8Array {
    // 16-bit PCM WAV header
    const bitsPerSample = 16;
    const byteRate = sampleRate * channels * (bitsPerSample / 8);
    const blockAlign = channels * (bitsPerSample / 8);
    const dataSize = pcm.length;
    const buffer = new Uint8Array(44 + dataSize);
    const view = new DataView(buffer.buffer);
    // RIFF header
    this.writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + dataSize, true);
    this.writeString(view, 8, 'WAVE');
    this.writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true); // Subchunk1Size
    view.setUint16(20, 1, true); // AudioFormat (PCM)
    view.setUint16(22, channels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitsPerSample, true);
    this.writeString(view, 36, 'data');
    view.setUint32(40, dataSize, true);
    // PCM data
    buffer.set(pcm, 44);
    return buffer;
  }

  private writeString(view: DataView, offset: number, str: string) {
    for (let i = 0; i < str.length; i++) {
      view.setUint8(offset + i, str.charCodeAt(i));
    }
  }

  /**
   * Upload audio to Gemini Files API, return fileUri
   */
  private async uploadAudioToGemini(wavBytes: Uint8Array): Promise<string | null> {
    const url = `${this.geminiApiBase}/files?key=${this.geminiApiKey}`;
    const resp = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'audio/wav',
        'Authorization': `Bearer ${this.geminiApiKey}`
      },
      body: wavBytes
    });
    if (!resp.ok) {
      console.error('Gemini Files API upload failed', await resp.text());
      return null;
    }
    const data = await resp.json();
    return data.fileUri || null;
  }

  /**
   * Call Gemini generateContent with multimodal prompt
   */
  private async generateContent(prompt: any[]): Promise<any> {
    const url = `${this.geminiApiBase}/models/gemini-1.5-pro-latest:generateContent?key=${this.geminiApiKey}`;
    const body = {
      contents: [
        {
          role: 'user',
          parts: prompt
        }
      ]
    };
    const resp = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.geminiApiKey}`
      },
      body: JSON.stringify(body)
    });
    if (!resp.ok) {
      console.error('Gemini generateContent failed', await resp.text());
      throw new Error('Gemini generateContent failed');
    }
    return await resp.json();
  }
}

// --- Service Instantiation --- (Singleton pattern)
let nluServiceInstance: NluService | null = null;

export function getNluServiceInstance(): NluService {
  if (!nluServiceInstance) {
    try {
      nluServiceInstance = new NluService();
    } catch (error) {
      console.error("Failed to initialize NluService:", error);
      throw error; 
    }
  }
  return nluServiceInstance;
}