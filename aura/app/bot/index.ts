/**
 * Bot Services Entry Point
 * 
 * Exports functions to initialize and interact with the bot services.
 */

import { getLiveKitBotServiceInstance } from './livekit.service';
import { getAudioProcessorServiceInstance } from './audio.processor';
import { getNluServiceInstance } from './nlu.service';
import { getTtsServiceInstance } from './tts.service';
import { getBotOrchestratorInstance, initializeBotServices, triggerBotJoin, triggerBotLeave } from './bot.orchestrator';

// Ensure all services are potentially initialized when this module is loaded,
// especially if they rely on singleton patterns.
// Calling the getter functions usually handles the initialization.
getLiveKitBotServiceInstance();
getAudioProcessorServiceInstance();
getNluServiceInstance();
getTtsServiceInstance();
getBotOrchestratorInstance(); 

// Export the main control functions
export {
    initializeBotServices,
    triggerBotJoin,
    triggerBotLeave,
    // Export service instance getters for direct access when needed
    getLiveKitBotServiceInstance, 
    getAudioProcessorServiceInstance,
    getNluServiceInstance,
    getTtsServiceInstance,
    getBotOrchestratorInstance
};