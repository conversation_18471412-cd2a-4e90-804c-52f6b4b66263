import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { authenticateVoiceAssistantLoader } from "../utils/voice-assistant-auth";

export const loader = async (args: LoaderFunctionArgs) => {
  const session = await authenticateVoiceAssistantLoader(args);
  
  // Get the app URL - this is where our app is hosted
  const appHost = process.env.SHOPIFY_APP_URL || 'http://localhost:3000';
  
  // Return configuration
  return json({
    // Instead of connecting directly to LiveKit, connect to our relay
    eventsEndpoint: "/api/voice-assistant/ws",
    audioEndpoint: "/api/voice-assistant/audio",
    apiEndpoint: "/api/voice-assistant",
    shop: session.shop
  }, {
    headers: {
      // Allow the theme extension to access this config
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET",
      "Access-Control-Allow-Headers": "Content-Type"
    }
  });
};