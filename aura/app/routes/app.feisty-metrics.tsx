import { useEffect, useState } from "react";
import type { LoaderFunctionArgs } from "@remix-run/node";
import { json } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import {
  Page,
  Layout,
  Card,
  Text,
  BlockStack,
  DataTable,
} from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { authenticate } from "../shopify.server";

interface LoaderData {
  summary: {
    crPercent: number;
    voiceRevenue: number;
    events: { event: string; revenue: number; timestamp: number; channel: string }[];
  };
  backendUrl: string;
  shop: string;
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session } = await authenticate.admin(request);
  const backendUrl = process.env.BACKEND_WORKER_URL || "";
  const res = await fetch(`${backendUrl}/v1/stats/summary?store=${session.shop}`);
  const summary = await res.json();
  return json<LoaderData>({ summary, backendUrl, shop: session.shop });
};

export default function FeistyMetrics() {
  const { summary, backendUrl, shop } = useLoaderData<LoaderData>();
  const [data, setData] = useState(summary);

  useEffect(() => {
    const interval = setInterval(async () => {
      const resp = await fetch(`${backendUrl}/v1/stats/summary?store=${shop}`);
      const json = await resp.json();
      setData(json);
    }, 30000);
    return () => clearInterval(interval);
  }, [backendUrl, shop]);

  const rows = data.events.map((e) => [
    new Date(e.timestamp * 1000).toLocaleString(),
    e.event,
    e.channel,
    e.revenue?.toFixed(2) || "0.00",
  ]);

  return (
    <Page>
      <TitleBar title="Feisty Metrics" />
      <Layout>
        <Layout.Section>
          <Card>
            <BlockStack gap="200">
              <Text variant="headingMd" as="h2">
                Today
              </Text>
              <Text as="p">Conversion Rate: {data.crPercent.toFixed(2)}%</Text>
              <Text as="p">Voice Revenue: ${data.voiceRevenue.toFixed(2)}</Text>
            </BlockStack>
          </Card>
        </Layout.Section>
        <Layout.Section>
          <Card>
            <DataTable
              columnContentTypes={["text", "text", "text", "numeric"]}
              headings={["Time", "Event", "Channel", "Revenue"]}
              rows={rows}
            />
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
