import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticateVoiceAssistantRequest } from "../utils/voice-assistant-auth";
import { triggerBotJoin } from "../bot";

// This endpoint is for receiving audio data from the frontend via AJAX
// Uses the new Bot Service architecture (direct LiveKit connection)
// Path: /api/voice-assistant/audio in production

export const action = async ({ request }: ActionFunctionArgs) => {
  // Try to authenticate the request
  try {
    // With the new architecture, this is less critical since most audio
    // will flow directly to LiveKit, but we keep this endpoint for compatibility
    await authenticateVoiceAssistantRequest(request);
  } catch (e) {
    // Authentication failed, but we'll continue for compatibility with app proxy
    console.log('Authentication failed, may be app proxy request');
  }
  
  console.log('Audio endpoint request received from:', request.url);
  
  if (request.method !== 'POST') {
    return json({ error: 'Method not allowed' }, { status: 405 });
  }
  
  try {
    const { audio, shopDomain, requestId } = await request.json();
    
    if (!audio) {
      return json({ error: 'Audio data is required' }, { status: 400 });
    }
    
    if (!shopDomain) {
      return json({ error: 'Shop domain is required' }, { status: 400 });
    }
    
    // Generate a request ID if one wasn't provided
    const finalRequestId = requestId || `req-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    
    // With the new Bot Service architecture, this endpoint should be used less
    // as audio is sent directly to LiveKit from the frontend.
    // However, we maintain compatibility for legacy clients or fallback scenarios.
    
    console.log(`[MIGRATION NOTE] Request to legacy audio endpoint received. Using new Bot Service architecture.`);
    
    // Use the new Bot Service architecture - trigger bot to join the room for this shop
    // This ensures the bot is ready to process audio for this shop
    const roomName = `room-${shopDomain}`;
    console.log(`Triggering bot to join room: ${roomName}`);
    
    try {
      // Try to make the bot join the room
      await triggerBotJoin(roomName);
      
      // For now, we just return success - the client should be using 
      // direct LiveKit connection for audio streaming
      return json({ 
        status: 'processing',
        message: 'Bot service activated for this shop. Please use direct LiveKit connection for audio streaming.',
        requestId: finalRequestId,
        notice: 'DEPRECATED: This endpoint is deprecated. Please use direct LiveKit connection with LiveKit tokens.'
      });
    } catch (error) {
      console.error('Error triggering bot join:', error);
      return json({ 
        error: 'Failed to activate bot service',
        status: 'error',
        requestId: finalRequestId
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('Error processing audio endpoint request:', error);
    return json({ 
      error: 'Internal server error', 
      status: 'error'
    }, { status: 500 });
  }
};