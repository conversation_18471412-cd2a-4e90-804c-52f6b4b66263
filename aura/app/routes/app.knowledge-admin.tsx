import type { LoaderFunctionArgs, ActionFunctionArgs } from '@remix-run/node';
import { json } from '@remix-run/node';
import { useLoaderData, useFetcher, useActionData } from '@remix-run/react';
import {
  Page,
  Layout,
  Card,
  DataTable,
  <PERSON><PERSON>,
  Banner,
  Spinner
} from '@shopify/polaris';
import { useState, useCallback } from 'react';

interface KnowledgeFile {
  key: string;
  size: number;
  lastModified: string;
}

interface LoaderData {
  files: KnowledgeFile[];
  storeId: string;
  error?: string;
}

export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    const storeId = 'demo-store';
    const backendUrl = process.env.BACKEND_WORKER_URL || 'https://aura-backend-worker.your-subdomain.workers.dev';
    const response = await fetch(`${backendUrl}/api/knowledge/files/${storeId}`);
    
    if (!response.ok) {
      throw new Error(`Failed to fetch files: ${response.status}`);
    }
    
    const data = await response.json();
    
    return json<LoaderData>({
      files: data.files || [],
      storeId,
    });
  } catch (error: any) {
    return json<LoaderData>({
      files: [],
      storeId: 'demo-store',
      error: error.message,
    });
  }
};

export const action = async ({ request }: ActionFunctionArgs) => {
  const formData = await request.formData();
  const actionType = formData.get('actionType') as string;
  const storeId = formData.get('storeId') as string;
  
  const backendUrl = process.env.BACKEND_WORKER_URL || 'https://aura-backend-worker.your-subdomain.workers.dev';

  try {
    switch (actionType) {
      case 'sync': {
        const shopifyDomain = 'feistyagency.myshopify.com';
        const accessToken = 'your-access-token';
        
        const response = await fetch(`${backendUrl}/api/knowledge/sync`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            storeId,
            shopifyDomain,
            accessToken,
          }),
        });

        const result = await response.json();
        return json(result);
      }

      default:
        return json({ error: 'Unknown action' }, { status: 400 });
    }
  } catch (error: any) {
    return json({ error: error.message }, { status: 500 });
  }
};

export default function KnowledgeAdmin() {
  const { files, storeId, error } = useLoaderData<LoaderData>();
  const actionData = useActionData<any>();
  const syncFetcher = useFetcher();

  const handleSync = () => {
    const formData = new FormData();
    formData.append('actionType', 'sync');
    formData.append('storeId', storeId);

    syncFetcher.submit(formData, { method: 'post' });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const tableRows = files.map((file) => [
    file.key.split('/').pop() || file.key,
    formatFileSize(file.size),
    new Date(file.lastModified).toLocaleDateString(),
    'Indexed'
  ]);

  return (
    <Page title="AI Assistant Knowledge Base">
      <Layout>
        <Layout.Section>
          {error && (
            <Banner title="Error loading knowledge base">
              <p>{error}</p>
            </Banner>
          )}

          {actionData?.error && (
            <Banner title="Action failed">
              <p>{actionData.error}</p>
            </Banner>
          )}

          {actionData?.success && (
            <Banner title="Success">
              <p>Operation completed successfully</p>
            </Banner>
          )}
        </Layout.Section>

        <Layout.Section>
          <Card>
            <div style={{ padding: '20px' }}>
              <h2 style={{ marginBottom: '20px' }}>Store Content Sync</h2>
              <p style={{ marginBottom: '15px', color: '#666' }}>
                Sync your Shopify pages and policies to the AI assistant's knowledge base.
              </p>
              <Button
                primary
                onClick={handleSync}
                loading={syncFetcher.state === 'submitting'}
              >
                {syncFetcher.state === 'submitting' ? 'Syncing...' : 'Sync Store Content'}
              </Button>
            </div>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <div style={{ padding: '20px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
                <h2>Knowledge Base Files</h2>
                {syncFetcher.state === 'submitting' && <Spinner size="small" />}
              </div>
              
              {files.length === 0 ? (
                <p style={{ color: '#666', textAlign: 'center', padding: '40px' }}>
                  No files in knowledge base. Sync store content to get started.
                </p>
              ) : (
                <DataTable
                  columnContentTypes={['text', 'text', 'text', 'text']}
                  headings={['File Name', 'Size', 'Last Modified', 'Status']}
                  rows={tableRows}
                />
              )}
            </div>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <div style={{ padding: '20px' }}>
              <h2 style={{ marginBottom: '15px' }}>How It Works</h2>
              <ul style={{ paddingLeft: '20px', lineHeight: '1.6' }}>
                <li><strong>Store Content:</strong> Pages, policies, and product information from your Shopify store are automatically indexed.</li>
                <li><strong>Auto-Indexing:</strong> All content is automatically processed and made searchable for the AI assistant.</li>
                <li><strong>Memory:</strong> The AI remembers customer preferences and past interactions for personalized responses.</li>
                <li><strong>Real-time Updates:</strong> Content changes are reflected in the AI's knowledge base automatically.</li>
              </ul>
            </div>
          </Card>
        </Layout.Section>
      </Layout>
    </Page>
  );
}