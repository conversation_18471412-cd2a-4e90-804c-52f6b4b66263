import { json, type LoaderFunctionArgs } from "@remix-run/node";
// import { AccessToken } from "livekit-server-sdk"; // REMOVE
// import { authenticate } from "../shopify.server"; // Cannot use standard authenticate here

// Cloudflare Realtime token generation (manual implementation)
// Based on Cloudflare Realtime HTTP API documentation
function generateRealtimeToken(params: {
  apiKey: string;
  apiSecret: string;
  room: string;
  identity: string;
  grants: {
    roomJoin: boolean;
    canPublish: boolean;
    canSubscribe: boolean;
    canPublishData: boolean;
  };
  ttl: number;
}) {
  // For now, we'll generate a simple JWT-like token manually
  // In production, you'd want to use proper JWT libraries and follow Cloudflare's exact spec
  const payload = {
    room: params.room,
    identity: params.identity,
    grants: params.grants,
    exp: Math.floor(Date.now() / 1000) + params.ttl,
    iss: params.apiKey,
  };
  
  // Simple base64 encoding for demonstration
  // In production, use proper JWT signing with the apiSecret
  const token = btoa(JSON.stringify(payload));
  return token;
}

// Define CORS headers for direct API access - updated to handle all development origins
const CORS_HEADERS = {
  "Access-Control-Allow-Origin": "*", // Allow access from any origin (including Shopify storefronts and local development)
  "Access-Control-Allow-Methods": "GET, OPTIONS, POST",
  "Access-Control-Allow-Headers": "Content-Type, Authorization, Accept, Origin, X-Requested-With",
  "Access-Control-Max-Age": "86400", // Cache preflight requests for 24 hours
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  console.log(`[RealtimeKit Token] Processing request from origin: ${request.headers.get('origin') || 'Unknown'} - URL: ${url.toString()}`);

  // Handle OPTIONS preflight request for CORS
  if (request.method === 'OPTIONS') {
    console.log(`[RealtimeKit Token] Handling OPTIONS preflight request with CORS headers`);
    return new Response(null, { status: 204, headers: CORS_HEADERS });
  }

  // Ensure environment variables are set
  const realtimekitHost = process.env.REALTIMEKIT_URL;
  const realtimekitApiKey = process.env.REALTIMEKIT_API_KEY;
  const realtimekitApiSecret = process.env.REALTIMEKIT_API_SECRET;

  // Log environment variable diagnostic information (mask secrets)
  console.log("[RealtimeKit Token] Environment variables check:");
  console.log(`  REALTIMEKIT_URL: ${realtimekitHost ? "Set" : "NOT SET"}`);
  console.log(`  REALTIMEKIT_API_KEY: ${realtimekitApiKey ? "Set" : "NOT SET"}`);
  console.log(`  REALTIMEKIT_API_SECRET: ${realtimekitApiSecret ? "Set (value masked)" : "NOT SET"}`);

  if (!realtimekitHost || !realtimekitApiKey || !realtimekitApiSecret) {
    console.error("[RealtimeKit Token] Missing required environment variables");
    // More specific error for debugging
    const missingVars = [];
    if (!realtimekitHost) missingVars.push("REALTIMEKIT_URL");
    if (!realtimekitApiKey) missingVars.push("REALTIMEKIT_API_KEY");
    if (!realtimekitApiSecret) missingVars.push("REALTIMEKIT_API_SECRET");

    return json({ 
      error: "RealtimeKit configuration missing", 
      missingVariables: missingVars
    }, { 
      status: 500, 
      headers: {
        ...CORS_HEADERS,
        "Content-Type": "application/json"
      } 
    });
  }

  // --- Get Shop Domain --- 
  // This endpoint is called directly from the theme extension, bypassing standard auth.
  // We expect the shop domain to be passed as a query parameter.
  const shopDomain = url.searchParams.get("shop");

  if (!shopDomain) {
    console.warn("[RealtimeKit Token] Missing 'shop' query parameter.");
    return json({ 
      error: "Missing required shop parameter" 
    }, { 
      status: 400, 
      headers: {
        ...CORS_HEADERS,
        "Content-Type": "application/json"
      } 
    });
  }

  // Basic validation of shop domain format (optional but recommended)
  if (!/^[a-zA-Z0-9][a-zA-Z0-9\-]*\.myshopify\.com$/.test(shopDomain)) {
      console.warn(`[RealtimeKit Token] Invalid shop domain format received: ${shopDomain}`);
      return json({ error: "Invalid shop domain format" }, { status: 400, headers: CORS_HEADERS });
  }

  // --- Generate Participant Identity & Room Name ---
  const roomName = `voice-assistant-${shopDomain}`;
  const participantIdentity = `user-${shopDomain}-${Math.random().toString(36).substring(2, 12)}`;

  console.log(`[RealtimeKit Token] Generating token for Shop: ${shopDomain}, Room: ${roomName}, Participant: ${participantIdentity}`);

  // --- Create RealtimeKit Access Token --- 
  try {
    // Create the access token using manual implementation
    const token = generateRealtimeToken({
      apiKey: realtimekitApiKey,
      apiSecret: realtimekitApiSecret,
      room: roomName,
      identity: participantIdentity,
      grants: {
        roomJoin: true,
        canPublish: true,
        canSubscribe: true,
        canPublishData: true,
      },
      ttl: 15 * 60, // 15 minutes in seconds
    });

    // Return the RealtimeKit URL and the generated token
    const response = {
      realtimekitUrl: realtimekitHost, 
      token: token,
      roomName: roomName,
      identity: participantIdentity
    };
    console.log("[RealtimeKit Token] Returning response with token and connection info");
    return json(response, { 
      headers: {
        ...CORS_HEADERS,
        "Content-Type": "application/json"
      } 
    });
  } catch (error) {
    // Detailed error logging
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorStack = error instanceof Error ? error.stack : 'No stack trace available';
    console.error("[RealtimeKit Token] ⚠️ ERROR GENERATING TOKEN ⚠️");
    console.error(`Error message: ${errorMessage}`);
    console.error(`Stack trace: ${errorStack}`);
    // Log all context information for debugging
    console.error(`Context: Room=${roomName}, Participant=${participantIdentity}`);
    console.error(`RealtimeKit URL=${realtimekitHost}`);
    // Return a detailed error response
    return json({ 
      error: "Failed to generate RealtimeKit token", 
      details: errorMessage,
      context: {
        room: roomName,
        participantIdentity: participantIdentity,
        timestamp: new Date().toISOString()
      }
    }, { 
      status: 500, 
      headers: {
        ...CORS_HEADERS,
        "Content-Type": "application/json"
      } 
    });
  }
};