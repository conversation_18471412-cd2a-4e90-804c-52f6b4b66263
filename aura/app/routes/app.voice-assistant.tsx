import { useEffect, useState, useCallback } from "react";
import {
  Page,
  Layout,
  BlockStack,
  Text,
  Banner,
  Button,
  Form,
  FormLayout,
  TextField,
  Select,
  PageActions,
  ColorPicker,
  hsbToRgb,
  rgbToHsb,
  rgbString,
  ContextualSaveBar,
  Spinner,
  Box,
} from "@shopify/polaris";
import { LegacyCard as Card } from "@shopify/polaris";
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { authenticate } from "../shopify.server";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { session, admin } = await authenticate.admin(request);
  
  // Cart data functionality now handled by the Bot Service architecture
  // We no longer need to fetch cart data here via the deprecated proxy
  
  // Set up the metafield with the config URL
  const appHost = process.env.SHOPIFY_APP_URL || 'http://localhost:3000';
  const configUrl = `${appHost}/api/voice-assistant/config`;
  
  try {
    // Set a metafield that the theme extension can access
    await admin.graphql(`
      mutation MetafieldsSet($metafields: [MetafieldsSetInput!]!) {
        metafieldsSet(metafields: $metafields) {
          metafields {
            id
            namespace
            key
            value
          }
          userErrors {
            field
            message
          }
        }
      }
    `, {
      variables: {
        metafields: [
          {
            ownerId: `gid://shopify/App/${process.env.SHOPIFY_VOICE_ASSISTANT_ID}`,
            namespace: "voice_assistant",
            key: "config_url",
            value: configUrl,
            type: "single_line_text_field"
          }
        ]
      }
    });
  } catch (error) {
    console.error("Error setting metafield:", error);
  }
  
  return json({
    shop: session.shop,
    liveKitUrl: process.env.LIVEKIT_URL || "ws://localhost:7880",
    liveKitKey: process.env.LIVEKIT_KEY || "",
    // Cart data is now handled by the Bot Service architecture
    cartData: { items: [] }, // Provide an empty cart for backward compatibility
    configUrl
  });
};

// --- HACKY REALTIMEKIT HOOK (to be replaced by official SDK) ---
import { useRef } from 'react';

function useHackyRealtimeKit({ whipUrl, onTextResponse }: { whipUrl: string, onTextResponse: (msg: string) => void }) {
  const pcRef = useRef<RTCPeerConnection | null>(null);
  const dcRef = useRef<RTCDataChannel | null>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const start = async () => {
    // 1. Create PeerConnection
    const pc = new RTCPeerConnection({
      iceServers: [{ urls: 'stun:stun.cloudflare.com:3478' }],
    });
    pcRef.current = pc;

    // 2. Open Data Channel for receiving text
    const dc = pc.createDataChannel('text');
    dcRef.current = dc;
    dc.onmessage = (event) => {
      if (typeof event.data === 'string') {
        try {
          const data = JSON.parse(event.data);
          if (data.type === 'ai-response' && data.text) {
            onTextResponse(data.text);
          }
        } catch {
          // fallback: just show raw text
          onTextResponse(event.data);
        }
      }
    };

    // 3. Get audio and add to connection
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    streamRef.current = stream;
    stream.getAudioTracks().forEach(track => pc.addTrack(track, stream));

    // 4. Create offer and send to WHIP endpoint
    const offer = await pc.createOffer();
    await pc.setLocalDescription(offer);
    const response = await fetch(whipUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/sdp' },
      body: offer.sdp,
    });
    if (!response.ok) throw new Error('Failed to connect to WHIP endpoint');
    const answerSdp = await response.text();
    await pc.setRemoteDescription({ type: 'answer', sdp: answerSdp });
  };

  const stop = () => {
    if (dcRef.current) dcRef.current.close();
    if (pcRef.current) pcRef.current.close();
    if (streamRef.current) streamRef.current.getTracks().forEach(t => t.stop());
    dcRef.current = null;
    pcRef.current = null;
    streamRef.current = null;
  };

  return { start, stop };
}
// --- END HACKY HOOK ---

export default function VoiceAssistantSettings() {
  const { shop, liveKitUrl, liveKitKey, cartData, configUrl } = useLoaderData<typeof loader>();
  
  const [settings, setSettings] = useState({
    assistantName: "Shopping Assistant",
    welcomeMessage: "How can I help you shop today?",
    position: "bottom-right",
    color: {
      hue: 0,
      brightness: 0,
      saturation: 0,
    },
    hasChanges: false,
  });

  const [isSaving, setIsSaving] = useState(false);
  const [savedSuccess, setSavedSuccess] = useState(false);
  const [audioStreamActive, setAudioStreamActive] = useState(false);
  const [testingMode, setTestingMode] = useState(false);
  const [responseMessage, setResponseMessage] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [wsConnection, setWsConnection] = useState<WebSocket | null>(null);
  const [audioContext, setAudioContext] = useState<AudioContext | null>(null);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const [audioStream, setAudioStream] = useState<MediaStream | null>(null);

  // --- HACKY REALTIMEKIT HOOK (to be replaced by official SDK) ---
  const [hackyKit, setHackyKit] = useState<any>(null);

  const startAudioStream = useCallback(async () => {
    const kit = useHackyRealtimeKit({
      whipUrl: liveKitUrl, // or the correct WHIP endpoint
      onTextResponse: (msg: string) => {
        setResponseMessage(msg);
        setIsProcessing(false);
      },
    });
    setHackyKit(kit);
    setAudioStreamActive(true);
    setIsProcessing(true);
    setResponseMessage('Listening...');
    await kit.start();
  }, [liveKitUrl]);

  const stopAudioStream = useCallback(() => {
    if (hackyKit) hackyKit.stop();
    setHackyKit(null);
    setAudioStreamActive(false);
    setIsProcessing(false);
  }, [hackyKit]);

  const handleChange = (field: string) => (value: string) => {
    setSettings((prev) => ({
      ...prev,
      [field]: value,
      hasChanges: true,
    }));
  };

  const handleColorChange = (color: { hue: number; brightness: number; saturation: number }) => {
    setSettings((prev) => ({
      ...prev,
      color,
      hasChanges: true,
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      // In a real implementation, you would save these settings to your API
      await new Promise((resolve) => setTimeout(resolve, 1000));
      
      setSettings((prev) => ({
        ...prev,
        hasChanges: false,
      }));
      
      setSavedSuccess(true);
      setTimeout(() => setSavedSuccess(false), 3000);
    } catch (error) {
      console.error("Error saving settings:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDiscardChanges = () => {
    // Reset to the last saved state
    // In a real implementation, you would reload from your API
    setSettings((prev) => ({
      ...prev,
      hasChanges: false,
    }));
  };

  const positionOptions = [
    { label: "Bottom Right", value: "bottom-right" },
    { label: "Bottom Left", value: "bottom-left" },
  ];

  const colorString = rgbString(hsbToRgb(settings.color));

  return (
    <Page
      title="Voice Assistant Settings"
      subtitle="Configure how the voice assistant appears and functions on your storefront"
    >
      {settings.hasChanges && (
        <ContextualSaveBar
          message="Unsaved changes"
          saveAction={{
            onAction: handleSave,
            loading: isSaving,
            disabled: isSaving,
          }}
          discardAction={{
            onAction: handleDiscardChanges,
          }}
        />
      )}

      {savedSuccess && (
        <Banner
          title="Settings saved"
          tone="success"
          onDismiss={() => setSavedSuccess(false)}
        />
      )}

      <Layout>
        <Layout.Section>
          <Card>
            <Card.Section>
              <Form onSubmit={handleSave}>
                <FormLayout>
                  <TextField
                    label="Assistant Name"
                    value={settings.assistantName}
                    onChange={handleChange("assistantName")}
                    autoComplete="off"
                  />
                  
                  <TextField
                    label="Welcome Message"
                    value={settings.welcomeMessage}
                    onChange={handleChange("welcomeMessage")}
                    autoComplete="off"
                    multiline={3}
                  />
                  
                  <Select
                    label="Position"
                    options={positionOptions}
                    value={settings.position}
                    onChange={handleChange("position")}
                  />
                  
                  <BlockStack gap="400">
                    <Text variant="bodyMd" as="p">
                      Assistant Color
                    </Text>
                    <div style={{ display: "flex", alignItems: "center", gap: "20px" }}>
                      <ColorPicker onChange={handleColorChange} color={settings.color} />
                      <div
                        style={{
                          width: "40px",
                          height: "40px",
                          borderRadius: "4px",
                          backgroundColor: colorString,
                          boxShadow: "0 0 0 1px rgba(0, 0, 0, 0.1)",
                        }}
                      />
                    </div>
                  </BlockStack>
                </FormLayout>
              </Form>
            </Card.Section>
          </Card>
          
          <div style={{ marginTop: "20px" }}></div>
          
          <Card>
            <Card.Section>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  Test Live Voice Assistant
                </Text>
                <Text variant="bodyMd" as="p">
                  Test the voice assistant with real-time audio streaming. This will use the new backend-worker to process your voice commands.
                </Text>
                
                <BlockStack gap="400">
                  <Button 
                    onClick={() => setTestingMode(!testingMode)}
                    variant={!testingMode ? "primary" : "plain"}
                    disabled={testingMode && audioStreamActive}
                  >
                    {testingMode ? "Exit Testing Mode" : "Enter Testing Mode"}
                  </Button>
                  
                  {testingMode && (
                    <div style={{ marginTop: '16px' }}>
                      <BlockStack gap="400">
                        <div style={{ display: 'flex', gap: '8px' }}>
                          <Button 
                            onClick={audioStreamActive ? stopAudioStream : startAudioStream}
                            variant={!audioStreamActive ? "primary" : "plain"}
                            disabled={!testingMode || (audioStreamActive && isProcessing)}
                          >
                            {audioStreamActive ? "Stop Listening" : "Start Listening"}
                          </Button>
                          
                          {isProcessing && (
                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                              <Spinner size="small" />
                              <Text as="span">Processing...</Text>
                            </div>
                          )}
                        </div>
                        
                        <div style={{ 
                          padding: '16px', 
                          border: '1px solid #ddd', 
                          borderRadius: '8px',
                          backgroundColor: '#f9f9f9',
                          minHeight: '80px'
                        }}>
                          <Text variant="headingMd" as="h3">Response:</Text>
                          <Text as="p">{responseMessage || 'No response yet'}</Text>
                        </div>
                        
                        <div>
                          <Text variant="bodyMd" as="p">Cart Items: {cartData.items.length}</Text>
                          <Text variant="bodyMd" as="p">Shop: {shop}</Text>
                          <Text variant="bodyMd" as="p">WebSocket: {wsConnection && wsConnection.readyState === WebSocket.OPEN ? 'Connected' : 'Disconnected'}</Text>
                        </div>
                      </BlockStack>
                    </div>
                  )}
                </BlockStack>
              </BlockStack>
            </Card.Section>
          </Card>
        </Layout.Section>

        <Layout.Section>
          <Card>
            <Card.Section>
              <BlockStack gap="400">
                <Text variant="headingMd" as="h2">
                  Preview
                </Text>
                <div
                  style={{
                    position: "relative",
                    height: "300px",
                    border: "1px solid #e1e3e5",
                    borderRadius: "4px",
                    overflow: "hidden",
                    backgroundColor: "#f6f6f7",
                  }}
                >
                  <div
                    style={{
                      position: "absolute",
                      bottom: "20px",
                      ...(settings.position === "bottom-right"
                        ? { right: "20px" }
                        : { left: "20px" }),
                    }}
                  >
                    <div
                      style={{
                        width: "56px",
                        height: "56px",
                        borderRadius: "50%",
                        backgroundColor: colorString,
                        color: "white",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
                        cursor: "pointer",
                      }}
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M12 15C13.66 15 15 13.66 15 12V6C15 4.34 13.66 3 12 3C10.34 3 9 4.34 9 6V12C9 13.66 10.34 15 12 15Z"
                          fill="currentColor"
                        />
                        <path
                          d="M17 12C17 14.76 14.76 17 12 17C9.24 17 7 14.76 7 12H5C5 15.53 7.61 18.43 11 18.93V21H13V18.93C16.39 18.43 19 15.53 19 12H17Z"
                          fill="currentColor"
                        />
                      </svg>
                    </div>
                  </div>
                </div>
              </BlockStack>
            </Card.Section>
          </Card>
        </Layout.Section>
      </Layout>

      <PageActions
        primaryAction={{
          content: "Save",
          onAction: handleSave,
          loading: isSaving,
          disabled: !settings.hasChanges || isSaving,
        }}
        secondaryActions={[
          {
            content: "Discard changes",
            onAction: handleDiscardChanges,
            disabled: !settings.hasChanges || isSaving,
          },
        ]}
      />
    </Page>
  );
}