import { json, type ActionFunctionArgs } from "@remix-run/node";
import { authenticate } from "../../../../../shopify.server";

/**
 * Bot API handler for the Shopify app proxy.
 * This handles requests to: /apps/voice/api/livekit/bot
 */
export const action = async (args: ActionFunctionArgs) => {
  const requestId = `bot-req-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  console.log(`=== [${requestId}] [proxy/api/livekit/bot] Bot API request via app proxy ===`);
  
  // Define CORS headers
  const CORS_HEADERS = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "POST, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Accept",
  };
  
  try {
    // Authenticate the request via app proxy
    const { session } = await authenticate.public.appProxy(args.request);
    const shopDomain = session?.shop;
    
    // Log the incoming request details for debugging
    const url = new URL(args.request.url);
    console.log(`[${requestId}] Request details:`);
    console.log(`[${requestId}] URL: ${url.toString()}`);
    console.log(`[${requestId}] Path: ${url.pathname}`);
    console.log(`[${requestId}] Method: ${args.request.method}`);
    console.log(`[${requestId}] Session shop: ${shopDomain}`);
    
    // Handle OPTIONS request for CORS
    if (args.request.method === "OPTIONS") {
      return new Response(null, {
        status: 204,
        headers: CORS_HEADERS
      });
    }
    
    // Only handle POST requests
    if (args.request.method !== "POST") {
      return json({ error: "Method not allowed" }, { 
        status: 405,
        headers: CORS_HEADERS
      });
    }
    
    // Parse the request body
    const body = await args.request.json();
    console.log(`[${requestId}] Request body:`, body);
    
    // Extract the action and roomName from the request
    const { action, roomName } = body;
    
    if (!action) {
      return json({ error: "Missing required 'action' field" }, { 
        status: 400,
        headers: CORS_HEADERS
      });
    }
    
    if (action === "join" && !roomName) {
      return json({ error: "Missing required 'roomName' field for join action" }, { 
        status: 400,
        headers: CORS_HEADERS
      });
    }
    
    // Get the LiveKit proxy URL from environment variables
    const livekitProxyUrl = process.env.LIVEKIT_PROXY_URL || "http://localhost:7880";
    
    try {
      // Forward the request to the LiveKit proxy
      console.log(`[${requestId}] Forwarding bot ${action} request to LiveKit Proxy at ${livekitProxyUrl}`);
      
      const proxyResponse = await fetch(`${livekitProxyUrl}/bot`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json",
          "X-Request-ID": requestId,
          "X-Shopify-Shop-Domain": shopDomain || ""
        },
        body: JSON.stringify({
          action,
          roomName,
          shopId: shopDomain,
          requestId
        })
      });
      
      if (!proxyResponse.ok) {
        const errorText = await proxyResponse.text();
        console.error(`[${requestId}] Error response from LiveKit Proxy: ${proxyResponse.status} - ${errorText}`);
        return json({ 
          error: "Failed to process bot request", 
          details: `Proxy returned status ${proxyResponse.status}`
        }, { 
          status: 502,  // Bad Gateway
          headers: CORS_HEADERS
        });
      }
      
      // Return the response from the proxy
      const responseData = await proxyResponse.json();
      console.log(`[${requestId}] LiveKit Proxy bot response:`, responseData);
      
      return json({
        status: "success",
        message: `Bot ${action} request processed successfully`,
        ...responseData
      }, {
        status: 200,
        headers: CORS_HEADERS
      });
      
    } catch (proxyError) {
      console.error(`[${requestId}] Error forwarding request to LiveKit Proxy:`, proxyError);
      return json({
        error: "Failed to communicate with voice service",
        message: proxyError instanceof Error ? proxyError.message : "Unknown error",
        requestId
      }, {
        status: 503,  // Service Unavailable
        headers: CORS_HEADERS
      });
    }
    
  } catch (error) {
    console.error(`[${requestId}] Error processing bot request:`, error);
    return json({
      error: "Server error",
      message: error instanceof Error ? error.message : "Unknown error",
      requestId
    }, {
      status: 500,
      headers: CORS_HEADERS
    });
  }
};

// Also handle GET requests for health checks
export const loader = async (args: ActionFunctionArgs) => {
  return json({ status: "ok", message: "Bot API is running" }, {
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "POST, GET, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Accept"
    }
  });
};