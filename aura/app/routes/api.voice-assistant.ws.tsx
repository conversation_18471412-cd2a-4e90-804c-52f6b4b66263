import { type LoaderFunctionArgs, json } from "@remix-run/node";
import { authenticateVoiceAssistantRequest } from "../utils/voice-assistant-auth";

/**
 * DEPRECATED: Server-Sent Events endpoint for voice assistant events
 *
 * This endpoint is deprecated and has been replaced by direct LiveKit WebRTC connections.
 * Use /api/livekit/token endpoint to get a LiveKit token and connect directly via WebRTC.
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  try {
    await authenticateVoiceAssistantRequest(request);
  } catch (e) {
    return json({ 
      error: "Authentication failed",
      message: "Please ensure you have a valid session"
    }, { status: 401 });
  }
  
  // Instead of maintaining backward compatibility, return a clear message
  // explaining that this endpoint is deprecated
  return json({
    error: "DEPRECATED_ENDPOINT",
    message: "This endpoint is deprecated and has been replaced by direct LiveKit WebRTC connections",
    migration: {
      step1: "Use /api/livekit/token endpoint to get a LiveKit token",
      step2: "Connect directly to LiveKit server using livekit-client SDK",
      step3: "For implementation details, see /extensions/voice-assistant/assets/voice-assistant-integration.js",
      documentation: "See docs/hybrid-architecture-overview.md for more information"
    }
  }, { 
    status: 410, // Gone status code
    headers: {
      'Cache-Control': 'no-cache'
    }
  });
};