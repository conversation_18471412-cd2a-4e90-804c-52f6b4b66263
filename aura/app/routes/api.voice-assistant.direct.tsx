import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { authenticateVoiceAssistantLoader, authenticateVoiceAssistantRequest } from "../utils/voice-assistant-auth";
import { triggerBotJoin } from "../bot";

/**
 * Direct API endpoint for processing voice assistant audio
 * Updated to use the new Bot Service architecture and direct LiveKit connection
 */

export const loader = async (args: LoaderFunctionArgs) => {
  // Use the shared authentication utility
  await authenticateVoiceAssistantLoader(args);
  
  return json({ 
    status: "ok",
    message: "Voice Assistant API is available and authenticated",
    architecture: "Using Bot Service (direct LiveKit connection)"
  });
};

export const action = async ({ request }: ActionFunctionArgs) => {
  // Authenticate using the new utility
  try {
    await authenticateVoiceAssistantRequest(request);
  } catch (error) {
    console.log('Authentication failed for direct API endpoint:', error);
    return json({ 
      error: "Authentication failed",
      message: "Please ensure you have a valid session"
    }, { status: 401 });
  }
  
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    // Parse the JSON body to get audio data
    const { audio, shopDomain } = await request.json();
    
    if (!audio) {
      return json({ error: "Missing audio data" }, { status: 400 });
    }
    
    if (!shopDomain) {
      return json({ error: "Missing shop domain" }, { status: 400 });
    }

    console.log(`[MIGRATION NOTE] Request to direct API endpoint received. Using new Bot Service architecture.`);
    
    // With the new architecture, this endpoint is primarily used for testing or compatibility
    // The preferred approach is direct LiveKit connection from the frontend
    
    // Use the new Bot Service architecture - trigger bot to join the room for this shop
    // This ensures the bot is ready to process audio for this shop
    const roomName = `room-${shopDomain}`;
    console.log(`Triggering bot to join room: ${roomName}`);
    
    try {
      // Make the bot join the room
      await triggerBotJoin(roomName);
      
      // Return success with information about the LiveKit connection approach
      return json({
        status: "success",
        result: {
          message: "Bot service activated for this shop",
          note: "For better performance, use direct LiveKit connection with LiveKit tokens",
          livekit_info: {
            room_name: roomName,
            connection_type: "direct"
          },
          shop_domain: shopDomain
        }
      });
    } catch (error) {
      console.error('Error triggering bot join:', error);
      return json({ 
        error: 'Failed to activate bot service',
        message: 'Sorry, there was a problem activating the voice assistant service.'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error processing direct API request:', error);
    return json({ 
      error: 'Error processing request',
      message: 'Sorry, there was a problem processing your request.'
    }, { status: 500 });
  }
};