// Import types only, to prevent direct reference in TypeScript
// Remove unused type import

// Define the processor interface for improved typing
interface KrispProcessor {
  process(data: Float32Array): Float32Array;
  destroy(): void;
}

// Safely create a Krisp filter instance
const createKrispFilter = (): KrispProcessor | null => {
  try {
    // Dynamically import <PERSON><PERSON><PERSON><PERSON>Filter to avoid TypeScript issues
    // Using CommonJS require since this is more reliable across environments
    const KrispModule = require('@livekit/krisp-noise-filter');
    if (KrispModule && KrispModule.KrispNoiseFilter) {
      return new KrispModule.KrispNoiseFilter();
    }
    return null;
  } catch (e) {
    console.warn('Krisp noise filter not available:', e);
    return null;
  }
};

export interface AudioConfig {
  noiseSuppression: boolean;
  echoCancellation: boolean;
  autoGainControl: boolean;
  sampleRate?: number;
  channelCount?: number;
}

const DEFAULT_AUDIO_CONFIG: AudioConfig = {
  noiseSuppression: true,
  echoCancellation: true,
  autoGainControl: true,
  sampleRate: 16000, // Optimal for speech recognition
  channelCount: 1,   // Mono audio for voice
};

// Utility to publish microphone audio stream via WebRTC (WHIP protocol)
export class RealtimeKitAudioClient {
  private peerConnection: RTCPeerConnection | null = null;
  private stream: MediaStream | null = null;
  private whipUrl: string;

  constructor(whipUrl: string) {
    this.whipUrl = whipUrl;
  }

  async startAudio() {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({ audio: true, video: false });
      this.peerConnection = new RTCPeerConnection({
        iceServers: [{ urls: 'stun:stun.cloudflare.com:3478' }],
      });
      this.stream.getAudioTracks().forEach(track => this.peerConnection!.addTrack(track, this.stream!));
      const offer = await this.peerConnection.createOffer();
      await this.peerConnection.setLocalDescription(offer);
      const response = await fetch(this.whipUrl, {
        method: 'POST',
        headers: { 'Content-Type': 'application/sdp' },
        body: offer.sdp,
      });
      if (!response.ok) throw new Error('Failed to connect to WHIP endpoint');
      const answerSdp = await response.text();
      await this.peerConnection.setRemoteDescription({ type: 'answer', sdp: answerSdp });
      return true;
    } catch (err) {
      this.stopAudio();
      throw err;
    }
  }

  stopAudio() {
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
  }

  isActive() {
    return !!(this.peerConnection && this.stream);
  }
}