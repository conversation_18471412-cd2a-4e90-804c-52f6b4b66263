/**
 * Voice Assistant Authentication Utilities
 * 
 * Provides shared authentication functions for voice assistant API routes.
 * Replaces the deprecated checkSessionCookie method with current authentication patterns.
 */

import { Session } from "@shopify/shopify-api";
import { LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";

/**
 * Authenticate a request to the voice assistant APIs
 * 
 * @param request The request to authenticate
 * @returns The authenticated session
 * @throws If authentication fails
 */
export async function authenticateVoiceAssistantRequest(request: Request): Promise<Session> {
  try {
    // Check for valid session using the recommended approach in the Shopify documentation
    // This automatically throws if authentication fails
    const authResponse = await authenticate.public.appProxy(request);
    
    // Extract the session
    const { session } = authResponse;
    
    if (!session) {
      throw new Response("Unauthorized: No session found", { status: 401 });
    }
    
    return session;
  } catch (error) {
    console.error("Voice Assistant Authentication Error:", error);
    throw new Response("Unauthorized: Authentication failed", { status: 401 });
  }
}

/**
 * Helper for Remix loader functions that need voice assistant authentication
 * 
 * @param args Loader function arguments from Remix
 * @returns The authenticated session
 */
export async function authenticateVoiceAssistantLoader({ request }: LoaderFunctionArgs): Promise<Session> {
  return authenticateVoiceAssistantRequest(request);
}