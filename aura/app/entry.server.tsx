import { PassThrough } from "stream";
import { renderToPipeableStream } from "react-dom/server";
import { RemixServer } from "@remix-run/react";
import {
  createReadableStreamFromReadable,
  type EntryContext,
} from "@remix-run/node";
import { isbot } from "isbot";
import { addDocumentResponseHeaders } from "./shopify.server";
// Import the new Bot Service architecture components
import { getLiveKitBotServiceInstance } from './bot';
import { initLiveKitProxy } from "./livekit-proxy.server";

// Initialize Bot Service when the application starts
let botServiceInitialized = false;

// Variable to track LiveKit proxy initialization
let livekitProxyInitialized = false;
let livekitProxyPromise: Promise<any> | null = null;

/**
 * Initialize the LiveKit proxy server once, for development use
 * This ensures it uses the same port consistently
 */
async function ensureLiveKitProxyStarted() {
  // Skip LiveKit proxy for bot requests or in production
  if (process.env.NODE_ENV === 'production') {
    console.log('[entry.server] Skipping LiveKit proxy in production (should be run separately)');
    return null;
  }

  // Use singleton pattern to avoid multiple initializations
  if (livekitProxyInitialized) {
    console.debug('[entry.server] LiveKit proxy already initialized');
    return livekitProxyPromise;
  }

  if (livekitProxyPromise) {
    console.debug('[entry.server] LiveKit proxy initialization in progress, reusing promise');
    return livekitProxyPromise;
  }

  console.log('[entry.server] Starting LiveKit proxy server for development...');
  livekitProxyPromise = initLiveKitProxy()
    .then(instance => {
      if (instance.isExistingInstance) {
        console.log(`[entry.server] ✅ Using existing LiveKit proxy server on port ${instance.port}`);
      } else {
        console.log(`[entry.server] ✅ LiveKit proxy server started successfully on port ${instance.port}`);
      }
      
      livekitProxyInitialized = true;
      
      // Always set the environment variable to the consistent port
      // This is important even if we're using an existing proxy instance
      const proxyUrl = `http://localhost:${instance.port}`;
      process.env.LIVEKIT_PROXY_URL = proxyUrl;
      console.log(`[entry.server] 🔗 Set LIVEKIT_PROXY_URL to: ${process.env.LIVEKIT_PROXY_URL}`);
      
      // We need to use a promise-based approach here rather than await
      // since we're inside a then() callback which isn't async
      console.log(`[entry.server] Verifying proxy connection at ${proxyUrl}/health...`);
      fetch(`${proxyUrl}/health`, { method: 'GET' })
        .then(response => {
          if (response.ok) {
            return response.json().then(healthData => {
              console.log(`[entry.server] ✅ LiveKit proxy health check successful: ${healthData.status}`);
            });
          } else {
            console.warn(`[entry.server] ❌ LiveKit proxy health check failed with status: ${response.status}`);
          }
        })
        .catch(error => {
          console.warn(`[entry.server] ❌ Failed to connect to LiveKit proxy for health check: ${error.message}`);
          // Continue anyway - the proxy might still work for WebSockets/SSE
        });
      
      return instance;
    })
    .catch(error => {
      console.error('[entry.server] ❌ Failed to start LiveKit proxy server:', error);
      livekitProxyPromise = null; // Reset promise on error to allow retry
      return null;
    });

  return livekitProxyPromise;
}

export const streamTimeout = 5000;

export default async function handleRequest(
  request: Request,
  responseStatusCode: number,
  responseHeaders: Headers,
  remixContext: EntryContext
) {
  // Skip the regular bot handling logic for health checks and handle CORS
  const url = new URL(request.url);
  
  // Define standard CORS headers for direct API access
  const CORS_HEADERS = {
    "Access-Control-Allow-Origin": "*", // Allow access from any origin (including Shopify storefronts)
    "Access-Control-Allow-Methods": "GET, OPTIONS, POST",
    "Access-Control-Allow-Headers": "Content-Type, Authorization, Accept",
    "Access-Control-Max-Age": "86400", // Cache preflight requests for 24 hours
  };
  
  // Handle preflight OPTIONS requests for CORS
  if (request.method === 'OPTIONS' && (url.pathname.includes('/api/') || url.pathname.includes('/proxy/'))) {
    return new Response(null, { 
      status: 204, 
      headers: CORS_HEADERS 
    });
  }
  
  if (url.pathname === "/health" || url.pathname.includes("/health")) {
    const isProxyRunning = livekitProxyInitialized;
    const isBotRunning = botServiceInitialized;
    return new Response(
      JSON.stringify({ 
        status: isProxyRunning && isBotRunning ? "ok" : "degraded",
        message: `Server is ${isProxyRunning && isBotRunning ? "healthy" : "degraded"}`,
        services: {
          proxy: isProxyRunning ? "running" : "not running",
          bot: isBotRunning ? "running" : "not running"
        },
        timestamp: new Date().toISOString()
      }),
      {
        status: 200, // Always return 200 for health checks
        headers: {
          "Content-Type": "application/json",
          ...CORS_HEADERS
        },
      }
    );
  }

  // Try to ensure the LiveKit proxy is started
  try {
    await ensureLiveKitProxyStarted();
  } catch (error) {
    console.error('[entry.server] Error ensuring LiveKit proxy is started:', error);
  }
  
  // Initialize Bot Service if not already done
  if (!botServiceInitialized) {
    try {
      // Get singleton instance of the LiveKit Bot Service
      // This will initialize the service
      getLiveKitBotServiceInstance();
      botServiceInitialized = true;
      console.log('[entry.server] Voice Assistant Bot Service initialized successfully');
    } catch (error) {
      console.error('[entry.server] Failed to initialize Voice Assistant Bot Service:', error);
    }
  }

  addDocumentResponseHeaders(request, responseHeaders);
  const userAgent = request.headers.get("user-agent");
  const callbackName = isbot(userAgent ?? '')
    ? "onAllReady"
    : "onShellReady";

  return new Promise((resolve, reject) => {
    const { pipe, abort } = renderToPipeableStream(
      <RemixServer
        context={remixContext}
        url={request.url}
      />,
      {
        [callbackName]: () => {
          const body = new PassThrough();
          const stream = createReadableStreamFromReadable(body);

          responseHeaders.set("Content-Type", "text/html");
          resolve(
            new Response(stream, {
              headers: responseHeaders,
              status: responseStatusCode,
            })
          );
          pipe(body);
        },
        onShellError(error) {
          reject(error);
        },
        onError(error) {
          responseStatusCode = 500;
          console.error(error);
        },
      }
    );

    // Automatically timeout the React renderer after 6 seconds, which ensures
    // React has enough time to flush down the rejected boundary contents
    setTimeout(abort, streamTimeout + 1000);
  });
}
