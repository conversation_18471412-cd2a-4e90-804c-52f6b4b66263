{"include": ["env.d.ts", "**/*.ts", "**/*.tsx"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "strict": true, "skipLibCheck": true, "isolatedModules": true, "allowSyntheticDefaultImports": true, "removeComments": false, "forceConsistentCasingInFileNames": true, "noEmit": true, "allowJs": true, "resolveJsonModule": true, "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "target": "ES2022", "baseUrl": ".", "types": ["node"]}, "exclude": ["legacy", "legacy/**/*"]}