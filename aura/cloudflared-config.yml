# Cloudflare tunnel configuration for aura.fy.studio
# This file is a copy of ~/.cloudflared/config.yml for reference

tunnel: c9a861cb-2669-4b57-b5f4-eedba698cbc2
credentials-file: /Users/<USER>/.cloudflared/c9a861cb-2669-4b57-b5f4-eedba698cbc2.json

ingress:
  # LiveKit health endpoint
  - hostname: aura.fy.studio
    path: /health
    service: http://localhost:7880

  # Route /apps/voice/* to your Remix app (NOT LiveKit)
  - hostname: aura.fy.studio
    path: /apps/voice/*
    service: http://localhost:54029

  # Route direct API access to your Remix app
  - hostname: aura.fy.studio
    path: /api/*
    service: http://localhost:54029
    
  # Route the root and all other traffic to your main app (Remix/Shopify)
  - hostname: aura.fy.studio
    service: http://localhost:54029

  # Catch-all rule must be last
  - service: http_status:404