import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { loader as livekitTokenLoader } from "./api.livekit.token";

/**
 * Custom route handler for the specific path /apps/voice/api/livekit/token
 * 
 * This is a direct match for the path in the frontend request.
 * This approach uses <PERSON>'s file-based routing to match the exact path.
 */
export const loader = async (args: LoaderFunctionArgs) => {
  console.log("[apps.voice.api.livekit.token] Received direct token request");
  
  try {
    // Log the original request URL
    const url = new URL(args.request.url);
    console.log(`[apps.voice.api.livekit.token] Original request URL: ${url.toString()}`);
    console.log(`[apps.voice.api.livekit.token] Original path: ${url.pathname}`);
    console.log(`[apps.voice.api.livekit.token] Query parameters:`, 
      Object.fromEntries([...url.searchParams.entries()].map(([k, v]) => [k, v])));
    
    // Create a new request with the correct URL path for the token endpoint
    const newUrl = new URL(url);
    newUrl.pathname = "/api/livekit/token";
    
    // Create a new request with the modified URL
    const newRequest = new Request(newUrl.toString(), {
      method: args.request.method,
      headers: args.request.headers,
      body: args.request.body
    });
    
    console.log(`[apps.voice.api.livekit.token] Forwarding to token endpoint at: ${newUrl.pathname}`);
    
    // Forward the modified request to the token endpoint
    return livekitTokenLoader({ ...args, request: newRequest });
  } catch (error) {
    console.error("[apps.voice.api.livekit.token] Error processing request:", error);
    
    // Return a friendly error with CORS headers
    return json(
      { 
        error: "Processing failed", 
        message: error instanceof Error ? error.message : "An unknown error occurred"
      }, 
      { 
        status: 500,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        }
      }
    );
  }
};

// Handle OPTIONS requests for CORS
export const action = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 204,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Access-Control-Max-Age": "86400"
      }
    });
  }
  
  return json({ error: "Method not allowed" }, { status: 405 });
};