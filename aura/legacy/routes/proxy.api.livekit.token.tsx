import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { AccessToken } from "livekit-server-sdk";

/**
 * Legacy proxy route handler for /proxy/api/livekit/token
 * 
 * NOTE: This file should be replaced by the nested route structure at:
 * /app/routes/proxy/api/livekit/token.tsx
 * 
 * This file is kept for backward compatibility during migration.
 */
export const loader = async (args: LoaderFunctionArgs) => {
  const requestId = `req-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  console.log(`=== [${requestId}] [proxy.api.livekit.token] Received token request via LEGACY flat route ===`);
  console.log(`[${requestId}] ⚠️ DEPRECATION WARNING: Using legacy flat route instead of nested route structure`);
  
  // Define CORS headers
  const CORS_HEADERS = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };
  
  try {
    // Log the incoming request details before authentication
    console.log(`[${requestId}] ⭐ INCOMING PROXY REQUEST DETAILS ⭐`);
    const url = new URL(args.request.url);
    console.log(`[${requestId}] Request URL: ${url.toString()}`);
    console.log(`[${requestId}] Request path: ${url.pathname}`);
    console.log(`[${requestId}] Request method: ${args.request.method}`);
    console.log(`[${requestId}] Request headers:`, Object.fromEntries([...args.request.headers.entries()].map(([k, v]) => [k, v])));
    console.log(`[${requestId}] Query parameters:`, Object.fromEntries([...url.searchParams.entries()].map(([k, v]) => [k, v])));
    
    // Check for Shopify proxy signature parameters
    const hasSignature = url.searchParams.has('signature');
    const hasTimestamp = url.searchParams.has('timestamp');
    const hasShop = url.searchParams.has('shop');
    console.log(`[${requestId}] Shopify proxy signature present: ${hasSignature}`);
    console.log(`[${requestId}] Shopify proxy timestamp present: ${hasTimestamp}`);
    console.log(`[${requestId}] Shop parameter present: ${hasShop}`);
    
    // Extract shop parameter from various potential sources
    let shopDomain = null;
    
    // First try query parameter
    shopDomain = url.searchParams.get('shop');
    if (shopDomain) {
      console.log(`[${requestId}] Found shop in query parameter: ${shopDomain}`);
    }
    
    // If not found, try Shopify-specific headers
    if (!shopDomain) {
      shopDomain = args.request.headers.get('x-shopify-shop-domain');
      if (shopDomain) {
        console.log(`[${requestId}] Found shop in header: ${shopDomain}`);
      }
    }
    
    // Last resort - try to get it from the session
    if (!shopDomain) {
      try {
        const { session } = await authenticate.public.appProxy(args.request);
        shopDomain = session?.shop;
        if (shopDomain) {
          console.log(`[${requestId}] ✅ Found shop in session: ${shopDomain}`);
        }
      } catch (authError) {
        console.error(`[${requestId}] ❌ App proxy authentication failed: ${authError instanceof Error ? authError.message : String(authError)}`);
      }
    }
    
    // Without a shop domain, we can't generate a token
    if (!shopDomain) {
      console.error(`[${requestId}] ❌ Missing required shop parameter from all sources`);
      return json({ 
        error: "Missing shop parameter", 
        message: "The shop parameter is required to generate a token",
        requestId
      }, { 
        status: 400,
        headers: CORS_HEADERS
      });
    }
    
    // Clean and validate the shop domain
    shopDomain = shopDomain.trim().toLowerCase();
    shopDomain = shopDomain.replace(/^https?:\/\//, '').replace(/\/$/, '');
    console.log(`[${requestId}] Using shop domain: ${shopDomain}`);
    
    // Check environment variables directly
    const livekitHost = process.env.LIVEKIT_URL;
    const livekitApiKey = process.env.LIVEKIT_API_KEY || process.env.LIVEKIT_KEY;
    const livekitApiSecret = process.env.LIVEKIT_API_SECRET || process.env.LIVEKIT_SECRET;
    
    console.log(`[${requestId}] Environment variables check:`);
    console.log(`[${requestId}] LIVEKIT_URL: ${livekitHost ? "Set" : "NOT SET"}`);
    console.log(`[${requestId}] LIVEKIT_API_KEY/LIVEKIT_KEY: ${livekitApiKey ? "Set" : "NOT SET"}`);
    console.log(`[${requestId}] LIVEKIT_API_SECRET/LIVEKIT_SECRET: ${livekitApiSecret ? "Set (masked)" : "NOT SET"}`);
    
    if (!livekitHost || !livekitApiKey || !livekitApiSecret) {
      console.error(`[${requestId}] ❌ Missing required LiveKit environment variables`);
      return json({ 
        error: "Server configuration error",
        message: "The LiveKit service is not properly configured",
        requestId 
      }, { 
        status: 500,
        headers: CORS_HEADERS
      });
    }
    
    // Generate LiveKit tokens directly rather than forwarding to another handler
    try {
      // Generate room name and participant identity
      const roomName = `voice-assistant-${shopDomain}`;
      const participantIdentity = `user-${shopDomain}-${Math.random().toString(36).substring(2, 12)}`;
      console.log(`[${requestId}] Generating token for Room: ${roomName}, Participant: ${participantIdentity}`);
      
      // Create the access token
      const at = new AccessToken(livekitApiKey, livekitApiSecret, {
        identity: participantIdentity,
      });
      
      // Define permissions
      at.addGrant({ 
        room: roomName,
        roomJoin: true,
        canPublish: true,      // Allow publishing audio 
        canSubscribe: true,    // Allow subscribing to tracks (e.g., TTS audio)
        canPublishData: true,  // Allow sending data messages
      });
      
      // Set token expiration (e.g., 15 minutes)
      at.ttl = '15m';
      
      // Generate the JWT
      const token = await at.toJwt();
      console.log(`[${requestId}] ✅ Successfully generated token`);
      
      // Return token info
      return json({
        livekitUrl: livekitHost, 
        token: token,
        roomName: roomName,
        identity: participantIdentity,
        requestId
      }, { 
        status: 200,
        headers: CORS_HEADERS
      });
    } catch (tokenError) {
      console.error(`[${requestId}] ❌ Error generating LiveKit token:`, tokenError);
      return json({ 
        error: "Token generation failed", 
        message: tokenError instanceof Error ? tokenError.message : String(tokenError),
        requestId
      }, { 
        status: 500,
        headers: CORS_HEADERS
      });
    }
  } catch (error) {
    console.error(`[${requestId}] ❌ Unexpected error processing request:`, error);
    console.error(`[${requestId}] Stack trace:`, error instanceof Error ? error.stack : 'No stack trace available');
    
    // Return a detailed error with CORS headers
    return json(
      { 
        error: "Server error", 
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
        requestId
      }, 
      { 
        status: 500,
        headers: CORS_HEADERS
      }
    );
  }
};

// Handle OPTIONS requests for CORS
export const action = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 204,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Access-Control-Max-Age": "86400"
      }
    });
  }
  
  return json({ error: "Method not allowed" }, { status: 405 });
};