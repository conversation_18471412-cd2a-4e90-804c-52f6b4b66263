import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { AccessToken } from "livekit-server-sdk";
import { authenticate } from "../../../../../shopify.server";

/**
 * Direct route handler for /apps/voice/api/livekit/token
 * This matches the exact path structure used in the frontend request.
 * 
 * IMPORTANT: This is a critical path that should directly generate the token
 * rather than forwarding, to avoid any path resolution issues.
 */
export const loader = async (args: LoaderFunctionArgs) => {
  const requestId = `req-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
  console.log(`=== [${requestId}] [apps/voice/api/livekit/token] Matched the exact path! Processing token request ===`);
  
  // Define CORS headers for direct access
  const CORS_HEADERS = {
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Methods": "GET, OPTIONS",
    "Access-Control-Allow-Headers": "Content-Type, Authorization",
  };
  
  try {
    // Handle OPTIONS preflight request for CORS
    if (args.request.method === 'OPTIONS') {
      return new Response(null, { status: 204, headers: CORS_HEADERS });
    }
    
    // Log the request details for debugging
    const url = new URL(args.request.url);
    console.log(`[${requestId}] Request URL: ${url.toString()}`);
    console.log(`[${requestId}] Request path: ${url.pathname}`);
    console.log(`[${requestId}] Request method: ${args.request.method}`);
    console.log(`[${requestId}] Request headers:`, Object.fromEntries([...args.request.headers.entries()].map(([k, v]) => [k, v])));
    console.log(`[${requestId}] Query parameters:`, Object.fromEntries([...url.searchParams.entries()].map(([k, v]) => [k, v])));
    
    // Check for Shopify proxy signature parameters
    const hasSignature = url.searchParams.has('signature');
    const hasTimestamp = url.searchParams.has('timestamp');
    const hasShop = url.searchParams.has('shop');
    console.log(`[${requestId}] Shopify proxy signature present: ${hasSignature}`);
    console.log(`[${requestId}] Shopify proxy timestamp present: ${hasTimestamp}`);
    console.log(`[${requestId}] Shop parameter present: ${hasShop}`);
    
    // Extract shop parameter from various potential sources
    let shopDomain = null;
    
    // First try query parameter
    shopDomain = url.searchParams.get('shop');
    
    // If not found, try Shopify-specific headers
    if (!shopDomain) {
      shopDomain = args.request.headers.get('x-shopify-shop-domain');
      if (shopDomain) {
        console.log(`[${requestId}] Found shop in header: ${shopDomain}`);
      }
    }
    
    // Last resort - try to get it from the session
    if (!shopDomain) {
      try {
        // For direct access (non-proxy), we want to ensure authentication is relaxed
        // since we're accepting direct calls from the store domain
        const { session } = await authenticate.public.appProxy(args.request);
        shopDomain = session?.shop;
        if (shopDomain) {
          console.log(`[${requestId}] ✅ Found shop in session: ${shopDomain}`);
        }
      } catch (authError) {
        console.error(`[${requestId}] Authentication failed: ${authError instanceof Error ? authError.message : String(authError)}`);
        // For direct route access, we'll keep going even if auth fails
        console.log(`[${requestId}] Continuing despite authentication error for direct route`);
      }
    }
    
    // Without a shop domain, we can't generate a token
    if (!shopDomain) {
      console.error(`[${requestId}] ❌ Missing required shop parameter from all sources`);
      return json({ 
        error: "Missing shop parameter", 
        message: "The shop parameter is required to generate a token",
        requestId
      }, { 
        status: 400,
        headers: CORS_HEADERS
      });
    }
    
    // Clean and validate the shop domain
    shopDomain = shopDomain.trim().toLowerCase();
    shopDomain = shopDomain.replace(/^https?:\/\//, '').replace(/\/$/, '');
    
    // Basic validation of shop domain format with very relaxed pattern
    // We don't want to reject valid domains due to overly strict validation
    const isValidDomain = (
      shopDomain.includes('.') && // Has at least one dot
      shopDomain.length >= 4 &&   // At least 4 chars total (a.co minimum)
      /^[a-zA-Z0-9]/.test(shopDomain) && // Starts with alphanumeric
      !/\s/.test(shopDomain)      // No whitespace
    );
    
    if (!isValidDomain) {
      console.warn(`[${requestId}] ❌ Invalid shop domain format: "${shopDomain}"`);
      return json({ 
        error: "Invalid shop domain format",
        receivedValue: shopDomain,
        message: "Shop domain should be in format: your-shop-domain.myshopify.com",
        requestId
      }, { 
        status: 400,
        headers: CORS_HEADERS
      });
    }
    
    console.log(`[${requestId}] ✅ Using shop domain: ${shopDomain}`);
    
    // Check environment variables directly
    const livekitHost = process.env.LIVEKIT_URL;
    const livekitApiKey = process.env.LIVEKIT_API_KEY || process.env.LIVEKIT_KEY;
    const livekitApiSecret = process.env.LIVEKIT_API_SECRET || process.env.LIVEKIT_SECRET;
    
    console.log(`[${requestId}] Environment variables check:`);
    console.log(`[${requestId}] LIVEKIT_URL: ${livekitHost ? "Set" : "NOT SET"}`);
    console.log(`[${requestId}] LIVEKIT_API_KEY/LIVEKIT_KEY: ${livekitApiKey ? "Set" : "NOT SET"}`);
    console.log(`[${requestId}] LIVEKIT_API_SECRET/LIVEKIT_SECRET: ${livekitApiSecret ? "Set (masked)" : "NOT SET"}`);
    
    if (!livekitHost || !livekitApiKey || !livekitApiSecret) {
      console.error(`[${requestId}] ❌ Missing required LiveKit environment variables`);
      return json({ 
        error: "Server configuration error",
        message: "The LiveKit service is not properly configured",
        requestId 
      }, { 
        status: 500,
        headers: CORS_HEADERS
      });
    }
    
    // Setup for token generation
    const roomName = `voice-assistant-${shopDomain}`;
    const participantIdentity = `user-${shopDomain}-${Math.random().toString(36).substring(2, 12)}`;
    console.log(`[${requestId}] Generating token for Room: ${roomName}, Participant: ${participantIdentity}`);
    
    try {
      // Create the access token
      const at = new AccessToken(livekitApiKey, livekitApiSecret, {
        identity: participantIdentity,
      });
      
      // Define permissions
      at.addGrant({ 
        room: roomName,
        roomJoin: true,
        canPublish: true,      // Allow publishing audio 
        canSubscribe: true,    // Allow subscribing to tracks (e.g., TTS audio)
        canPublishData: true,  // Allow sending data messages
      });
      
      // Set token expiration (e.g., 15 minutes)
      at.ttl = '15m';
      
      // Generate the JWT
      const token = await at.toJwt();
      console.log(`[${requestId}] ✅ Successfully generated token`);
      
      // Return token info
      return json({
        livekitUrl: livekitHost, 
        token: token,
        roomName: roomName,
        identity: participantIdentity,
        requestId
      }, { 
        status: 200,
        headers: CORS_HEADERS
      });
    } catch (tokenError) {
      console.error(`[${requestId}] ❌ Error generating LiveKit token:`, tokenError);
      return json({ 
        error: "Token generation failed", 
        message: tokenError instanceof Error ? tokenError.message : String(tokenError),
        requestId
      }, { 
        status: 500,
        headers: CORS_HEADERS
      });
    }
        identity: participantIdentity,
      });

      // Define permissions
      at.addGrant({ 
        room: roomName,
        roomJoin: true,
        canPublish: true,      // Allow publishing audio
        canSubscribe: true,    // Allow subscribing to tracks (e.g., TTS audio)
        canPublishData: true,  // Allow sending data messages (optional)
      });

      // Set token expiration (e.g., 15 minutes)
      at.ttl = '15m';

      // Generate the JWT
      const token = await at.toJwt();
      
      // Return the LiveKit URL and the generated token
      const response = {
        livekitUrl: livekitHost, 
        token: token,
        roomName: roomName,
        identity: participantIdentity
      };
      console.log("[apps/voice/api/livekit/token] Successfully generated token");
      return json(response, { headers: CORS_HEADERS });

    } catch (error: unknown) {
      // Detailed error logging
      const errorMessage = error instanceof Error ? error.message : String(error);
      const errorStack = error instanceof Error ? error.stack : 'No stack trace available';
      
      console.error("[apps/voice/api/livekit/token] ⚠️ ERROR GENERATING TOKEN ⚠️");
      console.error(`Error message: ${errorMessage}`);
      console.error(`Stack trace: ${errorStack}`);
      
      // Return a detailed error response
      return json({ 
        error: "Failed to generate LiveKit token", 
        details: errorMessage,
        context: {
          room: roomName,
          participantIdentity: participantIdentity,
          timestamp: new Date().toISOString()
        }
      }, { status: 500, headers: CORS_HEADERS });
    }
  } catch (error) {
    console.error("[apps/voice/api/livekit/token] Unexpected error:", error);
    
    return json(
      { 
        error: "Token generation failed", 
        message: error instanceof Error ? error.message : "Unknown error"
      }, 
      { 
        status: 500,
        headers: CORS_HEADERS
      }
    );
  }
};

// Handle CORS preflight
export const action = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === "OPTIONS") {
    return new Response(null, {
      status: 204,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Access-Control-Max-Age": "86400"
      }
    });
  }
  
  return json({ error: "Method not allowed" }, { status: 405 });
};