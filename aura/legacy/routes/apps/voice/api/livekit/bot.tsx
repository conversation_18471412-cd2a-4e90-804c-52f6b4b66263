import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { triggerBotJoin, triggerBotLeave } from "../../../../../bot/index";

// Define CORS headers for cross-origin access
const CORS_HEADERS = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Methods": "POST, OPTIONS",
  "Access-Control-Allow-Headers": "Content-Type, Authorization",
};

/**
 * Handle OPTIONS preflight request
 */
export const loader = async ({ request }: LoaderFunctionArgs) => {
  if (request.method === 'OPTIONS') {
    return new Response(null, { status: 204, headers: CORS_HEADERS });
  }

  return json({ error: "Method not allowed" }, { status: 405, headers: CORS_HEADERS });
};

/**
 * Handle bot join/leave requests
 */
export const action = async ({ request }: ActionFunctionArgs) => {
  console.log("[apps/voice/api/livekit/bot] Received bot action request");
  
  // Handle OPTIONS preflight request
  if (request.method === 'OPTIONS') {
    return new Response(null, { status: 204, headers: CORS_HEADERS });
  }

  // Only allow POST requests
  if (request.method !== 'POST') {
    return json({ error: "Method not allowed" }, { status: 405, headers: CORS_HEADERS });
  }

  try {
    // Parse request body
    const body = await request.json();
    const { action, roomName } = body;

    if (!roomName) {
      return json({ error: "Missing required parameter: roomName" }, { status: 400, headers: CORS_HEADERS });
    }

    switch (action) {
      case 'join':
        console.log(`[apps/voice/api/livekit/bot] Triggering bot to join room: ${roomName}`);
        await triggerBotJoin(roomName);
        return json({ success: true, message: `Bot triggered to join room: ${roomName}` }, { headers: CORS_HEADERS });

      case 'leave':
        console.log(`[apps/voice/api/livekit/bot] Triggering bot to leave room: ${roomName}`);
        await triggerBotLeave(roomName);
        return json({ success: true, message: `Bot triggered to leave room: ${roomName}` }, { headers: CORS_HEADERS });

      default:
        return json({ error: "Invalid action. Use 'join' or 'leave'." }, { status: 400, headers: CORS_HEADERS });
    }
  } catch (error) {
    console.error('[apps/voice/api/livekit/bot] Error processing request:', error);
    return json({ 
      error: "Failed to process bot request", 
      details: error instanceof Error ? error.message : String(error) 
    }, { status: 500, headers: CORS_HEADERS });
  }
};