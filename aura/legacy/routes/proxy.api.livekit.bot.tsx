import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { authenticate } from "../shopify.server";
import { action as livekitBotAction, loader as livekitBotLoader } from "./api.livekit.bot";

/**
 * Proxy for the LiveKit Bot API endpoint accessible via the Shopify App Proxy
 * This handles requests to: /apps/voice/api/livekit/bot
 * 
 * When requests come through the Shopify App Proxy, they will have this URL structure:
 * - Original request from storefront: /apps/voice/api/livekit/bot
 * - Proxied by Shopify to: https://aura.fy.studio/proxy/api/livekit/bot
 * - Handled by this Remix route at: /proxy/api/livekit/bot
 */
export const loader = async (args: LoaderFunctionArgs) => {
  console.log("[proxy.api.livekit.bot] Received loader request via app proxy");
  
  try {
    // Use Shopify's app proxy authentication
    const { session } = await authenticate.public.appProxy(args.request);
    console.log(`[proxy.api.livekit.bot] Authenticated request for shop: ${session?.shop}`);
    
    // Forward to the actual bot endpoint logic
    return livekitBotLoader(args);
  } catch (error) {
    console.error("[proxy.api.livekit.bot] Authentication error:", error);
    
    // Return a friendly error with CORS headers
    return json(
      { 
        error: "Authentication failed", 
        message: "Could not authenticate the request" 
      }, 
      { 
        status: 401,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        }
      }
    );
  }
};

// Handle POST and OPTIONS requests
export const action = async (args: ActionFunctionArgs) => {
  console.log("[proxy.api.livekit.bot] Received action request via app proxy");
  
  // Handle OPTIONS preflight requests for CORS
  if (args.request.method === "OPTIONS") {
    return new Response(null, {
      status: 204,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type, Authorization",
        "Access-Control-Max-Age": "86400"
      }
    });
  }
  
  try {
    // Use Shopify's app proxy authentication
    const { session } = await authenticate.public.appProxy(args.request);
    console.log(`[proxy.api.livekit.bot] Authenticated action request for shop: ${session?.shop}`);
    
    // Forward to the actual bot endpoint logic
    return livekitBotAction(args);
  } catch (error) {
    console.error("[proxy.api.livekit.bot] Authentication error:", error);
    
    return json(
      { 
        error: "Authentication failed", 
        message: "Could not authenticate the request" 
      }, 
      { 
        status: 401,
        headers: {
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "POST, OPTIONS",
          "Access-Control-Allow-Headers": "Content-Type, Authorization",
        }
      }
    );
  }
};