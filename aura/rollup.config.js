import resolve from '@rollup/plugin-node-resolve';
import commonjs from '@rollup/plugin-commonjs';
import url from '@rollup/plugin-url';
import wasm from '@rollup/plugin-wasm';
import terser from '@rollup/plugin-terser';

const production = process.env.NODE_ENV === 'production';

export default {
  input: 'extensions/voice-assistant/assets/voice-assistant.js',
  output: {
    file: 'assets/voice-assistant-bundle.js',
    format: 'iife',
    name: 'VoiceAssistant',
    sourcemap: false,
    inlineDynamicImports: true
  },
  plugins: [
    resolve({
      browser: true,
      preferBuiltins: false
    }),
    url({
      include: ['**/*.wasm?url', '**/*.js?url'],
      limit: 0, // always copy
      fileName: '[name]-[hash][extname]',
      publicPath: './',
      destDir: 'assets'
    }),
    wasm({ 
      maxFileSize: 100000000,
      targetEnv: 'browser'
    }),
    commonjs({
      include: ['node_modules/**']
    }),
    // Add minification for production builds
    production && terser({
      compress: {
        drop_console: true,
        drop_debugger: true
      },
      mangle: {
        safari10: true
      }
    })
  ].filter(Boolean)
}; 