node_modules

/.cache
/build
/app/build
/public/build/
/public/_dev
/app/public/build
database.sqlite

.env
.env.*





/extensions/*/dist

# Ignore shopify files created during app dev
.shopify/*
.shopify.lock

# Added by <PERSON> Task Master
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
.DS_Store
# Task files
tasks.json
tasks/ 