# LiveKit server configuration

port: 7880
rtc:
  port_range_start: 7882
  port_range_end: 7982
  # Using host networking, no need for external IP detection
  use_external_ip: false
  # Hard-coded IP addresses aren't needed with host networking
  # stun_servers:
  #   - *************:19302  # Direct IP for stun.l.google.com
  #   - *************:19302  # Alternative Google STUN server IP

logging:
  level: info
  json: false

keys:
  "APIAjHeVcAhyYvB": "fZgrWmDYX9QvBDdwY52SIa1LelJPNMA35LGBtgLuDTK"

# Turn Server configuration
turn:
  enabled: false
  # Standard TURN ports
  # udp_port: 3478
  # tls_port: 5349 # Commented out to disable TLS for now
  # Explicitly disable TLS by providing empty cert/key paths
  # cert_file: ""
  # key_file: ""
  # No need for domain when using host networking
  # domain: "*************"
  # Define port range for TURN relay
  # relay_range_start: 40000
  # relay_range_end: 40100

# Redis can be enabled for scalability in production
# redis:
#   address: redis:6379
#   username: ""
#   password: ""
#   db: 0

# WebHook configuration - can be used for bot triggers in the future
# webhook:
#   api_key: your_livekit_key
#   urls:
#     - http://voice-bot:3000/api/livekit/webhook

# Production settings
# https: false
# redirect_http: false