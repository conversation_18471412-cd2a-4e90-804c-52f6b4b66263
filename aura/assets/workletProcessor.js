var Ke=Object.defineProperty;var Ze=(c,n,e)=>n in c?Ke(c,n,{enumerable:!0,configurable:!0,writable:!0,value:e}):c[n]=e;var j=(c=>typeof require<"u"?require:typeof Proxy<"u"?new Proxy(c,{get:(n,e)=>(typeof require<"u"?require:n)[e]}):c)(function(c){if(typeof require<"u")return require.apply(this,arguments);throw Error('Dynamic require of "'+c+'" is not supported')});var R=(c,n,e)=>(Ze(c,typeof n!="symbol"?n+"":n,e),e);var S={};var Je=async()=>WebAssembly.validate(new Uint8Array([0,97,115,109,1,0,0,0,1,5,1,96,0,1,123,3,2,1,0,10,10,1,8,0,65,0,253,15,253,98,11])),Pe=(()=>{var c=import.meta.url;return function(e){e=e||{};var e=typeof e<"u"?e:{},a=Object.assign,p,u;e.ready=new Promise(function(r,o){p=r,u=o}),Object.getOwnPropertyDescriptor(e.ready,"_rnnoise_process_frame")||(Object.defineProperty(e.ready,"_rnnoise_process_frame",{configurable:!0,get:function(){t("You are getting _rnnoise_process_frame on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(e.ready,"_rnnoise_process_frame",{configurable:!0,set:function(){t("You are setting _rnnoise_process_frame on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(e.ready,"_rnnoise_destroy")||(Object.defineProperty(e.ready,"_rnnoise_destroy",{configurable:!0,get:function(){t("You are getting _rnnoise_destroy on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(e.ready,"_rnnoise_destroy",{configurable:!0,set:function(){t("You are setting _rnnoise_destroy on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(e.ready,"_rnnoise_create")||(Object.defineProperty(e.ready,"_rnnoise_create",{configurable:!0,get:function(){t("You are getting _rnnoise_create on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(e.ready,"_rnnoise_create",{configurable:!0,set:function(){t("You are setting _rnnoise_create on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(e.ready,"_rnnoise_get_frame_size")||(Object.defineProperty(e.ready,"_rnnoise_get_frame_size",{configurable:!0,get:function(){t("You are getting _rnnoise_get_frame_size on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(e.ready,"_rnnoise_get_frame_size",{configurable:!0,set:function(){t("You are setting _rnnoise_get_frame_size on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(e.ready,"_rnnoise_model_from_string")||(Object.defineProperty(e.ready,"_rnnoise_model_from_string",{configurable:!0,get:function(){t("You are getting _rnnoise_model_from_string on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(e.ready,"_rnnoise_model_from_string",{configurable:!0,set:function(){t("You are setting _rnnoise_model_from_string on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(e.ready,"_rnnoise_model_free")||(Object.defineProperty(e.ready,"_rnnoise_model_free",{configurable:!0,get:function(){t("You are getting _rnnoise_model_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(e.ready,"_rnnoise_model_free",{configurable:!0,set:function(){t("You are setting _rnnoise_model_free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(e.ready,"_malloc")||(Object.defineProperty(e.ready,"_malloc",{configurable:!0,get:function(){t("You are getting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(e.ready,"_malloc",{configurable:!0,set:function(){t("You are setting _malloc on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(e.ready,"_free")||(Object.defineProperty(e.ready,"_free",{configurable:!0,get:function(){t("You are getting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(e.ready,"_free",{configurable:!0,set:function(){t("You are setting _free on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(e.ready,"_fflush")||(Object.defineProperty(e.ready,"_fflush",{configurable:!0,get:function(){t("You are getting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(e.ready,"_fflush",{configurable:!0,set:function(){t("You are setting _fflush on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}})),Object.getOwnPropertyDescriptor(e.ready,"onRuntimeInitialized")||(Object.defineProperty(e.ready,"onRuntimeInitialized",{configurable:!0,get:function(){t("You are getting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}),Object.defineProperty(e.ready,"onRuntimeInitialized",{configurable:!0,set:function(){t("You are setting onRuntimeInitialized on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")}}));var g=a({},e),b=typeof S=="object",f=typeof importScripts=="function",y=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string",m=!b&&!y&&!f;if(e.ENVIRONMENT)throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -s ENVIRONMENT=web or -s ENVIRONMENT=node)");var T="";function L(r){return e.locateFile?e.locateFile(r,T):T+r}var I,N,U,J,B,$;if(y){if(!(typeof process=="object"&&typeof j=="function"))throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");f?T=j("path").dirname(T)+"/":T=__dirname+"/",$=function(){B||(J=j("fs"),B=j("path"))},I=function(o,i){return $(),o=B.normalize(o),J.readFileSync(o,i?null:"utf8")},U=function(o){var i=I(o,!0);return i.buffer||(i=new Uint8Array(i)),s(i.buffer),i},N=function(o,i,d){$(),o=B.normalize(o),J.readFile(o,function(O,M){O?d(O):i(M.buffer)})},process.argv.length>1&&process.argv[1].replace(/\\/g,"/"),process.argv.slice(2),process.on("uncaughtException",function(r){if(!(r instanceof Ve))throw r}),process.on("unhandledRejection",function(r){throw r}),e.inspect=function(){return"[Emscripten Module object]"}}else if(m){if(typeof process=="object"&&typeof j=="function"||typeof S=="object"||typeof importScripts=="function")throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");typeof read<"u"&&(I=function(o){return read(o)}),U=function(o){let i;return typeof readbuffer=="function"?new Uint8Array(readbuffer(o)):(i=read(o,"binary"),s(typeof i=="object"),i)},N=function(o,i,d){setTimeout(()=>i(U(o)),0)},typeof scriptArgs<"u"&&scriptArgs,typeof print<"u"&&(typeof console>"u"&&(console={}),console.log=print,console.warn=console.error=typeof printErr<"u"?printErr:print)}else if(b||f){if(f?T=self.location.href:typeof document<"u"&&document.currentScript&&(T=document.currentScript.src),c&&(T=c),T.indexOf("blob:")!==0?T=T.substr(0,T.replace(/[?#].*/,"").lastIndexOf("/")+1):T="",!(typeof S=="object"||typeof importScripts=="function"))throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");I=function(r){var o=new XMLHttpRequest;return o.open("GET",r,!1),o.send(null),o.responseText},f&&(U=function(r){var o=new XMLHttpRequest;return o.open("GET",r,!1),o.responseType="arraybuffer",o.send(null),new Uint8Array(o.response)}),N=function(r,o,i){var d=new XMLHttpRequest;d.open("GET",r,!0),d.responseType="arraybuffer",d.onload=function(){if(d.status==200||d.status==0&&d.response){o(d.response);return}i()},d.onerror=i,d.send(null)}}else throw new Error("environment detection error");e.print||console.log.bind(console);var _=e.printErr||console.warn.bind(console);a(e,g),g=null,e.arguments,Object.getOwnPropertyDescriptor(e,"arguments")||Object.defineProperty(e,"arguments",{configurable:!0,get:function(){t("Module.arguments has been replaced with plain arguments_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),e.thisProgram,Object.getOwnPropertyDescriptor(e,"thisProgram")||Object.defineProperty(e,"thisProgram",{configurable:!0,get:function(){t("Module.thisProgram has been replaced with plain thisProgram (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),e.quit,Object.getOwnPropertyDescriptor(e,"quit")||Object.defineProperty(e,"quit",{configurable:!0,get:function(){t("Module.quit has been replaced with plain quit_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),s(typeof e.memoryInitializerPrefixURL>"u","Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead"),s(typeof e.pthreadMainPrefixURL>"u","Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead"),s(typeof e.cdInitializerPrefixURL>"u","Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead"),s(typeof e.filePackagePrefixURL>"u","Module.filePackagePrefixURL option was removed, use Module.locateFile instead"),s(typeof e.read>"u","Module.read option was removed (modify read_ in JS)"),s(typeof e.readAsync>"u","Module.readAsync option was removed (modify readAsync in JS)"),s(typeof e.readBinary>"u","Module.readBinary option was removed (modify readBinary in JS)"),s(typeof e.setWindowTitle>"u","Module.setWindowTitle option was removed (modify setWindowTitle in JS)"),s(typeof e.TOTAL_MEMORY>"u","Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY"),Object.getOwnPropertyDescriptor(e,"read")||Object.defineProperty(e,"read",{configurable:!0,get:function(){t("Module.read has been replaced with plain read_ (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(e,"readAsync")||Object.defineProperty(e,"readAsync",{configurable:!0,get:function(){t("Module.readAsync has been replaced with plain readAsync (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(e,"readBinary")||Object.defineProperty(e,"readBinary",{configurable:!0,get:function(){t("Module.readBinary has been replaced with plain readBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),Object.getOwnPropertyDescriptor(e,"setWindowTitle")||Object.defineProperty(e,"setWindowTitle",{configurable:!0,get:function(){t("Module.setWindowTitle has been replaced with plain setWindowTitle (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),s(!m,"shell environment detected but not enabled at build time.  Add 'shell' to `-s ENVIRONMENT` to enable.");function x(r){x.shown||(x.shown={}),x.shown[r]||(x.shown[r]=1,_(r))}var H;e.wasmBinary&&(H=e.wasmBinary),Object.getOwnPropertyDescriptor(e,"wasmBinary")||Object.defineProperty(e,"wasmBinary",{configurable:!0,get:function(){t("Module.wasmBinary has been replaced with plain wasmBinary (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),e.noExitRuntime,Object.getOwnPropertyDescriptor(e,"noExitRuntime")||Object.defineProperty(e,"noExitRuntime",{configurable:!0,get:function(){t("Module.noExitRuntime has been replaced with plain noExitRuntime (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),typeof WebAssembly!="object"&&t("no native wasm support detected");var k,W=!1;function s(r,o){r||t("Assertion failed"+(o?": "+o:""))}var Ee=typeof TextDecoder<"u"?new TextDecoder("utf8"):void 0;function Fe(r,o,i){for(var d=o+i,O=o;r[O]&&!(O>=d);)++O;if(O-o>16&&r.subarray&&Ee)return Ee.decode(r.subarray(o,O));for(var M="";o<O;){var l=r[o++];if(!(l&128)){M+=String.fromCharCode(l);continue}var w=r[o++]&63;if((l&224)==192){M+=String.fromCharCode((l&31)<<6|w);continue}var P=r[o++]&63;if((l&240)==224?l=(l&15)<<12|w<<6|P:((l&248)!=240&&x("Invalid UTF-8 leading byte 0x"+l.toString(16)+" encountered when deserializing a UTF-8 string in wasm memory to a JS string!"),l=(l&7)<<18|w<<12|P<<6|r[o++]&63),l<65536)M+=String.fromCharCode(l);else{var v=l-65536;M+=String.fromCharCode(55296|v>>10,56320|v&1023)}}return M}function ee(r,o){return r?Fe(Y,r,o):""}typeof TextDecoder<"u"&&new TextDecoder("utf-16le");function Ae(r,o){return r%o>0&&(r+=o-r%o),r}var te,Y,C,re;function Oe(r){te=r,e.HEAP8=new Int8Array(r),e.HEAP16=new Int16Array(r),e.HEAP32=C=new Int32Array(r),e.HEAPU8=Y=new Uint8Array(r),e.HEAPU16=new Uint16Array(r),e.HEAPU32=re=new Uint32Array(r),e.HEAPF32=new Float32Array(r),e.HEAPF64=new Float64Array(r)}var oe=5242880;e.TOTAL_STACK&&s(oe===e.TOTAL_STACK,"the stack size can no longer be determined at runtime");var ne=e.INITIAL_MEMORY||16777216;Object.getOwnPropertyDescriptor(e,"INITIAL_MEMORY")||Object.defineProperty(e,"INITIAL_MEMORY",{configurable:!0,get:function(){t("Module.INITIAL_MEMORY has been replaced with plain INITIAL_MEMORY (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)")}}),s(ne>=oe,"INITIAL_MEMORY should be larger than TOTAL_STACK, was "+ne+"! (TOTAL_STACK="+oe+")"),s(typeof Int32Array<"u"&&typeof Float64Array<"u"&&Int32Array.prototype.subarray!==void 0&&Int32Array.prototype.set!==void 0,"JS engine does not provide full typed array support"),s(!e.wasmMemory,"Use of `wasmMemory` detected.  Use -s IMPORTED_MEMORY to define wasmMemory externally"),s(ne==16777216,"Detected runtime INITIAL_MEMORY setting.  Use -s IMPORTED_MEMORY to define wasmMemory dynamically");var z;function ue(){var r=ce();s((r&3)==0),C[r+4>>2]=34821223,C[r+8>>2]=2310721022,C[0]=1668509029}function V(){if(!W){var r=ce(),o=re[r+4>>2],i=re[r+8>>2];(o!=34821223||i!=2310721022)&&t("Stack overflow! Stack cookie has been overwritten, expected hex dwords 0x89BACDFE and 0x2135467, but received 0x"+i.toString(16)+" 0x"+o.toString(16)),C[0]!==1668509029&&t("Runtime error: The application has corrupted its heap memory area (address zero)!")}}(function(){var r=new Int16Array(1),o=new Int8Array(r.buffer);if(r[0]=25459,o[0]!==115||o[1]!==99)throw"Runtime error: expected the system to be little-endian! (Run with -s SUPPORT_BIG_ENDIAN=1 to bypass)"})();var Te=[],_e=[],ge=[],ie=!1,Ie=!1;function Ue(){if(e.preRun)for(typeof e.preRun=="function"&&(e.preRun=[e.preRun]);e.preRun.length;)Ne(e.preRun.shift());ae(Te)}function ve(){V(),s(!ie),ie=!0,ae(_e)}function je(){if(V(),e.postRun)for(typeof e.postRun=="function"&&(e.postRun=[e.postRun]);e.postRun.length;)He(e.postRun.shift());ae(ge)}function Ne(r){Te.unshift(r)}function xe(r){_e.unshift(r)}function He(r){ge.unshift(r)}s(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),s(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),s(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill"),s(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var F=0,A=null,X=null,Q={};function ke(r){F++,e.monitorRunDependencies&&e.monitorRunDependencies(F),r?(s(!Q[r]),Q[r]=1,A===null&&typeof setInterval<"u"&&(A=setInterval(function(){if(W){clearInterval(A),A=null;return}var o=!1;for(var i in Q)o||(o=!0,_("still waiting on run dependencies:")),_("dependency: "+i);o&&_("(end of list)")},1e4))):_("warning: run dependency added without ID")}function Ce(r){if(F--,e.monitorRunDependencies&&e.monitorRunDependencies(F),r?(s(Q[r]),delete Q[r]):_("warning: run dependency removed without ID"),F==0&&(A!==null&&(clearInterval(A),A=null),X)){var o=X;X=null,o()}}e.preloadedImages={},e.preloadedAudios={};function t(r){e.onAbort&&e.onAbort(r),r="Aborted("+r+")",_(r),W=!0;var o=new WebAssembly.RuntimeError(r);throw u(o),o}var h={error:function(){t("Filesystem support (FS) was not included. The problem is that you are using files from JS, but files were not used from C/C++, so filesystem support was not auto-included. You can force-include filesystem support with  -s FORCE_FILESYSTEM=1")},init:function(){h.error()},createDataFile:function(){h.error()},createPreloadedFile:function(){h.error()},createLazyFile:function(){h.error()},open:function(){h.error()},mkdev:function(){h.error()},registerDevice:function(){h.error()},analyzePath:function(){h.error()},loadFilesFromDB:function(){h.error()},ErrnoError:function(){h.error()}};e.FS_createDataFile=h.createDataFile,e.FS_createPreloadedFile=h.createPreloadedFile;var Xe="data:application/octet-stream;base64,";function De(r){return r.startsWith(Xe)}function se(r){return r.startsWith("file://")}function D(r,o){return function(){var i=r,d=o;return o||(d=e.asm),s(ie,"native function `"+i+"` called before runtime initialization"),s(!Ie,"native function `"+i+"` called after runtime exit (use NO_EXIT_RUNTIME to keep it alive after main() exits)"),d[r]||s(d[r],"exported native function `"+i+"` not found"),d[r].apply(null,arguments)}}var E;e.locateFile?(E="rnnoise.wasm",De(E)||(E=L(E))):E=new URL("rnnoise.wasm",import.meta.url).toString();function ye(r){try{if(r==E&&H)return new Uint8Array(H);if(U)return U(r);throw"both async and sync fetching of the wasm failed"}catch(o){t(o)}}function Qe(){if(!H&&(b||f)){if(typeof fetch=="function"&&!se(E))return fetch(E,{credentials:"same-origin"}).then(function(r){if(!r.ok)throw"failed to load wasm binary file at '"+E+"'";return r.arrayBuffer()}).catch(function(){return ye(E)});if(N)return new Promise(function(r,o){N(E,function(i){r(new Uint8Array(i))},o)})}return Promise.resolve().then(function(){return ye(E)})}function Le(){var r={env:he,wasi_snapshot_preview1:he};function o(w,P){var v=w.exports;e.asm=v,k=e.asm.memory,s(k,"memory not found in wasm exports"),Oe(k.buffer),z=e.asm.__indirect_function_table,s(z,"table not found in wasm exports"),xe(e.asm.__wasm_call_ctors),Ce("wasm-instantiate")}ke("wasm-instantiate");var i=e;function d(w){s(e===i,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?"),i=null,o(w.instance)}function O(w){return Qe().then(function(P){return WebAssembly.instantiate(P,r)}).then(function(P){return P}).then(w,function(P){_("failed to asynchronously prepare wasm: "+P),se(E)&&_("warning: Loading from a file URI ("+E+") is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing"),t(P)})}function M(){return!H&&typeof WebAssembly.instantiateStreaming=="function"&&!De(E)&&!se(E)&&typeof fetch=="function"?fetch(E,{credentials:"same-origin"}).then(function(w){var P=WebAssembly.instantiateStreaming(w,r);return P.then(d,function(v){return _("wasm streaming compile failed: "+v),_("falling back to ArrayBuffer instantiation"),O(d)})}):O(d)}if(e.instantiateWasm)try{var l=e.instantiateWasm(r,o);return l}catch(w){return _("Module.instantiateWasm callback failed with error: "+w),!1}return M().catch(u),{}}function ae(r){for(;r.length>0;){var o=r.shift();if(typeof o=="function"){o(e);continue}var i=o.func;typeof i=="number"?o.arg===void 0?we(i)():we(i)(o.arg):i(o.arg===void 0?null:o.arg)}}var G=[];function we(r){var o=G[r];return o||(r>=G.length&&(G.length=r+1),G[r]=o=z.get(r)),s(z.get(r)==o,"JavaScript-side Wasm function table mirror is out of date!"),o}function Be(r,o,i,d){t("Assertion failed: "+ee(r)+", at: "+[o?ee(o):"unknown filename",i,d?ee(d):"unknown function"])}function We(r,o,i){Y.copyWithin(r,o,o+i)}function Ye(r){try{return k.grow(r-te.byteLength+65535>>>16),Oe(k.buffer),1}catch(o){_("emscripten_realloc_buffer: Attempted to grow heap from "+te.byteLength+" bytes to "+r+" bytes, but got error: "+o)}}function ze(r){var o=Y.length;r=r>>>0,s(r>o);var i=2147483648;if(r>i)return _("Cannot enlarge memory, asked to go up to "+r+" bytes, but the limit is "+i+" bytes!"),!1;for(var d=1;d<=4;d*=2){var O=o*(1+.2/d);O=Math.min(O,r+100663296);var M=Math.min(i,Ae(Math.max(r,O),65536)),l=Ye(M);if(l)return!0}return _("Failed to grow the heap from "+o+" bytes to "+M+" bytes, not enough memory!"),!1}var he={__assert_fail:Be,emscripten_memcpy_big:We,emscripten_resize_heap:ze};Le(),e.___wasm_call_ctors=D("__wasm_call_ctors"),e._rnnoise_get_frame_size=D("rnnoise_get_frame_size"),e._rnnoise_create=D("rnnoise_create"),e._rnnoise_destroy=D("rnnoise_destroy"),e._rnnoise_process_frame=D("rnnoise_process_frame"),e._rnnoise_model_free=D("rnnoise_model_free"),e._rnnoise_model_from_string=D("rnnoise_model_from_string"),e.___errno_location=D("__errno_location"),e._fflush=D("fflush");var Me=e._emscripten_stack_init=function(){return(Me=e._emscripten_stack_init=e.asm.emscripten_stack_init).apply(null,arguments)};e._emscripten_stack_get_free=function(){return(e._emscripten_stack_get_free=e.asm.emscripten_stack_get_free).apply(null,arguments)};var ce=e._emscripten_stack_get_end=function(){return(ce=e._emscripten_stack_get_end=e.asm.emscripten_stack_get_end).apply(null,arguments)};e.stackSave=D("stackSave"),e.stackRestore=D("stackRestore"),e.stackAlloc=D("stackAlloc"),e._malloc=D("malloc"),e._free=D("free"),Object.getOwnPropertyDescriptor(e,"intArrayFromString")||(e.intArrayFromString=function(){t("'intArrayFromString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"intArrayToString")||(e.intArrayToString=function(){t("'intArrayToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"ccall")||(e.ccall=function(){t("'ccall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"cwrap")||(e.cwrap=function(){t("'cwrap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"setValue")||(e.setValue=function(){t("'setValue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getValue")||(e.getValue=function(){t("'getValue' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"allocate")||(e.allocate=function(){t("'allocate' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"UTF8ArrayToString")||(e.UTF8ArrayToString=function(){t("'UTF8ArrayToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"UTF8ToString")||(e.UTF8ToString=function(){t("'UTF8ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stringToUTF8Array")||(e.stringToUTF8Array=function(){t("'stringToUTF8Array' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stringToUTF8")||(e.stringToUTF8=function(){t("'stringToUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"lengthBytesUTF8")||(e.lengthBytesUTF8=function(){t("'lengthBytesUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stackTrace")||(e.stackTrace=function(){t("'stackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"addOnPreRun")||(e.addOnPreRun=function(){t("'addOnPreRun' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"addOnInit")||(e.addOnInit=function(){t("'addOnInit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"addOnPreMain")||(e.addOnPreMain=function(){t("'addOnPreMain' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"addOnExit")||(e.addOnExit=function(){t("'addOnExit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"addOnPostRun")||(e.addOnPostRun=function(){t("'addOnPostRun' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"writeStringToMemory")||(e.writeStringToMemory=function(){t("'writeStringToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"writeArrayToMemory")||(e.writeArrayToMemory=function(){t("'writeArrayToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"writeAsciiToMemory")||(e.writeAsciiToMemory=function(){t("'writeAsciiToMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"addRunDependency")||(e.addRunDependency=function(){t("'addRunDependency' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(e,"removeRunDependency")||(e.removeRunDependency=function(){t("'removeRunDependency' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(e,"FS_createFolder")||(e.FS_createFolder=function(){t("'FS_createFolder' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"FS_createPath")||(e.FS_createPath=function(){t("'FS_createPath' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(e,"FS_createDataFile")||(e.FS_createDataFile=function(){t("'FS_createDataFile' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(e,"FS_createPreloadedFile")||(e.FS_createPreloadedFile=function(){t("'FS_createPreloadedFile' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(e,"FS_createLazyFile")||(e.FS_createLazyFile=function(){t("'FS_createLazyFile' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(e,"FS_createLink")||(e.FS_createLink=function(){t("'FS_createLink' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"FS_createDevice")||(e.FS_createDevice=function(){t("'FS_createDevice' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(e,"FS_unlink")||(e.FS_unlink=function(){t("'FS_unlink' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ). Alternatively, forcing filesystem support (-s FORCE_FILESYSTEM=1) can export this for you")}),Object.getOwnPropertyDescriptor(e,"getLEB")||(e.getLEB=function(){t("'getLEB' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getFunctionTables")||(e.getFunctionTables=function(){t("'getFunctionTables' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"alignFunctionTables")||(e.alignFunctionTables=function(){t("'alignFunctionTables' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerFunctions")||(e.registerFunctions=function(){t("'registerFunctions' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"addFunction")||(e.addFunction=function(){t("'addFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"removeFunction")||(e.removeFunction=function(){t("'removeFunction' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getFuncWrapper")||(e.getFuncWrapper=function(){t("'getFuncWrapper' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"prettyPrint")||(e.prettyPrint=function(){t("'prettyPrint' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"dynCall")||(e.dynCall=function(){t("'dynCall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getCompilerSetting")||(e.getCompilerSetting=function(){t("'getCompilerSetting' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"print")||(e.print=function(){t("'print' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"printErr")||(e.printErr=function(){t("'printErr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getTempRet0")||(e.getTempRet0=function(){t("'getTempRet0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"setTempRet0")||(e.setTempRet0=function(){t("'setTempRet0' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"callMain")||(e.callMain=function(){t("'callMain' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"abort")||(e.abort=function(){t("'abort' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"keepRuntimeAlive")||(e.keepRuntimeAlive=function(){t("'keepRuntimeAlive' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"zeroMemory")||(e.zeroMemory=function(){t("'zeroMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stringToNewUTF8")||(e.stringToNewUTF8=function(){t("'stringToNewUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"setFileTime")||(e.setFileTime=function(){t("'setFileTime' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"emscripten_realloc_buffer")||(e.emscripten_realloc_buffer=function(){t("'emscripten_realloc_buffer' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"ENV")||(e.ENV=function(){t("'ENV' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"withStackSave")||(e.withStackSave=function(){t("'withStackSave' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"ERRNO_CODES")||(e.ERRNO_CODES=function(){t("'ERRNO_CODES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"ERRNO_MESSAGES")||(e.ERRNO_MESSAGES=function(){t("'ERRNO_MESSAGES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"setErrNo")||(e.setErrNo=function(){t("'setErrNo' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"inetPton4")||(e.inetPton4=function(){t("'inetPton4' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"inetNtop4")||(e.inetNtop4=function(){t("'inetNtop4' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"inetPton6")||(e.inetPton6=function(){t("'inetPton6' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"inetNtop6")||(e.inetNtop6=function(){t("'inetNtop6' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"readSockaddr")||(e.readSockaddr=function(){t("'readSockaddr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"writeSockaddr")||(e.writeSockaddr=function(){t("'writeSockaddr' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"DNS")||(e.DNS=function(){t("'DNS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getHostByName")||(e.getHostByName=function(){t("'getHostByName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"GAI_ERRNO_MESSAGES")||(e.GAI_ERRNO_MESSAGES=function(){t("'GAI_ERRNO_MESSAGES' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"Protocols")||(e.Protocols=function(){t("'Protocols' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"Sockets")||(e.Sockets=function(){t("'Sockets' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getRandomDevice")||(e.getRandomDevice=function(){t("'getRandomDevice' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"traverseStack")||(e.traverseStack=function(){t("'traverseStack' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"convertFrameToPC")||(e.convertFrameToPC=function(){t("'convertFrameToPC' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"UNWIND_CACHE")||(e.UNWIND_CACHE=function(){t("'UNWIND_CACHE' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"saveInUnwindCache")||(e.saveInUnwindCache=function(){t("'saveInUnwindCache' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"convertPCtoSourceLocation")||(e.convertPCtoSourceLocation=function(){t("'convertPCtoSourceLocation' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"readAsmConstArgsArray")||(e.readAsmConstArgsArray=function(){t("'readAsmConstArgsArray' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"readAsmConstArgs")||(e.readAsmConstArgs=function(){t("'readAsmConstArgs' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"mainThreadEM_ASM")||(e.mainThreadEM_ASM=function(){t("'mainThreadEM_ASM' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"jstoi_q")||(e.jstoi_q=function(){t("'jstoi_q' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"jstoi_s")||(e.jstoi_s=function(){t("'jstoi_s' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getExecutableName")||(e.getExecutableName=function(){t("'getExecutableName' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"listenOnce")||(e.listenOnce=function(){t("'listenOnce' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"autoResumeAudioContext")||(e.autoResumeAudioContext=function(){t("'autoResumeAudioContext' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"dynCallLegacy")||(e.dynCallLegacy=function(){t("'dynCallLegacy' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getDynCaller")||(e.getDynCaller=function(){t("'getDynCaller' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"dynCall")||(e.dynCall=function(){t("'dynCall' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"callRuntimeCallbacks")||(e.callRuntimeCallbacks=function(){t("'callRuntimeCallbacks' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"wasmTableMirror")||(e.wasmTableMirror=function(){t("'wasmTableMirror' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"setWasmTableEntry")||(e.setWasmTableEntry=function(){t("'setWasmTableEntry' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getWasmTableEntry")||(e.getWasmTableEntry=function(){t("'getWasmTableEntry' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"handleException")||(e.handleException=function(){t("'handleException' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"runtimeKeepalivePush")||(e.runtimeKeepalivePush=function(){t("'runtimeKeepalivePush' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"runtimeKeepalivePop")||(e.runtimeKeepalivePop=function(){t("'runtimeKeepalivePop' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"callUserCallback")||(e.callUserCallback=function(){t("'callUserCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"maybeExit")||(e.maybeExit=function(){t("'maybeExit' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"safeSetTimeout")||(e.safeSetTimeout=function(){t("'safeSetTimeout' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"asmjsMangle")||(e.asmjsMangle=function(){t("'asmjsMangle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"asyncLoad")||(e.asyncLoad=function(){t("'asyncLoad' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"alignMemory")||(e.alignMemory=function(){t("'alignMemory' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"mmapAlloc")||(e.mmapAlloc=function(){t("'mmapAlloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"reallyNegative")||(e.reallyNegative=function(){t("'reallyNegative' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"unSign")||(e.unSign=function(){t("'unSign' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"reSign")||(e.reSign=function(){t("'reSign' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"formatString")||(e.formatString=function(){t("'formatString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"PATH")||(e.PATH=function(){t("'PATH' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"PATH_FS")||(e.PATH_FS=function(){t("'PATH_FS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"SYSCALLS")||(e.SYSCALLS=function(){t("'SYSCALLS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"syscallMmap2")||(e.syscallMmap2=function(){t("'syscallMmap2' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"syscallMunmap")||(e.syscallMunmap=function(){t("'syscallMunmap' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getSocketFromFD")||(e.getSocketFromFD=function(){t("'getSocketFromFD' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getSocketAddress")||(e.getSocketAddress=function(){t("'getSocketAddress' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"JSEvents")||(e.JSEvents=function(){t("'JSEvents' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerKeyEventCallback")||(e.registerKeyEventCallback=function(){t("'registerKeyEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"specialHTMLTargets")||(e.specialHTMLTargets=function(){t("'specialHTMLTargets' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"maybeCStringToJsString")||(e.maybeCStringToJsString=function(){t("'maybeCStringToJsString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"findEventTarget")||(e.findEventTarget=function(){t("'findEventTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"findCanvasEventTarget")||(e.findCanvasEventTarget=function(){t("'findCanvasEventTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getBoundingClientRect")||(e.getBoundingClientRect=function(){t("'getBoundingClientRect' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"fillMouseEventData")||(e.fillMouseEventData=function(){t("'fillMouseEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerMouseEventCallback")||(e.registerMouseEventCallback=function(){t("'registerMouseEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerWheelEventCallback")||(e.registerWheelEventCallback=function(){t("'registerWheelEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerUiEventCallback")||(e.registerUiEventCallback=function(){t("'registerUiEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerFocusEventCallback")||(e.registerFocusEventCallback=function(){t("'registerFocusEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"fillDeviceOrientationEventData")||(e.fillDeviceOrientationEventData=function(){t("'fillDeviceOrientationEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerDeviceOrientationEventCallback")||(e.registerDeviceOrientationEventCallback=function(){t("'registerDeviceOrientationEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"fillDeviceMotionEventData")||(e.fillDeviceMotionEventData=function(){t("'fillDeviceMotionEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerDeviceMotionEventCallback")||(e.registerDeviceMotionEventCallback=function(){t("'registerDeviceMotionEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"screenOrientation")||(e.screenOrientation=function(){t("'screenOrientation' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"fillOrientationChangeEventData")||(e.fillOrientationChangeEventData=function(){t("'fillOrientationChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerOrientationChangeEventCallback")||(e.registerOrientationChangeEventCallback=function(){t("'registerOrientationChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"fillFullscreenChangeEventData")||(e.fillFullscreenChangeEventData=function(){t("'fillFullscreenChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerFullscreenChangeEventCallback")||(e.registerFullscreenChangeEventCallback=function(){t("'registerFullscreenChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerRestoreOldStyle")||(e.registerRestoreOldStyle=function(){t("'registerRestoreOldStyle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"hideEverythingExceptGivenElement")||(e.hideEverythingExceptGivenElement=function(){t("'hideEverythingExceptGivenElement' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"restoreHiddenElements")||(e.restoreHiddenElements=function(){t("'restoreHiddenElements' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"setLetterbox")||(e.setLetterbox=function(){t("'setLetterbox' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"currentFullscreenStrategy")||(e.currentFullscreenStrategy=function(){t("'currentFullscreenStrategy' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"restoreOldWindowedStyle")||(e.restoreOldWindowedStyle=function(){t("'restoreOldWindowedStyle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"softFullscreenResizeWebGLRenderTarget")||(e.softFullscreenResizeWebGLRenderTarget=function(){t("'softFullscreenResizeWebGLRenderTarget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"doRequestFullscreen")||(e.doRequestFullscreen=function(){t("'doRequestFullscreen' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"fillPointerlockChangeEventData")||(e.fillPointerlockChangeEventData=function(){t("'fillPointerlockChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerPointerlockChangeEventCallback")||(e.registerPointerlockChangeEventCallback=function(){t("'registerPointerlockChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerPointerlockErrorEventCallback")||(e.registerPointerlockErrorEventCallback=function(){t("'registerPointerlockErrorEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"requestPointerLock")||(e.requestPointerLock=function(){t("'requestPointerLock' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"fillVisibilityChangeEventData")||(e.fillVisibilityChangeEventData=function(){t("'fillVisibilityChangeEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerVisibilityChangeEventCallback")||(e.registerVisibilityChangeEventCallback=function(){t("'registerVisibilityChangeEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerTouchEventCallback")||(e.registerTouchEventCallback=function(){t("'registerTouchEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"fillGamepadEventData")||(e.fillGamepadEventData=function(){t("'fillGamepadEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerGamepadEventCallback")||(e.registerGamepadEventCallback=function(){t("'registerGamepadEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerBeforeUnloadEventCallback")||(e.registerBeforeUnloadEventCallback=function(){t("'registerBeforeUnloadEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"fillBatteryEventData")||(e.fillBatteryEventData=function(){t("'fillBatteryEventData' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"battery")||(e.battery=function(){t("'battery' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"registerBatteryEventCallback")||(e.registerBatteryEventCallback=function(){t("'registerBatteryEventCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"setCanvasElementSize")||(e.setCanvasElementSize=function(){t("'setCanvasElementSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getCanvasElementSize")||(e.getCanvasElementSize=function(){t("'getCanvasElementSize' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"demangle")||(e.demangle=function(){t("'demangle' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"demangleAll")||(e.demangleAll=function(){t("'demangleAll' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"jsStackTrace")||(e.jsStackTrace=function(){t("'jsStackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stackTrace")||(e.stackTrace=function(){t("'stackTrace' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getEnvStrings")||(e.getEnvStrings=function(){t("'getEnvStrings' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"checkWasiClock")||(e.checkWasiClock=function(){t("'checkWasiClock' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"flush_NO_FILESYSTEM")||(e.flush_NO_FILESYSTEM=function(){t("'flush_NO_FILESYSTEM' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"writeI53ToI64")||(e.writeI53ToI64=function(){t("'writeI53ToI64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"writeI53ToI64Clamped")||(e.writeI53ToI64Clamped=function(){t("'writeI53ToI64Clamped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"writeI53ToI64Signaling")||(e.writeI53ToI64Signaling=function(){t("'writeI53ToI64Signaling' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"writeI53ToU64Clamped")||(e.writeI53ToU64Clamped=function(){t("'writeI53ToU64Clamped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"writeI53ToU64Signaling")||(e.writeI53ToU64Signaling=function(){t("'writeI53ToU64Signaling' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"readI53FromI64")||(e.readI53FromI64=function(){t("'readI53FromI64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"readI53FromU64")||(e.readI53FromU64=function(){t("'readI53FromU64' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"convertI32PairToI53")||(e.convertI32PairToI53=function(){t("'convertI32PairToI53' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"convertU32PairToI53")||(e.convertU32PairToI53=function(){t("'convertU32PairToI53' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"setImmediateWrapped")||(e.setImmediateWrapped=function(){t("'setImmediateWrapped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"clearImmediateWrapped")||(e.clearImmediateWrapped=function(){t("'clearImmediateWrapped' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"polyfillSetImmediate")||(e.polyfillSetImmediate=function(){t("'polyfillSetImmediate' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"Browser")||(e.Browser=function(){t("'Browser' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"funcWrappers")||(e.funcWrappers=function(){t("'funcWrappers' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"getFuncWrapper")||(e.getFuncWrapper=function(){t("'getFuncWrapper' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"setMainLoop")||(e.setMainLoop=function(){t("'setMainLoop' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"wget")||(e.wget=function(){t("'wget' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"FS")||(e.FS=function(){t("'FS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"MEMFS")||(e.MEMFS=function(){t("'MEMFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"TTY")||(e.TTY=function(){t("'TTY' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"PIPEFS")||(e.PIPEFS=function(){t("'PIPEFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"SOCKFS")||(e.SOCKFS=function(){t("'SOCKFS' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"_setNetworkCallback")||(e._setNetworkCallback=function(){t("'_setNetworkCallback' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"warnOnce")||(e.warnOnce=function(){t("'warnOnce' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stackSave")||(e.stackSave=function(){t("'stackSave' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stackRestore")||(e.stackRestore=function(){t("'stackRestore' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stackAlloc")||(e.stackAlloc=function(){t("'stackAlloc' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"AsciiToString")||(e.AsciiToString=function(){t("'AsciiToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stringToAscii")||(e.stringToAscii=function(){t("'stringToAscii' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"UTF16ToString")||(e.UTF16ToString=function(){t("'UTF16ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stringToUTF16")||(e.stringToUTF16=function(){t("'stringToUTF16' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"lengthBytesUTF16")||(e.lengthBytesUTF16=function(){t("'lengthBytesUTF16' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"UTF32ToString")||(e.UTF32ToString=function(){t("'UTF32ToString' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"stringToUTF32")||(e.stringToUTF32=function(){t("'stringToUTF32' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"lengthBytesUTF32")||(e.lengthBytesUTF32=function(){t("'lengthBytesUTF32' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"allocateUTF8")||(e.allocateUTF8=function(){t("'allocateUTF8' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),Object.getOwnPropertyDescriptor(e,"allocateUTF8OnStack")||(e.allocateUTF8OnStack=function(){t("'allocateUTF8OnStack' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}),e.writeStackCookie=ue,e.checkStackCookie=V,Object.getOwnPropertyDescriptor(e,"ALLOC_NORMAL")||Object.defineProperty(e,"ALLOC_NORMAL",{configurable:!0,get:function(){t("'ALLOC_NORMAL' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}}),Object.getOwnPropertyDescriptor(e,"ALLOC_STACK")||Object.defineProperty(e,"ALLOC_STACK",{configurable:!0,get:function(){t("'ALLOC_STACK' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the FAQ)")}});var K;function Ve(r){this.name="ExitStatus",this.message="Program terminated with exit("+r+")",this.status=r}X=function r(){K||de(),K||(X=r)};function Ge(){Me(),ue()}function de(r){if(F>0||(Ge(),Ue(),F>0))return;function o(){K||(K=!0,e.calledRun=!0,!W&&(ve(),p(e),e.onRuntimeInitialized&&e.onRuntimeInitialized(),s(!e._main,'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]'),je()))}e.setStatus?(e.setStatus("Running..."),setTimeout(function(){setTimeout(function(){e.setStatus("")},1),o()},1)):o(),V()}if(e.run=de,e.preInit)for(typeof e.preInit=="function"&&(e.preInit=[e.preInit]);e.preInit.length>0;)e.preInit.pop()();return de(),e.ready}})(),q=class c{constructor(n){R(this,"rnnoiseModule");R(this,"frameSize");this.rnnoiseModule=n,this.frameSize=n._rnnoise_get_frame_size()}static async load(n={}){let e=await Je().then(a=>Pe({locateFile:(p,u)=>(n.assetsPath!==void 0&&(u=n.assetsPath+"/"),n.wasmFileName!==void 0?(p=n.wasmFileName,console.debug("Loads rnnoise-wasm: ",u+p)):a?(p="rnnoise_simd.wasm",console.debug("Loads rnnoise-wasm (SIMD ver): ",u+p)):console.debug("Loads rnnoise-wasm (non SIMD ver): ",u+p),u+p)}));return Promise.resolve(new c(e))}static async loadBinary(n){let e=await Pe({locateFile:a=>a,wasmBinary:n});return new c(e)}createDenoiseState(n){return new pe(this.rnnoiseModule,n)}createModel(n){return new le(this.rnnoiseModule,n)}},Z=4,pe=class{constructor(n,e){R(this,"rnnoiseModule");R(this,"state");R(this,"pcmInputBuf");R(this,"pcmOutputBuf");R(this,"frameSize");R(this,"model");this.rnnoiseModule=n,this.model=e,this.frameSize=this.rnnoiseModule._rnnoise_get_frame_size();let a;e!==void 0?a=this.rnnoiseModule._rnnoise_create(e.model):a=this.rnnoiseModule._rnnoise_create();let p=this.rnnoiseModule._malloc(this.frameSize*Z),u=this.rnnoiseModule._malloc(this.frameSize*Z);if(!a||!p||!u)throw this.destroy(),Error("Failed to allocate DenoiseState or PCM buffers.");this.state=a,this.pcmInputBuf=p,this.pcmOutputBuf=u}processFrame(n){if(this.rnnoiseModule===void 0)throw Error("This denoise state has already been destroyed.");if(n.length!=this.frameSize)throw Error(`Expected frame size ${this.frameSize}, but got ${n.length}`);let e=this.pcmInputBuf/Z,a=this.pcmOutputBuf/Z;this.rnnoiseModule.HEAPF32.set(n,e);let p=this.rnnoiseModule._rnnoise_process_frame(this.state,this.pcmOutputBuf,this.pcmInputBuf);return n.set(this.rnnoiseModule.HEAPF32.subarray(a,a+this.frameSize)),p}destroy(){this.rnnoiseModule!==void 0&&(this.rnnoiseModule._rnnoise_destroy(this.state),this.rnnoiseModule._free(this.pcmInputBuf),this.rnnoiseModule._free(this.pcmOutputBuf),this.rnnoiseModule=void 0)}},le=class{constructor(n,e){R(this,"rnnoiseModule");R(this,"model");this.rnnoiseModule=n;let a=new TextEncoder().encode(e+"\0"),p=n._malloc(a.length);if(n.HEAPU8.subarray(p,p+a.length).set(a),this.model=n._rnnoise_model_from_string(p),n._free(p),!this.model)throw Error("Failed to create Model from a given model string.")}free(){this.rnnoiseModule!==void 0&&(this.rnnoiseModule._rnnoise_model_free(this.model),this.rnnoiseModule=void 0)}};var Re=c=>{for(let[n,e]of c.entries())c[n]=e*32767},be=c=>{for(let[n,e]of c.entries())c[n]=e/32767};var $e=c=>{let n=c.createDenoiseState(),e=m=>{Re(m),n.processFrame(m),be(m)},a=128,p=480,u=(Math.floor(p/a)+1)*a+a,g=1920,b=new Float32Array(g),f=0,y=g-p*2;return{process:(m,T)=>{if(b.set(m,f),f=(f+a)%g,f===128||f===512||f===1024||f===1536){y=(y+p)%g;let I=b.subarray(y,y+p);e(I)}let L=(f+(g-u))%g;T.set(b.subarray(L,L+a))},destroy:()=>{n.destroy()}}},me=(c,{bufferSize:n,maxChannels:e})=>{if(c.frameSize!==480)throw new Error(`rnnoise frameSize must be 480. (was ${c.frameSize})`);if(n!==128)throw new Error(`bufferSize must be 128. (was ${n}).`);let a=Array.from({length:e},()=>$e(c));return{process:(g,b)=>{let f=Math.min(g.length,e);for(let y=0;y<f;y++)a[y].process(g[y],b[y])},destroy:()=>{for(let g of a)g.destroy()}}};var Se="@sapphi-red/web-noise-suppressor/rnnoise";var et=128,fe=class extends AudioWorkletProcessor{constructor(e){super();this.destroyed=!1;this.port.addEventListener("message",a=>{a.data==="destroy"&&this.destroy()}),(async()=>{let a=await q.loadBinary(e.processorOptions.wasmBinary);this.processor=me(a,{bufferSize:et,maxChannels:e.processorOptions.maxChannels}),this.destroyed&&this.destroy()})()}process(e,a,p){return e.length===0||!e[0]||e[0]?.length===0||!this.processor||this.processor.process(e[0],a[0]),!0}destroy(){this.destroyed=!0,this.processor?.destroy(),this.processor=void 0}};registerProcessor(Se,fe);
/*! Bundled license information:

@shiguredo/rnnoise-wasm/dist/rnnoise.mjs:
  (**
   * @shiguredo/rnnoise-wasm
   * SIMD-accelerated WebAssembly build of RNNoise
   * @version: 2022.2.0
   * @author: Shiguredo Inc.
   * @license: Apache-2.0
   **)
*/
//# sourceMappingURL=workletProcessor.js.map