# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "bf4c779259de11e69362150ed70a9533"
name = "feisty-voice-studio"
handle = "feisty-voice-studio"
application_url = "https://patrol-orchestra-keys-blend.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = false
dev_store_url = "foodcube-dev.myshopify.com"
include_config_on_deploy = true

[webhooks]
api_version = "2025-01"

  [[webhooks.subscriptions]]
  topics = [ "app/scopes_update" ]
  uri = "/webhooks/app/scopes_update"

  [[webhooks.subscriptions]]
  topics = [ "app/uninstalled" ]
  uri = "/webhooks/app/uninstalled"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "read_content,read_customers,read_product_listings,write_products"

[auth]
redirect_urls = [
  "https://patrol-orchestra-keys-blend.trycloudflare.com/auth/callback",
  "https://patrol-orchestra-keys-blend.trycloudflare.com/auth/shopify/callback",
  "https://patrol-orchestra-keys-blend.trycloudflare.com/api/auth/callback"
]

[app_proxy]
url = "https://patrol-orchestra-keys-blend.trycloudflare.com/proxy"
subpath = "voice"
prefix = "apps"

[pos]
embedded = false
