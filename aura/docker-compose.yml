services:
  # LiveKit server for WebRTC connections
  livekit:
    build:
      context: .
      dockerfile: Dockerfile.livekit
    network_mode: host
    environment:
      - 'LIVEKIT_KEYS=APIAjHeVcAhyYvB: fZgrWmDYX9QvBDdwY52SIa1LelJPNMA35LGBtgLuDTK'
    healthcheck:
      test: ["CMD", "wget", "-q", "--spider", "http://live.fy.studio:7880/health"]
      interval: 30s
      timeout: 5s
      retries: 3

  # Bot service for voice assistant
  voice-bot:
    build:
      context: .
      dockerfile: Dockerfile.livekit-proxy
    depends_on:
      - livekit
    environment:
      - NODE_ENV=production
      - LIVEKIT_URL=wss://jarvis-yd6nbc0x.livekit.cloud
      - LIVEKIT_API_KEY=APIAjHeVcAhyYvB
      - LIVEKIT_API_SECRET=fZgrWmDYX9QvBDdwY52SIa1LelJPNMA35LGBtgLuDTK
      - REPLICATE_API_TOKEN=****************************************
      - PLAYHT_USER_ID=${PLAYHT_USER_ID}
      - PLAYHT_SECRET_KEY=${PLAYHT_SECRET_KEY}
      - LIVEKIT_LOG=info
    restart: unless-stopped
    ports:
      - "3000:3000"
    network_mode: host

# We don't need to define network when using host mode