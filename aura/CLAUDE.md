# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build/Run Commands
- `npm run dev` - Start development server
- `npm run build` - Build the app
- `npm run lint` - Run ESLint
- `npm run setup` - Generate Prisma client and run migrations
- `npm run setup:voice-assistant` - Start LiveKit proxy and test connection
- `npm run test:livekit-connection` - Test LiveKit connection

## Code Style Guidelines
- **TypeScript**: Strict mode enabled, use proper types (avoid `any`)
- **Imports**: Organize alphabetically in groups (built-in, external, internal)
- **Naming**: kebab-case for files, PascalCase for components/classes, camelCase for variables/functions
- **Services**: Follow singleton pattern with getter functions (e.g., `getServiceInstance()`)
- **Logging**: Use prefixed console logs: `[ServiceName] message`
- **Error Handling**: Use try/catch with detailed error logging
- **JSDoc**: Document all public functions and classes
- **Events**: Use typed event emitters with interfaces
- **State Management**: React Context API & Hooks
- **CSS**: CSS Modules with kebab-case class names

## Architecture & Patterns
- **Frontend**: React with Shopify Polaris components
- **Backend**: Node.js with Remix framework
- **Database**: Prisma ORM
- **Voice Processing**: LiveKit for WebRTC, Play.ht for TTS
- **Directory Structure**: See /docs/technical-guidelines.md
- **Git Workflow**: feature/fix/refactor branches, conventional commits

## Environment Requirements
- Node.js >= 18.20
- Required env vars: LIVEKIT_URL, LIVEKIT_KEY, LIVEKIT_SECRET, REPLICATE_API_TOKEN