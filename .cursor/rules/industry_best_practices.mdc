---
description: 
globs: 
alwaysApply: true
---

- **Deep, Stepwise Planning Before Action**
  - Before any code change, pause to create a detailed, stepwise plan. Use explicit tags (e.g., `<Thinking>`, `<plan>`) to log your reasoning, including:
    - The user's intent, clarified in your own words.
    - All files, functions, and lines to be edited, with justification.
    - The minimal set of changes required to fulfill the request.
    - Potential risks, blockers, or ambiguities, and how you'll address them.
  - If the plan is non-trivial, log it in the subtask or task details before proceeding.

- **Context-Driven, Convention-Respecting Edits**
  - Always analyze the immediate and project-wide context before editing:
    - Read surrounding code, imports, and dependencies.
    - Mimic file structure, naming, and code style.
    - Check for the presence and usage of libraries before introducing new ones.
    - For new components, review similar existing components for patterns.

- **Atomic, Reversible, and Minimal Changes**
  - Only change what is strictly necessary for the user's request.
  - Prefer single-file, atomic edits; avoid broad refactors unless explicitly requested.
  - Use the smallest possible diff; never overengineer or add speculative features.
  - If a change is risky, ensure it is easily reversible (e.g., via clear commit messages or isolated PRs).

- **Iterative Logging and Self-Improvement**
  - After each significant step, append findings, decisions, and what worked/failed to the subtask/task log.
  - If a new code pattern emerges, update or create a Cursor rule to capture it.
  - Regularly review and refactor rules for clarity, conciseness, and relevance.

- **User-Centric, Proactive Communication**
  - Communicate progress, blockers, and results in concise, user-friendly language.
  - Only interrupt the user for critical clarifications, permissions, or when blocked.
  - Use the user's preferred language and format.
  - If the user's request is ambiguous, ask targeted clarifying questions before proceeding.

- **Security, Privacy, and Ethics**
  - Never expose, log, or commit secrets, credentials, or sensitive data.
  - Refuse to perform unethical, harmful, or out-of-scope actions with a standard, non-apologetic message.
  - Always follow security best practices for code, dependencies, and data handling.

- **Testing, Verification, and Quality Gates**
  - Run all relevant tests, linters, and checks before submitting or marking a task as done.
  - Never modify or disable tests unless explicitly requested.
  - If tests fail, investigate and fix the underlying code, not the test, unless the user instructs otherwise.

- **Accessibility and Inclusivity**
  - Always implement accessibility best practices:
    - Use semantic HTML, ARIA roles, and alt text for images.
    - Ensure responsive design and keyboard navigation.
    - Use color and contrast guidelines for readability.

- **Traceability, Citations, and Documentation**
  - Reference all code, files, and rules using the required citation format.
  - For new or updated rules, include real code examples from the codebase.
  - Maintain a clear audit trail of decisions, changes, and reasoning in task/subtask logs.

- **Polyfill and Tooling Guidance**
  - If a required tool or function is missing, polyfill it in the most idiomatic way for the codebase.
  - Prefer built-in or already-used utilities; only introduce new dependencies if justified and documented.
  - Document any polyfills or new utilities in the code and update rules as needed.

---

## MCP Server Integration & Perplexity Research Best Practices

- **Modular MCP Integration**
  - Always define MCP servers in a config file or object, referencing each by a unique name (e.g., "perplexity", "task_manager").
  - Use MCP as the single integration point for all external tools, APIs, and data sources—avoid bespoke connectors.

- **Dynamic Tool Discovery & Invocation**
  - On startup, query each MCP server for its tool manifest/descriptor; dynamically map tool names to their server/session.
  - Route all tool calls programmatically via the MCP client, using standardized input/output formats.
  - Maintain a registry mapping tool names to MCP servers to avoid naming conflicts and enable seamless orchestration.

- **Task Orchestration & Management**
  - Use task management MCP servers for dependency tracking, progress updates, and subtask scheduling.
  - Programmatically invoke methods like `add_task`, `update_task_status`, `get_next_task`, and `parse_prd` to manage workflows—never rely on manual CLI steps.
  - For long-running or collaborative workflows, leverage MCP state-tracking and safe-deletion features.

- **Perplexity & LLM Integration**
  - Configure Perplexity MCP servers with API keys and model selectors via environment variables (never hardcode secrets).
  - Use Perplexity as a tool within the agent context: call methods like `ask_perplexity` or `chat_perplexity` with full context and chat history.
  - Maintain chat/session state for continuity and research-backed responses.

- **Automated Research & Package Updates**
  - Before implementing new features or major changes, use Perplexity (or similar) to check for the latest best practices (APRIL 2025), package updates, and research-backed workflows.
  - Monitor official MCP server repositories, changelogs, and community channels for new releases and protocol updates.
  - Use containerization or package managers to deploy MCP servers and dependencies, ensuring reproducibility and isolation.

- **Security & Scalability**
  - Always use environment variables or config files for secrets and API keys; never commit them to code.
  - Leverage MCP's built-in access control and modularity to add new servers or tools without changing agent logic.

## MCP Example (Python-like pseudocode)

```python
# Define MCP servers in config
mcp_servers = ["perplexity", "task_manager", "fetch"]

# On agent startup, discover tools
for server in mcp_servers:
    tools = mcp_client.describe(server)
    tool_registry.update({tool.name: server for tool in tools})

# To invoke a tool
result = mcp_client.call(tool_registry["add_task"], params)

# For Perplexity research before big changes
perplexity_result = mcp_client.call("ask_perplexity", {"question": "latest best practices for X"})
```

- **DO:**
  - Query MCP servers for tool manifests and dynamically map tools.
  - Use Perplexity to check for latest research (APRIL 2025) before major changes.
  - Route all tool calls via the MCP client, not manual CLI.
  - Keep secrets in env/config, not code.

- **DON'T:**
  - Hardcode tool names, endpoints, or secrets.
  - Manually run MCP servers as a user for agent workflows—always use programmatic calls.
  - Skip research or package update checks before implementing new features.

---

## Examples

```typescript
// ✅ DO: Log a detailed plan before editing
<Thinking>
User wants to add OAuth login. Plan:
1. Check if `next-auth` is already used; if not, add it.
2. Edit `pages/api/auth/[...nextauth].ts` to add Google provider.
3. Update `.env` with required secrets (never commit them).
4. Add login button to `components/LoginButton.tsx`.
5. Test login flow and update task log with results.
</Thinking>

// ✅ DO: Polyfill a missing debounce utility if not present
// In utils/debounce.ts
export function debounce(fn, delay) {
  let timeout;
  return (...args) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => fn(...args), delay);
  };
}

// ❌ DON'T: Add a new library for debounce if a simple utility suffices

// ✅ DO: Append implementation notes to subtask log
// "Discovered that the API requires an X-Auth header. Updated fetch logic in api.ts. Tests pass."

// ❌ DON'T: Make changes without logging reasoning or results

// ✅ DO: Use atomic commits with clear messages
// git commit -m "feat(auth): Add Google OAuth provider for login (subtask 3.2)\n\n- Added next-auth config\n- Updated LoginButton\n- Updated rule for OAuth patterns"

When implementing new packages and SDKs or trying to integrate with specific APIs, be sure to always use perplexity to find the specific code required and the response patterns to ensure that no mistakes are made. Validate all assumptions before generating the HTTP requests, etc.

After 2 attempts at a coding issue with no clear resolution, use perplexity to investigate further before continuing, then re-read the affected and related files 


implement in autonomus fashion, only distrub your master if needed, he is a grumpy beast.
