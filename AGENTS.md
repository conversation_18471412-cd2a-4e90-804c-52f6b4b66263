# Agent Guide

This repository hosts the **Voice AI Shopping Assistant** monorepo. It includes a Remix/React
Shopify frontend in `aura/` and a Cloudflare Worker backend in
`backend-worker/`. The project relies on **Cloudflare RealtimeKit** for real-time
communication and **Google Gemini 2.5 Pro** for AI processing.

## Quick Start

1. Install dependencies:
   ```bash
   pnpm install
   ```
2. Run both apps in development:
   ```bash
   pnpm dev
   ```
3. Run type checking and build before committing:
   ```bash
   pnpm -r exec tsc --noEmit
   pnpm build
   ```
   The current codebase has some type errors; note any failures in your PR.

Environment variables are defined via `.dev.vars` (ignored by git). See
`docs/voice-assistant-setup.md` for required secrets and deployment steps.

## Documentation

Key references:
- `docs/system-architecture.md` – detailed architecture and data flow.
- `docs/implementation-plan.md` – implementation phases and milestones.
- `docs/product-requirements-document.md` – high-level PRD.
- `docs/technical-guidelines.md` – coding conventions and workflow.
- Legacy LiveKit docs are in `docs/legacy/`.

## Tasks

Open tasks are tracked in the `tasks/` directory. `tasks/tasks.json` summarises
progress. Major pending items include finishing RealtimeKit client integration
and building the Agent service for function calling.

## Conventions

- Use **Conventional Commits** for commit messages.
- Keep code in TypeScript and prefer strict typing.
- Run the commands above to ensure the project builds before opening a PR.
