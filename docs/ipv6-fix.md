# IPv6 Binding Issues Fix

## Issue

The application was experiencing errors related to IPv6 address binding, particularly with WebSocket servers:

```
WebSocket server error:
Error: listen EADDRNOTAVAIL: address not available 2606:4700::6810:e684:64073
    at Server.setupListenHandle [as _listen2] (node:net:1917:21)
    at listenInCluster (node:net:1996:12)
    at GetAddrInfoReqWrap.callback (node:net:2205:7)
    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)
```

This occurred because by default, Node.js tries to bind to both IPv4 and IPv6 addresses, but sometimes the IPv6 address is not actually available or properly configured in the development environment.

## Solution

We implemented several changes to explicitly prefer IPv4 and avoid IPv6 binding issues:

1. **Force IPv4 in Vite Configuration**
   - Updated `vite.config.ts` to explicitly set `host: "localhost"` instead of allowing automatic IPv6 binding
   - Set a fixed port for HMR (Hot Module Replacement) to make it more consistent

2. **DNS Resolution Order in Node.js**
   - Added `process.env.NODE_OPTIONS = '--dns-result-order=ipv4first'` to prefer IPv4 addresses when resolving DNS
   - This is especially important for WebSocket connections

3. **Consistent Port Configuration**
   - Set fixed ports for both the main server (54029) and HMR WebSocket server (54030)
   - This ensures consistent behavior across restarts

## Key Changes

### 1. Updated Vite Config (`vite.config.ts`)

```javascript
export default defineConfig({
  server: {
    allowedHosts: [host, "aura.fy.studio"],
    cors: {
      preflightContinue: true,
    },
    port: 54029, // Hardcoded port for consistent access
    host: "localhost", // Force IPv4 only to avoid IPv6 issues
    hmr: {
      ...hmrConfig,
      host: "localhost", // Force IPv4 for HMR as well
    },
    fs: {
      allow: ["app", "node_modules"],
    },
  },
});
```

### 2. Fixed HMR Configuration

```javascript
// Set up consistent HMR configuration 
let hmrConfig;
if (host === "localhost") {
  hmrConfig = {
    protocol: "ws",
    host: "localhost",
    port: 54030, // Use fixed port for HMR to avoid random port assignment
    clientPort: 54030,
  };
} else {
  hmrConfig = {
    protocol: "wss",
    host: host,
    port: parseInt(process.env.FRONTEND_PORT!) || 8002,
    clientPort: 443,
  };
}
```

### 3. Prefer IPv4 in Cloudflare RealtimeKit Proxy (`scripts/start-livekit-proxy.js`)

```javascript
// Force Node.js to prefer IPv4 to avoid IPv6 issues
process.env.NODE_OPTIONS = '--dns-result-order=ipv4first';

import { initCloudflareRealtimeKitProxy } from '../app/livekit-proxy.server.js';
```

## Benefits

1. **Stability**: The application now reliably binds to IPv4 addresses, avoiding IPv6-related issues
2. **Consistency**: Fixed ports ensure consistent behavior across restarts
3. **Compatibility**: Better compatibility with environments where IPv6 might be partially implemented or problematic

## Testing

To test this fix:
1. Restart the development server with `npm run dev`
2. Verify there are no IPv6 binding errors in the logs
3. Check that HMR (hot reloading) works properly
4. Verify the Cloudflare RealtimeKit proxy server starts on port 7880

These changes help ensure consistent behavior in development environments, especially on systems with complex network configurations or partial IPv6 support.