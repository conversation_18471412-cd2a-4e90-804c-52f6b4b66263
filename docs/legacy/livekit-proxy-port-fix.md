# LiveKit Proxy Port Consistency Fix

## Issue
The LiveKit proxy server was starting on a random port each time the application restarted, causing inconsistency between server restarts. This led to connection issues with the proxy server, which was affecting the voice assistant functionality.

## Solution
We implemented a fix to ensure that the LiveKit proxy server consistently uses the same port (7880) across restarts:

1. **Fixed Port Assignment**
   - Modified `app/livekit-proxy.server.js` to always use port 7880 instead of allowing a random port.
   - Added proper error handling for the case when the port is already in use.

2. **Improved Proxy Initialization**
   - Added a singleton pattern in `app/entry.server.tsx` to ensure the proxy is only started once.
   - Created `ensureLiveKitProxyStarted()` helper function to manage proxy initialization.
   - Made the application work smoothly even when the port is already in use by another instance.

3. **Environment Variable Consistency**
   - Set `LIVEKIT_PROXY_URL` environment variable to consistently point to the correct port.
   - This ensures all components can communicate with the proxy server.

4. **Enhanced Error Handling**
   - Added proper error handling for the case when the port is already in use.
   - When the port is in use, the application will now assume an existing proxy is running and continue.

5. **Health Check Endpoint**
   - Added a health check endpoint that reports the status of the LiveKit proxy and bot services.
   - This helps diagnose issues with the services.

## Key Changes

### 1. Fixed Port Assignment in `app/livekit-proxy.server.js`
```javascript
// Always use port 7880 for development consistency
const PORT = 7880;
logInfo(`Attempting to start server on fixed port ${PORT}...`);
```

### 2. Error Handling for Port Already in Use
```javascript
server.on('error', (error) => {
  // Check for the specific EADDRINUSE error (port already in use)
  if (error.code === 'EADDRINUSE') {
    logWarn(`Port ${PORT} is already in use - this may be another instance of the proxy.`);
    logWarn(`Will assume existing proxy is valid and continue.`);
    
    // Instead of rejecting, we'll resolve with a fake server instance
    resolve({
      server: null,
      port: PORT,
      isExistingInstance: true
    });
    return;
  }
  
  // For other errors, reject as usual
  logError(`Server failed to start:`, error);
  reject(error);
});
```

### 3. Singleton Pattern in `app/entry.server.tsx`
```typescript
// Variable to track LiveKit proxy initialization
let livekitProxyInitialized = false;
let livekitProxyPromise: Promise<any> | null = null;

/**
 * Initialize the LiveKit proxy server once, for development use
 * This ensures it uses the same port consistently
 */
async function ensureLiveKitProxyStarted() {
  // Use singleton pattern to avoid multiple initializations
  if (livekitProxyInitialized) {
    console.debug('[entry.server] LiveKit proxy already initialized');
    return livekitProxyPromise;
  }

  if (livekitProxyPromise) {
    console.debug('[entry.server] LiveKit proxy initialization in progress, reusing promise');
    return livekitProxyPromise;
  }
  
  // ... initialization logic ...
}
```

## Benefits
1. **Stability**: The application now has a stable proxy server port across restarts.
2. **Predictability**: Developers can always use the same port for testing.
3. **Resilience**: The application can continue even if another instance of the proxy is already running.
4. **Diagnostics**: Better logging and health checks make it easier to diagnose issues.

## Usage
The LiveKit proxy now consistently listens on port 7880. The application will automatically set the `LIVEKIT_PROXY_URL` environment variable to `http://localhost:7880`.

## Testing
To test this fix:
1. Restart the development server with `npm run dev`.
2. Verify that the LiveKit proxy starts on port 7880.
3. Restart the server again and verify that it either:
   - Uses the existing proxy server if one is running, or
   - Starts a new proxy server on port 7880 if none is running.
4. Check the health endpoint at `/health` to verify service status.