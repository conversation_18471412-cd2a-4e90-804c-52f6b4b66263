# LiveKit Client Loading Fix

## Issues

When attempting to use the voice assistant, users encountered JavaScript errors:

1. **Initial Error**:
```
TypeError: Room is not a constructor
    at VoiceAssistantIntegration.connectToLiveKit (voice-assistant-integration.js:188:26)
```

2. **Secondary Error**:
```
ReferenceError: ConnectionState is not defined
    at VoiceAssistantIntegration.publishLocalAudio (voice-assistant-integration.js:328:57)
```

This issue occurred because the LiveKit client library was not being loaded correctly or was being accessed before it was fully available.

## Root Cause Analysis

1. **Timing Issue**: The voice-assistant-integration.js script was trying to destructure the LiveKit classes at the top level, assuming they were already available in the global scope.

2. **Global Variable Inconsistency**: The client library was supposed to be available as `window.LivekitClient`, but the actual global variable name might have been different (`window.livekit` or `window.LiveKit`).

3. **Order of Operations**: The loader.js script was correctly loading the LiveKit client before the voice assistant integration, but the destructuring at the top level happened during module evaluation, not during method execution.

## Solution

We implemented a robust approach to accessing the LiveKit client library:

1. **Lazy Loading via Getter Functions**: Instead of destructuring at the top level, we created getter functions for each LiveKit class or enum:

```javascript
function getLiveKitClient() {
  // Multiple possible global names based on how the library is loaded
  const client = window.livekit || window.LivekitClient || window.LiveKit; 
  if (!client || !client.Room) {
    console.error('LiveKit client not found in window object. Check if livekit-client.js loaded properly.');
    throw new Error('LiveKit client library not loaded correctly');
  }
  return client;
}

// Expose these functions rather than trying to destructure at the top level
function getRoom() { return getLiveKitClient().Room; }
function getRoomEvent() { return getLiveKitClient().RoomEvent; }
// ... etc.
```

2. **Runtime Access**: We fetch the LiveKit classes at runtime when they're actually needed, rather than at module load time:

```javascript
try {
  console.log(`Getting LiveKit Room constructor...`);
  const Room = getRoom(); // Get Room constructor at runtime
  
  // Create Room instance
  console.log(`Creating Room instance...`);
  this.livekitRoom = new Room({
    // ...options
  });
  
  // ...etc.
} catch (error) {
  // ...error handling
}
```

3. **Enhanced Error Handling**: We added more detailed error messages to help diagnose loading issues:

```javascript
if (!client || !client.Room) {
  console.error('LiveKit client not found in window object. Check if livekit-client.js loaded properly.');
  throw new Error('LiveKit client library not loaded correctly');
}
```

## Additional Fixes

1. **Consistent Use of Getter Functions**: We ensured all references to LiveKit classes and enums use the getter functions:
   - Fixed `ConnectionState` references in `publishLocalAudio` and `connectToLiveKit`
   - Added proper error handling for track unpublishing and stopping
   - Used optional chaining (`?.`) to safely access methods that might not exist

2. **Improved Loader Script**: Enhanced the loader.js script to:
   - Check multiple possible global variable names
   - Create an alias for backward compatibility
   - Provide better diagnostic information
   - Ensure the client is properly exposed on the window object

## Benefits

1. **Resilience**: This approach ensures LiveKit classes are only accessed when needed and when they're actually available.

2. **Flexibility**: It works with multiple possible global variable names that the LiveKit library might use.

3. **Better Error Messages**: If something goes wrong, developers get clear error messages about what happened.

4. **Consistent Access**: All LiveKit class references now use the same getter pattern, eliminating potential inconsistencies.

5. **Robust Error Handling**: Added try-catch blocks around critical operations to prevent cascading failures.

## Testing

To test this fix:

1. Reload the Shopify storefront with the voice assistant.
2. Click the microphone button to start the assistant.
3. Verify that it correctly connects to LiveKit without any "Room is not a constructor" errors.
4. Check the browser console for the new detailed logging.

## Future Improvements

Consider implementing a proper module loading system to avoid relying on global variables. This could use:

1. ES Modules with proper imports
2. A more robust dynamic import mechanism
3. A dependency management approach that explicitly manages the loading order

However, for now, the getter functions approach provides the necessary robustness while working within the current architecture.