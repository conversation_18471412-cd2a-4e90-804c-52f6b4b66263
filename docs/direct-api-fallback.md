# Direct API Access with App Proxy Fallback

## Issue
During testing, we discovered that the Shopify App Proxy was failing to properly forward requests to our server for Cloudflare RealtimeKit token generation. This was causing a 500 error that prevented the voice assistant from connecting to Cloudflare RealtimeKit rooms.

Upon investigation, we found that requests to the app proxy path:
`https://feistyagency.myshopify.com/apps/voice/api/livekit/token`

Were being intercepted by Shopify's password protection page, since the store is in development/password mode. This meant that the app proxy wasn't even reaching our server.

## Solution
We implemented a robust approach that prioritizes direct API access while maintaining app proxy access as a fallback:

1. **Primary Method: Direct API Access**
   - Instead of routing through the Shopify App Proxy, the client now makes direct requests to the API server:
   - `https://aura.fy.studio/api/livekit/token`
   - This bypasses Shopify's infrastructure completely, avoiding any potential issues with app proxy configuration or store password protection.

2. **Fallback Method: App Proxy**
   - If direct API access fails (e.g., due to network errors or 5xx server responses), the client falls back to the original app proxy method:
   - `https://feistyagency.myshopify.com/apps/voice/api/livekit/token`
   - This ensures backward compatibility and resilience.

3. **Implementation Pattern**
   - Try-catch pattern to attempt direct access first, then fallback to app proxy
   - Clear logging to indicate which method is being used
   - Consistent error handling in both approaches

## Implementation Details

### Token Request Logic
```javascript
try {
  // First try direct access to the API server
  response = await fetch(directApiUrl, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
    },
  });
  
  // If direct access fails with a network error or 5xx, try app proxy as fallback
  if (!response.ok && response.status >= 500) {
    throw new Error(`Direct API access failed with status ${response.status}`);
  }
} catch (directApiError) {
  console.warn(`Direct API access failed: ${directApiError.message}. Trying app proxy fallback...`);
  
  // Fallback to app proxy
  usedAppProxy = true;
  const appProxyPath = '/apps/voice';
  const proxyEndpoint = `${appProxyPath}/api/livekit/token`;
  const proxyUrl = `${proxyEndpoint}?${queryParams.toString()}`;
  
  response = await fetch(proxyUrl, { ... });
}
```

## Benefits
1. **Reliability**: Direct API access provides a more reliable method for token generation.
2. **Performance**: Eliminates an extra hop through Shopify's infrastructure, potentially improving latency.
3. **Resilience**: Fallback mechanism ensures the system works even if one method fails.
4. **Development Friendly**: Works regardless of whether the store is password protected.

## Server-Side Considerations
Both approaches (direct API and app proxy) are supported by our server-side routes:
- Direct access: `/api/livekit/token`
- App proxy: `/proxy/api/livekit/token`

The server handles CORS properly to allow requests from the Shopify storefront domain.

## Testing
To test this change:
1. Verify that the voice assistant successfully connects to Cloudflare RealtimeKit via direct API access
2. Simulate a failure of direct API access to verify the fallback mechanism works
3. Check browser developer tools to see detailed logs about which method is being used

## Limitations
Direct API access requires appropriate CORS headers on the server, which we've already implemented. However, if CORS settings change in the future, this might need adjustments.

## Future Improvements
Once the store is out of development mode and the password protection is removed, the app proxy should work properly. At that point, we might reconsider the preferred approach, but maintaining both methods provides the best reliability.