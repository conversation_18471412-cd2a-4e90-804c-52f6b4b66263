# Voice Assistant Testing Instructions

This document provides detailed instructions for testing the voice assistant solution implemented with the Hybrid Architecture (Option C) using Cloudflare RealtimeKit, Replicate/Ultravox, and Play.ht.

## Prerequisites

1. Environment Variables
   - `LIVEKIT_URL`: URL of your Cloudflare RealtimeKit server (WSS)
   - `LIVEKIT_KEY`: Your Cloudflare RealtimeKit API key
   - `LIVEKIT_SECRET`: Your Cloudflare RealtimeKit API secret
   - `REPLICATE_API_TOKEN`: Your Replicate API token
   - `ULTRAVOX_MODEL_VERSION`: Ultravox model version identifier
   - `PLAYHT_USER_ID`: Your Play.ht User ID
   - `PLAYHT_SECRET_KEY`: Your Play.ht Secret Key

2. Running Services
   - Cloudflare RealtimeKit Server (available via Docker Compose or external)
   - Remix App with Bot Service components (integrated within the app)
   - Cloudflare Tunnel (optional, for local development)

## Setup Instructions

### 1. Environment Configuration

Copy the `.env.example` file to `.env` and fill in the required values:

```bash
cp .env.example .env
# Edit .env file with your credentials
```

### 2. Start Services Using Docker Compose

```bash
docker-compose up
```

This will start the Cloudflare RealtimeKit server. The Bot Service now runs as part of the Remix app.

### 3. Start Shopify App (Development)

In a separate terminal:

```bash
npm run dev
```

This command starts both the Remix app and initializes the Bot Service components.

## Testing Flow

### 1. Basic Voice Response Test

1. Install the extension on a test store
2. Navigate to a store page with the voice assistant
3. Click the voice assistant button to activate
4. Observe the browser console to verify:
   - Cloudflare RealtimeKit token is fetched successfully
   - Connection to Cloudflare RealtimeKit server is established
   - Local audio track is published successfully
5. Speak a simple question (e.g., "Hello, can you help me find a product?")
6. Verify that:
   - The audio is captured and sent to Cloudflare RealtimeKit (visualizer should show activity)
   - The Bot Participant Service joins the room (check server logs)
   - Ultravox processes the audio (check for Replicate API calls in logs)
   - Play.ht synthesizes a response (check for Play.ht API calls in logs)
   - The TTS audio is played back on the frontend (you should hear a response)

### 2. Connection and Error Handling Test

1. Test disconnection handling by temporarily disabling network
2. Verify the assistant attempts to reconnect when connection is restored
3. Test with different browsers to ensure cross-browser compatibility
4. Verify error messages are properly displayed to the user when:
   - Microphone permission is denied
   - Connection to Cloudflare RealtimeKit server fails
   - Bot Service fails to process audio

### 3. Audio Quality Testing

1. Test in noisy environments to verify noise suppression
2. Test with different microphones and devices
3. Verify TTS audio quality is acceptable
4. Check for any unusual latency in the response time

### 4. Tool Calling Test (When Implemented)

1. Ask questions that should trigger tool calls, such as:
   - "Show me red shirts"
   - "Find me shoes under $50"
   - "What's your bestselling product?"
2. Verify that:
   - The Bot Service correctly identifies tool calls
   - The Tool Execution Engine processes the calls 
   - Results are displayed appropriately in the UI
   - The assistant provides a verbal response about the results

### 5. End-to-End Flow Testing

Test the complete flow from voice capture through NLU processing, tool execution, and TTS response:

1. Open browser dev tools and server logs side by side
2. Activate the voice assistant
3. Ask a product-related question
4. Monitor and verify each step in the logs:
   - Frontend: Audio capture and publishing to Cloudflare RealtimeKit
   - Server: Bot joining room, receiving audio
   - Server: Sending audio to Replicate/Ultravox
   - Server: Receiving NLU results
   - Server: Executing any tool calls
   - Server: Sending text to Play.ht
   - Server: Receiving TTS audio
   - Server: Publishing TTS to Cloudflare RealtimeKit
   - Frontend: Receiving and playing TTS audio

## Troubleshooting

### Cloudflare RealtimeKit Connection Issues

- Check Cloudflare RealtimeKit server logs: `docker-compose logs livekit`
- Verify Cloudflare RealtimeKit URL and credentials in `.env`
- Ensure ports 7880, 7881, and 7882 are accessible
- Test Cloudflare RealtimeKit connectivity directly with a WebSocket client tool

### Bot Service Issues

- Check Remix server logs for Bot Service initialization and errors
- Verify Bot Service components are imported and initialized in `entry.server.tsx`
- Check TypeScript compilation errors for Bot Service files:
  - Issues in `tts.service.ts` related to `Lame` decoder compatibility
  - Issues in `livekit-audio.ts` related to `KrispNoiseFilter` and `LocalAudioTrack`
  - Run `tsc --noEmit` to identify specific type errors
- Verify all required environment variables are correctly set
- Check for any rate limiting or authentication errors with external APIs

### Audio Processing Issues

- Verify browser permissions for microphone
- Check for any console errors related to audio capture
- Verify audio is being published to Cloudflare RealtimeKit room (check frontend console)
- Monitor Replicate API calls and responses in server logs
- Check Play.ht API calls and responses in server logs

### TypeScript Compilation Errors

If encountering TypeScript errors when building or running the app:

1. For `tts.service.ts` Lame/Writable stream issues:
   - Check the imports and type definitions for the Lame decoder
   - Verify the piping between streams is properly typed
   - Consider using type assertions (as) to resolve compatibility issues

2. For `livekit-audio.ts` KrispNoiseFilter issues:
   - Ensure KrispNoiseFilter is properly imported/referenced as a type
   - Check that the correct properties and methods are being used on LocalAudioTrack

3. General TS fixes:
   - Run `npm install --save-dev @types/node` to ensure Node.js types are available
   - Check tsconfig.json for proper configuration

## Next Steps for Testing (Current MVP Priorities)

1. **Fix TypeScript Compilation Issues:**
   - Test compilation of Bot Service TS files
   - Verify fixes for Lame decoder type compatibility
   - Verify fixes for KrispNoiseFilter and LocalAudioTrack types

2. **Test Architecture Transition:**
   - Verify the old Cloudflare RealtimeKit proxy server is fully removed
   - Test that only the new Bot Service is handling audio processing
   - Confirm legacy SSE/POST code is removed from frontend

3. **Test Tool Execution Framework:**
   - Implement basic product search functionality
   - Test voice queries that trigger product searches
   - Verify UI updates correctly based on search results

4. **Performance Testing:**
   - Measure end-to-end latency from query to response
   - Test with various network conditions (high latency, packet loss)
   - Monitor API usage and costs with Replicate and Play.ht

5. **Production Readiness:**
   - Test with real Shopify stores across different themes
   - Collect user feedback on voice recognition accuracy and assistant utility
   - Verify the system scales properly with multiple concurrent users
   - Ensure proper error handling and recovery throughout the system 