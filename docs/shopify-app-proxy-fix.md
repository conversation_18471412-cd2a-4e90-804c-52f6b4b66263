# Shopify App Proxy Fix for Cloudflare RealtimeKit Token Generation

## Issue
The Cloudflare RealtimeKit token generation endpoint was not correctly handling requests through the Shopify app proxy. This caused 500 errors when trying to connect to Cloudflare RealtimeKit rooms via the Shopify storefront.

## Root Cause Analysis
1. **Path Mismatch**: The app was using flat route paths, but Shopify app proxy requests follow a nested structure.
2. **Request Routing**: The proxy handler was manually attempting to route requests rather than letting <PERSON>'s nested routing work naturally.
3. **Inconsistent Error Handling**: Error responses weren't consistent and lacked proper CORS headers.
4. **Debugging Challenges**: Lack of detailed request tracing made it difficult to diagnose issues in production.

## Solution
We implemented a comprehensive fix with the following components:

### 1. Proper Nested Route Structure
Created proper nested route files to match the exact path structure that the Shopify app proxy uses:
- `/app/routes/proxy.tsx` - Main proxy route handler
- `/app/routes/proxy/api/livekit/token.tsx` - Exact match for token requests
- `/app/routes/proxy/api/livekit/bot.tsx` - Exact match for bot requests

### 2. Enhanced Request Logging
Added detailed request tracing with unique request IDs to track requests across all handlers:
```typescript
const requestId = `req-${Date.now()}-${Math.floor(Math.random() * 10000)}`;
console.log(`=== [${requestId}] [proxy/api/livekit/token] Processing token request via EXACT file-based route match ===`);
```

### 3. Improved Error Handling
Implemented comprehensive error handling with standardized response formats and CORS headers:
```typescript
return json({ 
  error: "Missing shop parameter", 
  message: "The shop parameter is required to generate a token",
  requestId
}, { 
  status: 400,
  headers: CORS_HEADERS
});
```

### 4. Multiple Shop Domain Sources
Added fallback mechanisms to extract the shop domain from different sources:
- Query parameters (`?shop=example.myshopify.com`)
- Headers (`x-shopify-shop-domain`)
- Session data from authentication

### 5. Direct Token Generation
Each route handler now directly generates Cloudflare RealtimeKit tokens rather than forwarding requests:
```typescript
// Create the access token
const at = new AccessToken(livekitApiKey, livekitApiSecret, {
  identity: participantIdentity,
});

// Define permissions
at.addGrant({ 
  room: roomName,
  roomJoin: true,
  canPublish: true,
  canSubscribe: true,
  canPublishData: true,
});

// Generate the JWT
const token = await at.toJwt();
```

### 6. Environment Variable Handling
Added support for different environment variable naming conventions:
```typescript
const livekitHost = process.env.LIVEKIT_URL;
const livekitApiKey = process.env.LIVEKIT_API_KEY || process.env.LIVEKIT_KEY;
const livekitApiSecret = process.env.LIVEKIT_API_SECRET || process.env.LIVEKIT_SECRET;
```

### 7. Frontend Integration Updates
Updated frontend code in `voice-assistant-integration.js` with improved error handling and better logging:
```javascript
console.log(`[${fetchRequestId}] Requesting token from: ${url}`);
console.log(`[${fetchRequestId}] This path should be proxied by Shopify to: /proxy/api/livekit/token`);
```

## Deployment Instructions
1. Deploy all modified files to the production server:
   - `/app/routes/proxy.tsx`
   - `/app/routes/proxy.api.livekit.token.tsx` (legacy route)
   - `/app/routes/proxy/api/livekit/token.tsx` (new nested route)
   - `/app/routes/proxy/api/livekit/bot.tsx` (new nested route)
   - `/extensions/voice-assistant/assets/voice-assistant-integration.js`

2. Verify the server logs show the new request tracing format when token requests are made.

3. Test the app proxy connection from the storefront to ensure it successfully connects to Cloudflare RealtimeKit rooms.

## Remaining Tasks
1. Migrate any other app proxy endpoints to the nested route structure for consistency.
2. Consider adding automated tests for the app proxy routes.
3. Remove legacy flat routes once the nested routes are confirmed working in production.