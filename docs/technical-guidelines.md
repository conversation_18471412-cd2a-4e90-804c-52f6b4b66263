# Voice AI Shopping Assistant - Technical Guidelines (Gemini & Cloudflare Workers)

## Development Standards & Best Practices

This document outlines the technical standards, coding conventions, and best practices for developing and maintaining the Voice AI Shopping Assistant Shopify app, built on Gemini 2.5 Pro, Cloudflare RealtimeKit, and Cloudflare Workers with Next.js/Remix.

## Table of Contents

1. [Technology Stack](#technology-stack)
2. [Code Organization](#code-organization)
3. [Development Workflow](#development-workflow)
4. [Cloudflare Workers Development Guidelines](#cloudflare-workers-development-guidelines)
5. [Shopify-Specific Guidelines](#shopify-specific-guidelines)
6. [Theme App Extension Guidelines (Next.js/Remix)](#theme-app-extension-guidelines-nextjsremix)
7. [Voice Processing & AI (Gemini) Best Practices](#voice-processing--ai-gemini-best-practices)
8. [Cloudflare RealtimeKit Integration Guidelines](#cloudflare-realtimekit-integration-guidelines)
9. [Security Guidelines](#security-guidelines)
10. [Performance Optimization](#performance-optimization)
11. [Testing Standards](#testing-standards)
12. [Documentation Requirements](#documentation-requirements)

## Technology Stack

### Frontend
- **Framework**: Next.js (preferred, using OpenNext) or Remix
- **UI Framework**: Shopify Polaris components
- **State Management**: React Context API & Hooks, Zustand, or Jotai
- **Styling**: Tailwind CSS or CSS Modules
- **Package Manager**: npm or pnpm
- **Real-time Communication**: Cloudflare RealtimeKit Client SDK

### Backend
- **Platform**: Cloudflare Workers
- **Runtime**: Cloudflare Workers Runtime (V8 isolate)
- **Framework**: Next.js (via OpenNext) or Remix
- **Language**: TypeScript
- **Database/Storage**: Cloudflare Workers KV, Durable Objects, potentially Hyperdrive for relational DBs.
- **Real-time Communication**: Cloudflare RealtimeKit Server SDK (Node.js version, ensure Workers compatibility)
- **AI**: Google Gemini 2.5 Pro (via Vertex AI API)
- **Deployment**: Wrangler CLI

### AI/Voice Services
- **Multimodal AI**: Google Gemini 2.5 Pro (Vertex AI) for audio understanding, NLU, function calling.
- **Real-time Audio/Data**: Cloudflare RealtimeKit Server (Cloud or Self-hosted).

### DevOps
- **Version Control**: Git with GitHub
- **CI/CD**: GitHub Actions integrated with Wrangler CLI / Cloudflare Workers Builds
- **Hosting**: Cloudflare Workers
- **Monitoring**: Cloudflare Workers Observability (Logs, Metrics, Tracing), Sentry (optional)

## Code Organization

*(Adapt based on chosen framework - Next.js/Remix)*

### Directory Structure (Example for Next.js App Router)
```
/
├── app/                       # Next.js App Router
│   ├── api/                   # API Routes (e.g., Cloudflare RealtimeKit token)
│   │   └── realtimekit/
│   │       └── token/route.ts
│   ├── (app)/                 # Main application routes/UI
│   │   └── page.tsx           # Example page
│   ├── layout.tsx
│   └── globals.css
├── components/                # Shared React components
├── hooks/                     # Custom React hooks
├── lib/                       # Utility functions, SDK clients, Agent logic
│   ├── cloudflare-realtimekit/  # Cloudflare RealtimeKit client/server helpers
│   ├── gemini/              # Gemini API integration
│   ├── shopify/             # Shopify API helpers
│   └── agent/               # AI Agent core logic, context, tools
├── extensions/                # Shopify Extensions
│   └── voice-assistant/       # Theme App Extension
│       ├── app/               # Embedded Next.js app route/component
│       ├── shopify.extension.toml
│       └── ...
├── public/                    # Static assets
├── prisma/                    # Database schema (if using DB+Hyperdrive)
├── worker-configuration/      # Cloudflare Worker specific code (Durable Objects, entry)
│   └── durable-objects/       # Durable Object classes
│       └── sessionState.ts
├── wrangler.toml              # Cloudflare Worker configuration
├── tsconfig.json
├── next.config.mjs
├── package.json
└── docs/                      # Project documentation
```

### Naming Conventions
- **Files**: Use kebab-case (e.g., `livekit-token.ts`, `voice-widget.tsx`)
- **Components**: Use PascalCase (e.g., `VoiceAssistantUI.tsx`)
- **Functions/Variables**: Use camelCase (e.g., `processAudioInput`, `geminiClient`)
- **CSS Classes**: Use descriptive names (Tailwind utility classes or BEM with CSS Modules)
- **Worker Bindings**: Use UPPER_SNAKE_CASE (e.g., `APP_KV`, `GEMINI_API_KEY_SECRET`)

### Coding Style
- Follow ESLint and Prettier configurations.
- Prefer functional components with hooks.
- Use TypeScript strictly; avoid `any` where possible.
- Organize imports (e.g., using Prettier plugin).
- Include TSDoc comments for public APIs, complex functions, and types.

## Development Workflow

### Git Workflow
- Follow GitHub Flow (main branch + feature branches).
- Create feature branch from `main` (e.g., `feature/gemini-function-calling`).
- Commit frequently using Conventional Commits format.
- Submit Pull Requests (PRs) to `main`.
- Require code review and passing CI checks before merging.
- Squash and merge preferred.

### Branch Naming
- `feature/short-description`
- `fix/short-description`
- `refactor/area-or-component`
- `docs/area-updated`
- `chore/maintenance-task`

### Commit Message Format
- Follow Conventional Commits (e.g., `feat(agent): implement gemini function calling loop`).

### Pull Request Process
- Use PR templates.
- Ensure clear description of changes and motivation.
- Link to relevant issues.
- Ensure CI checks (linting, tests, build) pass.
- Request reviews.
- Address feedback before merging.

## Cloudflare Workers Development Guidelines

### Environment Variables & Secrets
- Use Worker secrets for all sensitive credentials (API keys, tokens).
- Use `wrangler secret put` to manage secrets.
- Use `.dev.vars` for local development (add to `.gitignore`).
- Access secrets/vars via the `env` object passed to the Worker handler/fetch function (`context.env` in Hono/itty-router, `env` in Pages Functions).

### State Management
- Prefer stateless functions where possible.
- Use **Workers KV** for simple key-value storage, configuration, and caching (eventually consistent).
- Use **Durable Objects** for strong consistency needs, coordinating state, managing WebSockets (though Cloudflare RealtimeKit handles this), or complex session state.
- Design Durable Objects carefully to avoid bottlenecks.

### Node.js Compatibility
- Enable `nodejs_compat` flag in `wrangler.toml` for broader Node.js API/package support.
- Be mindful that not all Node.js modules or APIs are perfectly replicated; test thoroughly.
- Prefer Web Standard APIs where available.

### Error Handling & Logging
- Implement robust error handling (`try...catch`) in Worker code.
- Utilize Cloudflare Worker's built-in logging (`console.log` appears in `wrangler tail` or dashboard).
- Consider structured logging for easier analysis.
- Integrate external monitoring (Sentry) if needed for advanced error tracking.

### Testing
- Use `vitest` or `jest` with mocking libraries (`miniflare` for local environment simulation) for unit/integration testing Worker logic.
- Test Durable Objects interactions.

## Shopify-Specific Guidelines

### API Usage
- Use official Shopify API libraries (`@shopify/shopify-api`, `@shopify/shopify-app-remix`, etc.), ensuring compatibility with the Worker environment.
- Prefer GraphQL APIs (Admin & Storefront) for efficiency.
- Handle Shopify API rate limits gracefully (implement backoff/retry mechanisms).
- Authenticate API calls correctly (session tokens for Admin API, storefront access token for Storefront API).

### App Installation & Authentication
- Follow Shopify standards for OAuth flow and session token management.
- Adapt session storage for the Worker environment (e.g., using KV or Durable Objects if needed, though standard cookie-based sessions might work with compatible frameworks).
- Ensure App Proxy signature verification if using App Proxy routes.

### Webhooks
- Handle webhook registrations and HMAC verification securely within the Worker backend.
- Process webhooks reliably (consider Cloudflare Queues for background processing if complex).

## Theme App Extension Guidelines (Next.js/Remix)

### Integration
- Use `shopify.theme.extension.toml` for configuration.
- Embed Next.js/Remix components correctly using Liquid snippets or App Blocks.
- Ensure assets (JS, CSS) are bundled and loaded efficiently.

### Performance
- Optimize component bundle sizes (code splitting, tree shaking).
- Use server components / server actions where appropriate (Next.js) or loaders/actions (Remix) for data fetching.
- Minimize client-side JavaScript execution.

### UI/UX
- Use Shopify Polaris for UI consistency within the embedded context where applicable.
- Ensure responsive design and accessibility (WCAG).
- Avoid interfering with the store's theme styles or functionality (use CSS scoping/namespacing).

## Voice Processing & AI (Gemini) Best Practices

### Audio Handling
- Request microphone permission clearly and only when needed.
- Use WebAudio API for capturing audio (e.g., 16kHz mono PCM).
- Implement end-of-utterance detection (VAD, silence detection) before sending audio to Gemini to avoid unnecessary API calls.
- Send audio data in a format supported by Gemini API (check latest documentation).

### Gemini API Interaction
- **Authentication**: Use service account keys stored securely as Worker secrets.
- **Model Selection**: Use the specified Gemini model (`gemini-2.5-flash-preview-03-25` or latest).
- **Prompt Engineering**: Design clear system prompts defining the assistant's role, capabilities, personality, and constraints (e.g., language handling, tool usage).
- **Context Management**: Leverage the large context window. Pass relevant conversation history (user/assistant turns). Implement strategies for summarizing or truncating very long histories if performance/cost becomes an issue. Consider using Durable Objects to store session context.
- **Function Calling**: Define tools using OpenAPI 3.0.3 schema. Provide clear descriptions for tools and parameters. Handle the function call/response loop correctly.
- **Multilingual/Code-Switching**: Include explicit instructions in the system prompt on how to handle English/Albanian and code-switching. Rely on Gemini's inherent capabilities but guide its behavior.
- **Error Handling**: Implement retries (with backoff) for transient API errors. Handle specific API error codes gracefully.
- **Latency**: Be aware that Gemini 2.5 Pro is not streaming. Structure the interaction flow to manage user expectations during the ~1-3 second processing time.

### Tool/Function Design
- Keep tool functions focused and specific.
- Design tool schemas with clear, unambiguous parameter names and descriptions.
- Ensure tool execution logic (calling Shopify APIs) is robust and handles errors.

## Cloudflare RealtimeKit Integration Guidelines

### Connection & Authentication
- Fetch Cloudflare RealtimeKit tokens securely from the backend Worker endpoint.
- Handle connection state changes gracefully on the frontend.
- Use the correct Cloudflare RealtimeKit SDKs (`livekit-client` frontend, `livekit-server-sdk` backend).

### Real-time Communication
- Publish user audio efficiently using appropriate track settings (e.g., Opus codec).
- Use the **Cloudflare RealtimeKit Data Channel** (`publishData`, `DataReceived` event) reliably for sending/receiving the assistant's text responses.
- Ensure data messages are properly encoded/decoded (e.g., JSON stringify/parse).

### Agent Logic (Backend Worker)
- Implement agent logic to join rooms, subscribe to participant tracks (audio), and publish data messages.
- Handle participant join/leave events if necessary.
- Manage Cloudflare RealtimeKit client instances within the Worker environment carefully (connection pooling or per-request instantiation might be needed depending on architecture).

## Security Guidelines

### Authentication & Authorization
- Secure Cloudflare RealtimeKit token generation endpoint.
- Validate Shopify sessions/App Proxy signatures.
- Authenticate all internal API calls between Worker services if applicable.

### Secret Management
- **NEVER** hardcode secrets.
- Use Cloudflare Worker secrets for all API keys (Shopify, Cloudflare RealtimeKit, Google Cloud) and other sensitive data.
- Rotate secrets periodically.

### Data Protection
- Do not store raw user audio unless explicitly required and compliant with privacy policies.
- Minimize storage of PII.
- Adhere to GDPR, CCPA.
- Secure data in transit (HTTPS, WSS).

### Input Validation
- Validate data received via API calls, function call arguments, and Cloudflare RealtimeKit data messages.
- Sanitize inputs where necessary.

## Performance Optimization

### Cloudflare Workers
- Optimize Worker startup time (minimize global scope initialization).
- Write efficient code (avoid blocking operations).
- Leverage Cloudflare caching where appropriate (requires careful consideration for dynamic apps).

### Frontend
- Optimize Next.js/Remix bundle sizes.
- Use performant React patterns.
- Minimize re-renders.
- Ensure efficient Cloudflare RealtimeKit connection handling.

### API Calls
- Batch Shopify API calls if possible.
- Optimize Gemini prompts (avoid unnecessary verbosity).
- Cache API responses where appropriate (e.g., using KV store).

## Testing Standards

### Unit Testing
- Test individual functions, components, and utilities.
- Use `vitest` or `jest`.
- Mock dependencies (APIs, SDKs).
- Aim for high code coverage for critical logic.

### Integration Testing
- Test interactions between components/modules within the Worker (e.g., Agent Logic <-> Tool Engine).
- Test API endpoint handlers.
- Use `miniflare` for local Worker environment simulation, including KV/DO bindings.

### End-to-End (E2E) Testing
- Simulate user interaction flow (voice input -> Cloudflare RealtimeKit -> Worker -> Gemini -> Function Call -> Shopify -> Data Channel -> UI display).
- Use tools like Playwright or Cypress (may require complex setup for Worker environment).
- Focus on critical user journeys (product search, core commands).
- Include tests for multilingual and code-switching scenarios.

## Documentation Requirements

- Maintain up-to-date documentation in the `/docs` directory.
- Use TSDoc for code comments.
- Update `README.md` with setup, architecture overview, and usage instructions.
- Document architecture decisions, especially around Workers, Gemini, and Cloudflare RealtimeKit.
- Document environment variables and secrets required. 