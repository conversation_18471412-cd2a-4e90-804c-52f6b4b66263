# Voice AI Shopping Assistant - System Architecture (Gemini 2.5 Pro, Cloudflare RealtimeKit & Workers)

## System Overview

The Voice AI Shopping Assistant enhances Shopify stores by integrating a voice-activated AI assistant. Built on a modern, scalable architecture, it leverages Google's Gemini 2.5 Pro model for direct audio understanding and function calling, Cloudflare RealtimeKit for real-time communication, and Cloudflare Workers with Next.js for edge deployment. This document outlines the key components and interactions.

## Updated Architecture (April 2025)

- **Agent Service (Cloudflare Worker, Node.js SDK, nodejs_compat):**
  - Joins RealtimeKit room as bot participant.
  - Subscribes to user audio tracks.
  - Buffers/processes audio, detects end-of-utterance.
  - Sends audio/context to Gemini API (function calling enabled).
  - Handles Gemini function call responses, triggers Tool Execution Engine for Shopify API calls.
  - Publishes text responses (and tool results) back to the room via RealtimeKit Data Channel.
  - Uses Durable Objects for session/context management.

- **Frontend:**
  - Uses RealtimeKit Client SDK for audio and Data Channel.
  - UI: Voice button, listening/processing indicators, text response area.

- **Tool Execution Engine:**
  - Receives function call requests from Gemini, executes Shopify API calls, returns results.

- **Data Flow:**
  1. User speaks → audio to RealtimeKit → Agent Service (Worker) → Gemini → (function call) → Shopify → text response via Data Channel → UI.

- **Security:**
  - All secrets in Worker env.
  - JWT tokens for RealtimeKit.
  - Robust error handling and logging.
  - Accessibility and privacy requirements enforced.

## Architecture Diagram

```mermaid
graph TD
    subgraph "User_Browser_Shopify_Storefront"
        FE_UI[Voice Assistant UI (Next.js Component)]
        FE_RTK_SDK[RealtimeKit Client SDK]
        FE_AudioCap[WebAudio Capture]
        FE_DataDisplay[Text Response Display]

        FE_UI -- "Activate" --> FE_AudioCap
        FE_AudioCap -- "Audio Stream" --> FE_RTK_SDK
        FE_RTK_SDK -- "Publishes User Audio Track" --> RTK_Service
        FE_RTK_SDK -- "Receives Data Message" --> FE_DataDisplay
        FE_DataDisplay -- "Display Text" --> FE_UI
        FE_RTK_SDK -- "Connection Mgmt" --> FE_UI
    end

    subgraph "Cloudflare_Workers_Platform"
        CF_Worker[Remix/Next.js App on Workers (OpenNext)]
        subgraph "CF_Worker"
            BE_Token[RealtimeKit Token Endpoint]
            BE_Agent[AI Agent Logic]
            BE_ToolExec[Tool Execution Engine]
        end
        KV_Store[(Workers KV Store)]
        DO_Store[(Durable Objects)]
        Hyperdrive[(Hyperdrive DB Connector)]

        FE_UI -- "Request Token" --> BE_Token
        BE_Token -- "Token/URL" --> FE_UI

        BE_Agent -- "Joins Room" --> RTK_Service
        RTK_Service -- "User Audio Track" --> BE_Agent
        BE_Agent -- "Audio Data + Context" --> Gemini_API
        Gemini_API -- "Text Response / Function Call" --> BE_Agent
        BE_Agent -- "If Function Call" --> BE_ToolExec
        BE_ToolExec -- "Executes" --> Shopify_API[Shopify Storefront/Admin API]
        Shopify_API -- "Data" --> BE_ToolExec
        BE_ToolExec -- "Function Result" --> BE_Agent
        BE_Agent -- "Function Result (optional)" --> Gemini_API
        Gemini_API -- "Final Text Response" --> BE_Agent
        BE_Agent -- "Publishes Data Message (Text)" --> RTK_Service
        RTK_Service -- "Forwards Data Message" --> FE_RTK_SDK

        BE_Agent -- "State/Config" --> KV_Store
        BE_Agent -- "Session State" --> DO_Store
        BE_ToolExec -- "DB Access (optional)" --> Hyperdrive
    end

    subgraph "External_Services"
        RTK_Service[Cloudflare RealtimeKit Service]
        Gemini_API[Google Gemini 2.5 Pro API (Vertex AI)]
    end

    style Gemini_API fill:#f9f,stroke:#333,stroke-width:2px
    style RTK_Service fill:#ccf,stroke:#333,stroke-width:2px
    style CF_Worker fill:#dff,stroke:#333,stroke-width:2px
    style KV_Store fill:#f80, stroke:#333, stroke-width:1px
    style DO_Store fill:#f80, stroke:#333, stroke-width:1px
    style Hyperdrive fill:#f80, stroke:#333, stroke-width:1px
```

**Diagram Description:**

1.  The Shopper interacts with the Voice UI (Next.js component) in the Theme App Extension.
2.  The frontend requests a RealtimeKit token from an endpoint within the **Cloudflare Worker** hosting the Remix/Next.js app.
3.  The frontend connects directly to the RealtimeKit Service using the RealtimeKit Client SDK and publishes the user's audio track.
4.  The **AI Agent Logic** (running within the Cloudflare Worker) joins the RealtimeKit room.
5.  The Agent subscribes to the user's audio track and sends the audio data directly to the **Google Gemini 2.5 Pro API**.
6.  Gemini processes the audio and returns either a final text response or a function call request.
7.  If a function call is requested, the Agent triggers the **Tool Execution Engine** (also within the Worker).
8.  The Tool Execution Engine calls the necessary **Shopify APIs**.
9.  The Agent sends the tool execution result back to Gemini for final processing.
10. Gemini returns the final **text response** to the Agent.
11. The Agent publishes the text response as a **Data Message** via the RealtimeKit Data Channel to the room.
12. The Frontend subscribes to the Data Channel, receives the text message, and displays it in the UI.
13. Cloudflare Workers KV, Durable Objects, and Hyperdrive can be used for caching, state management, and database connections respectively.

## Component Descriptions

### 1. Shopify Integration Components

#### 1.1 Shopify Admin Interface
- **App Settings & Configuration**: React-based interface (potentially Next.js/Remix) using Shopify App Bridge and Polaris.
- **Purpose**: Allows merchants to configure the voice assistant, customize appearance, and view analytics.
- **Technologies**: Next.js/Remix, React, Shopify App Bridge, Polaris UI components.

#### 1.2 Theme App Extension
- **Voice UI Components**: Frontend interface (Next.js component) embedded via Theme App Extension.
- **Assistant Logic**: Client-side JavaScript handles voice capture (WebAudio API), requests RealtimeKit tokens, connects to RealtimeKit Service, publishes audio tracks, subscribes to Data Channel for text responses, and manages UI interactions.
- **Purpose**: Embeds the voice assistant into the storefront and handles real-time communication via RealtimeKit.
- **Technologies**: Next.js, JavaScript, RealtimeKit Client SDK, WebAudio API, Fetch API.

### 2. Cloudflare Workers Platform

#### 2.1 Next.js/Remix App on Workers
- **Hosting**: The entire application backend (API endpoints, agent logic) runs on Cloudflare Workers, potentially using the OpenNext adapter for Next.js compatibility.
- **RealtimeKit Token Service**: An API endpoint within the Worker app that generates RealtimeKit access tokens (JWTs).
- **AI Agent Logic**: Core logic that joins RealtimeKit rooms, handles audio streams, interacts with the Gemini API, manages conversation context, processes function calls, and sends text responses via Data Channel.
- **Tool Execution Engine**: Handles the execution of functions requested by Gemini (e.g., calling Shopify Storefront/Admin APIs).
- **Technologies**: Node.js (via Workers runtime), Next.js/Remix, TypeScript, RealtimeKit SDK (for token generation & agent logic), Google AI SDK.

#### 2.2 Cloudflare Edge Storage & Services
- **Workers KV**: Used for storing configuration, caching non-sensitive data.
- **Durable Objects**: Used for managing stateful logic, like conversation sessions or participant state across Worker instances.
- **Hyperdrive**: Used for efficient connection pooling to traditional databases if needed by the Tool Execution Engine.

### 3. External Services

#### 3.1 Cloudflare RealtimeKit Service
- **Signaling & Media Transport**: Manages WebSocket connections and WebRTC media streams for real-time audio, video, and data messages.
- **Purpose**: Provides the core real-time communication infrastructure.
- **Technologies**: Cloudflare RealtimeKit, WebRTC, WebSockets.

#### 3.2 Google Gemini 2.5 Pro API (Vertex AI)
- **AI Model**: Processes audio input directly, understands intent, handles context, performs function calling, and generates text responses. Supports multilingual understanding and code-switching.
- **Purpose**: The core intelligence of the voice assistant.
- **Integration**: Called via REST API or Google AI SDK from the AI Agent Logic running on Cloudflare Workers.

## Data Flow

### Voice Query & Text Response Flow
1.  Shopper activates the voice assistant UI.
2.  Frontend requests a RealtimeKit access token from the Cloudflare Worker backend endpoint.
3.  Backend generates and returns the token and RealtimeKit service URL.
4.  Frontend uses RealtimeKit Client SDK to connect directly to the RealtimeKit Service.
5.  Frontend captures audio (WebAudio API) and publishes it as an audio track to the RealtimeKit room.
6.  The **AI Agent Logic** (running in Cloudflare Worker) joins the room and subscribes to the user's audio track.
7.  The Agent buffers audio and sends it to the **Google Gemini 2.5 Pro API** along with conversation context.
8.  Gemini processes the audio and returns a text response or a function call request to the Agent.
9.  *(If function call, see flow below)*. If direct text response:
10. The Agent publishes the **text response** as a **Data Message** to the RealtimeKit room.
11. Frontend receives the Data Message via its RealtimeKit connection and displays the text in the UI.

### Function Calling Flow
1.  Gemini API returns a structured function call request to the Agent Logic.
2.  Agent Logic parses the request and triggers the **Tool Execution Engine** within the Worker.
3.  Tool Execution Engine calls the appropriate **Shopify API** (e.g., Storefront GraphQL API for product search).
4.  Shopify API returns data to the Tool Execution Engine.
5.  Agent Logic sends the function execution result back to the **Gemini API** for final interpretation.
6.  Gemini API processes the result and returns the final **text response** to the Agent.
7.  Agent publishes the text response as a **Data Message** to the RealtimeKit room (as in step 10-11 above).
8.  Frontend displays the final text response and potentially updates UI based on tool results (e.g., showing product cards).

## Technical Considerations

### Security
- All API communications use HTTPS/TLS.
- RealtimeKit connections secured via JWT tokens generated by the backend Worker.
- Shopify API keys, Google Cloud credentials, and other secrets stored securely using Cloudflare Worker secrets/environment variables, never in client-side code or `wrangler.toml`.
- App Proxy usage for token endpoint requires signature verification.
- Adherence to GDPR/CCPA.

### Performance
- **Cloudflare Edge Deployment**: Minimizes latency by running backend logic close to the user.
- **Direct WebRTC**: Low-latency audio/video streaming via RealtimeKit.
- **RealtimeKit Data Channel**: Efficient delivery of text responses.
- **Gemini Latency**: Gemini 2.5 Pro is powerful but not streaming; expect 1-3s response time. Optimize prompts and consider Gemini Flash for specific use cases if needed.
- **Cloudflare Caching**: Leverage Workers KV and standard HTTP caching for static assets and API responses where applicable.

### Scalability
- **Cloudflare Workers**: Designed for high scalability and automatic scaling.
- **RealtimeKit Service**: Horizontally scalable, managed by Cloudflare.
- **Stateless Logic**: Design Worker logic to be largely stateless, using KV/Durable Objects for state management when necessary.
- **Rate Limiting**: Monitor and respect rate limits for Gemini API and Shopify APIs.

### Implementation Details

- **Cloudflare Worker Development**: Use Wrangler CLI for development and deployment. Set appropriate compatibility dates and flags (e.g., `nodejs_compat`).
- **Next.js/Remix on Workers**: Utilize adapters like OpenNext or native Cloudflare Pages functions. Manage environment variables correctly within the Worker context.
- **RealtimeKit Integration**: Use `realtimekit-client` SDK (frontend) and `realtimekit-server-sdk` (backend agent logic within Worker).
- **Gemini Integration**: Use Google AI SDK (Node.js version compatible with Workers) or direct REST calls. Handle authentication via Worker secrets.
- **Function Calling**: Define tool schemas compatible with Gemini's function calling specification (OpenAPI-like). Implement robust parsing and execution logic in the Tool Execution Engine.
- **Context Management**: Leverage Gemini's large context window (1M tokens). Implement strategies for managing conversation history (passing relevant turns, potential truncation/summarization for very long conversations) within the Agent logic, potentially using Durable Objects for session state.
- **Multilingual/Code-Switching**: Design prompts and agent logic to leverage Gemini's multilingual capabilities. Explicitly instruct the model on desired language behavior (e.g., respond in the user's language).

## Future Considerations
- Adding Text-to-Speech (TTS) output as an optional enhancement.
- Advanced analytics integration.
- Deeper integration with Cloudflare services (Queues, R2, etc.).

---

This architecture document reflects the shift to Gemini 2.5 Pro and a Cloudflare Workers-based deployment, prioritizing text output via RealtimeKit Data Channels. It will be updated as implementation progresses.