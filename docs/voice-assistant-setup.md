# Voice Assistant Integration Setup (Gemini 2.5 Pro, Cloudflare RealtimeKit & Workers)

This document explains how to set up and troubleshoot the Voice Assistant feature in the Shopify app, using Gemini 2.5 Pro, Cloudflare RealtimeKit for real-time communication, and deployed on Cloudflare Workers.

## Prerequisites

1.  **Cloudflare RealtimeKit Account:** Access to Cloudflare RealtimeKit (managed service, no self-hosting required).
2.  **Cloudflare Account:** Access to Cloudflare Workers, KV, Durable Objects (optional), and secrets management.
3.  **Google Cloud Project:** Access to Vertex AI with the Gemini 2.5 Pro API enabled.
4.  **Shopify Partner Account & Development Store.**
5.  **Node.js, npm, and Git installed.**
6.  **Wrangler CLI** installed and configured (`npm install -g wrangler`, `wrangler login`).

## Setup Instructions

### 1. Configure Environment Variables & Secrets (Cloudflare Workers)

Secrets should be managed via the Cloudflare dashboard or Wrangler CLI, **not** in `.env` files for production deployments. For local development with `wrangler dev`, you can use a `.dev.vars` file (which should **not** be committed to Git).

**Required Secrets/Variables:**

-   **Shopify Credentials:**
    -   `SHOPIFY_API_KEY`: Your Shopify App API Key.
    -   `SHOPIFY_API_SECRET`: Your Shopify App API Secret Key.
    -   `SHOPIFY_APP_URL`: The public URL of your deployed Worker application (e.g., `https://your-worker.workers.dev`).
    -   `SHOPIFY_SCOPES`: Required API scopes (e.g., `read_products`, `write_checkouts`).
    -   `SHOPIFY_STOREFRONT_ACCESS_TOKEN`: If using Storefront API for tools.
-   **Cloudflare RealtimeKit Configuration:**
    -   `REALTIMEKIT_URL`: Public WSS URL of your RealtimeKit instance (provided by Cloudflare).
    -   `REALTIMEKIT_API_KEY`: RealtimeKit API Key (from Cloudflare dashboard).
    -   `REALTIMEKIT_API_SECRET`: RealtimeKit API Secret (from Cloudflare dashboard).
-   **Google Cloud / Gemini Credentials:**
    -   `GOOGLE_APPLICATION_CREDENTIALS_JSON`: The content of your Google Cloud service account key JSON file (store this securely as a Worker secret).
    -   `GEMINI_MODEL_NAME`: The specific Gemini model identifier (e.g., `gemini-2.5-flash-preview-03-25`).
    -   `GOOGLE_CLOUD_PROJECT_ID`: Your Google Cloud Project ID.
-   **Database (Optional, if using Hyperdrive):**
    -   Connection string or relevant credentials stored as secrets.

**Setting Secrets with Wrangler:**

```bash
# Example for Google Cloud credentials (replace 'your_service_account_key.json' with your actual file)
wrangler secret put GOOGLE_APPLICATION_CREDENTIALS_JSON < your_service_account_key.json

wrangler secret put REALTIMEKIT_API_KEY
# Paste your key when prompted

wrangler secret put REALTIMEKIT_API_SECRET
# Paste your secret when prompted

# ... set other secrets similarly ...
```

**Local Development (`.dev.vars`):**

Create a `.dev.vars` file in the project root (and add it to `.gitignore`):

```.dev.vars
SHOPIFY_API_KEY="..."
SHOPIFY_API_SECRET="..."
# ... other non-secret vars ...

# For local testing, secrets might be loaded differently or mocked
# GOOGLE_APPLICATION_CREDENTIALS_JSON="...content of local key file..."
REALTIMEKIT_API_KEY="..."
REALTIMEKIT_API_SECRET="..."
# ...
```

Refer to Wrangler documentation for managing environments (staging, production).

### 2. Set up Google Cloud Project & Gemini API

1.  Ensure you have a Google Cloud project.
2.  Enable the **Vertex AI API** within the project.
3.  Create a **Service Account** with necessary permissions (e.g., Vertex AI User role).
4.  Download the **JSON key file** for the service account.
5.  Securely store the content of this JSON file as the `GOOGLE_APPLICATION_CREDENTIALS_JSON` secret in Cloudflare Workers.
6.  Note your `GOOGLE_CLOUD_PROJECT_ID`.

### 3. Configure Cloudflare Worker Project (`wrangler.toml`)

Set up your `wrangler.toml` file. Key configurations include:

```toml
name = "shopify-voice-assistant"
main = "path/to/your/worker/entry.js" # Adjust based on your build output (e.g., from OpenNext)
compatibility_date = "YYYY-MM-DD" # Use a recent date

# Enable Node.js compatibility
compatibility_flags = [ "nodejs_compat" ]

# Define bindings for secrets and services
kv_namespaces = [
  { binding = "APP_KV", id = "your_kv_namespace_id" } # Optional KV binding
]

durable_objects.bindings = [
  { name = "SESSION_STATE", class_name = "SessionStateDO" } # Optional DO binding
]

# Environment variables (use secrets for sensitive data)
[vars]
  GEMINI_MODEL_NAME = "gemini-2.5-flash-preview-03-25"
  REALTIMEKIT_URL = "wss://your-realtimekit-url.cloudflare.com"
  GOOGLE_CLOUD_PROJECT_ID = "your-gcp-project-id"

# Secrets are accessed via the environment in the Worker code
# Example: context.env.REALTIMEKIT_API_SECRET

# Build configuration (if using OpenNext or similar)
[build]
  command = "npm run build"
  # ... other build settings ...
```

Consult the OpenNext documentation for specific `wrangler.toml` configuration when deploying Next.js apps.

### 4. Deploy the Cloudflare Worker

Build your application and deploy it to Cloudflare Workers:

```bash
# Install dependencies
npm install

# Build the application (adjust command if needed)
npm run build

# Deploy the worker
wrangler deploy
```

Make sure your `SHOPIFY_APP_URL` environment variable points to the deployed Worker URL.

### 5. Install and Configure the Shopify App

1.  Use the Shopify CLI to register/deploy your app configuration.
2.  Install the app on your development store.
3.  Ensure the Theme App Extension is enabled in the theme editor.

## How It Works (Gemini & Text Output)

1.  The frontend voice assistant (Next.js component in Theme App Extension) activates.
2.  Frontend JS requests a RealtimeKit token from an API endpoint running on the Cloudflare Worker (e.g., `/api/realtimekit/token`). Authentication might use App Proxy signature or session data.
3.  The Worker endpoint reads RealtimeKit secrets from the environment and generates a JWT using RealtimeKit's API.
4.  The Worker returns the `REALTIMEKIT_URL` and the token to the frontend.
5.  Frontend uses the RealtimeKit SDK to connect *directly* to the RealtimeKit service.
6.  Frontend captures audio (WebAudio API) and publishes it as an audio track.
7.  The **AI Agent Logic** (part of the Worker backend) joins the same RealtimeKit room.
8.  The Agent subscribes to the user audio track, buffers it, and potentially detects end-of-utterance.
9.  The Agent prepares the audio data and conversation context (potentially using Durable Objects for state) and calls the **Google Gemini 2.5 Pro API** (Vertex AI) via the Google AI SDK, authenticating using the service account key stored as a Worker secret.
10. Gemini processes the audio and context, potentially using defined function schemas, and returns a text response or a function call request.
11. **If Function Call:** The Agent triggers the **Tool Execution Engine** (within the Worker) which calls Shopify APIs (authenticating as needed) and sends results back to Gemini for a final text response.
12. The Agent takes the final text response and publishes it as a **Data Message** using RealtimeKit's data messaging API to the room.
13. The frontend, listening via the RealtimeKit SDK, receives the text message and displays it in the UI.

**Note:** Real-time audio and data flow directly via RealtimeKit; the Worker handles backend logic, AI interaction, and tool execution.

## Troubleshooting

### Connection Issues
-   **RealtimeKit Connection Failed (Frontend Console):** Check RealtimeKit service status, `REALTIMEKIT_URL` correctness, token validity (check Worker logs for generation errors), firewall issues.
-   **Token Endpoint Error (5xx on Frontend, Worker Logs):** Check Worker logs for errors in the token generation route. Verify `REALTIMEKIT_API_KEY`, `REALTIMEKIT_API_SECRET` are correctly set as secrets and accessible in the Worker environment (`context.env`). Check authentication logic (App Proxy signature verification, session handling).

### AI / Gemini Issues
-   **Gemini API Errors (Worker Logs):**
    -   Check authentication: Ensure `GOOGLE_APPLICATION_CREDENTIALS_JSON` secret is correctly set and the service account has Vertex AI permissions.
    -   Check `GOOGLE_CLOUD_PROJECT_ID` and `GEMINI_MODEL_NAME`.
    -   Verify audio data format sent to the API.
    -   Check prompts and function schemas for correctness.
    -   Monitor Google Cloud logs for Vertex AI API errors.
    -   Check for rate limiting issues.
-   **Incorrect Responses / Function Calls:** Review prompts, context management, and tool schemas passed to Gemini. Adjust temperature or other model parameters.

### Worker Issues
-   **Deployment Errors (Wrangler Output):** Check `wrangler.toml` configuration, build command success, bundle size limits.
-   **Runtime Errors (Worker Logs):** Check Cloudflare dashboard logs. Ensure Node.js compatibility flags are set if using Node APIs. Verify environment variables and secrets are accessed correctly (`context.env`).
-   **Tool Execution Failures (Worker Logs):** Check Shopify API credentials, API call logic, and error handling within the Tool Execution Engine.

### Frontend Issues
-   **No Text Response Displayed:** Verify frontend is subscribing to RealtimeKit data messages, check console for errors parsing the message, ensure the Agent is actually publishing data messages (check Worker logs).
-   **UI Not Updating:** Check React state management, ensure data messages trigger UI updates correctly.
-   **Microphone Access Denied:** Ensure HTTPS, check browser permissions.

### General Tips
-   Use `wrangler dev` for local testing and debugging.
-   Add extensive logging throughout the Worker code (token generation, agent logic, Gemini calls, tool execution).
-   Use browser developer tools heavily for frontend debugging (console, network tabs).
-   Test incrementally: Verify token generation, then RealtimeKit connection, then basic audio flow, then Gemini integration, then function calling.