# Voice Assistant Implementation Plan for Shopify (Gemini 2.5 Pro & Cloudflare Workers)

## Overview
This plan details the implementation of the Voice AI Shopping Assistant Shopify app, leveraging Gemini 2.5 Pro for AI, Cloudflare RealtimeKit for real-time communication, and deployed on Cloudflare Workers using Next.js (via OpenNext). The focus is on direct audio understanding, function calling for Shopify actions, and text-based responses delivered via Cloudflare RealtimeKit Data Channels.

## 1. Architecture & Core Components

### 1.1. Core Components
- **Shopify App Frontend**: Next.js component embedded via Theme App Extension; handles voice UI, Cloudflare RealtimeKit connection, audio publishing, and displays text responses.
- **Shopify App Backend**: Next.js/Remix app running on Cloudflare Workers; handles API endpoints (Cloudflare RealtimeKit token), AI Agent Logic, and Tool Execution.
- **AI Agent Logic**: Runs within the Worker; manages Cloudflare RealtimeKit connection, processes audio with Gemini, handles function calls, maintains context, and sends text responses.
- **Tool Execution Engine**: Runs within the Worker; executes Shopify API calls based on Gemini's function requests.
- **Cloudflare RealtimeKit Server**: Handles real-time audio and data transport (WebRTC).
- **AI Model**: Google Gemini 2.5 Pro (Vertex AI) for multimodal understanding and function calling.
- **Cloudflare Services**: Workers (runtime), KV (caching/config), Durable Objects (state), Hyperdrive (DB access).
- **Tool Framework**: System for defining and executing Shopify actions (product search, cart management) requested by Gemini.

### 1.2. Data Flow (Gemini & Text Output)
1. Shopper activates the voice assistant button (Next.js component).
2. Frontend requests a Cloudflare RealtimeKit token from the Cloudflare Worker backend.
3. Frontend connects directly to Cloudflare RealtimeKit Server using the token and Cloudflare RealtimeKit Client SDK.
4. Frontend captures audio (WebAudio API) and publishes it as an audio track to the Cloudflare RealtimeKit room.
5. **AI Agent Logic** (in Worker) joins the room and subscribes to the user's audio track.
6. Agent buffers/processes audio and sends it to **Google Gemini 2.5 Pro API** (Vertex AI) along with context.
7. Gemini processes audio and returns either a **text response** or a **function call request** to the Agent.
8. **If Function Call:**
    a. Agent triggers the **Tool Execution Engine** (in Worker).
    b. Tool Engine calls the appropriate **Shopify API**.
    c. Agent sends the tool result back to Gemini for final processing.
    d. Gemini returns the final **text response** to the Agent.
9. **If Text Response (direct or after function call):**
    a. Agent publishes the **text response** as a **Data Message** via the Cloudflare RealtimeKit Data Channel.
10. Frontend (subscribed to Data Channel) receives the text message and displays it in the UI.
11. Frontend updates UI based on tool execution results if necessary (e.g., showing product cards).

## 2. Server-Side Implementation (Cloudflare Workers)

### 2.1. Worker Setup & Next.js/Remix Integration `[ ]`
- **Platform**: Cloudflare Workers.
- **Framework**: Next.js (using OpenNext adapter) or Remix.
- **Tasks**:
  - `[ ]` Set up Cloudflare account and Wrangler CLI.
  - `[ ]` Configure `wrangler.toml` for the project, including compatibility dates/flags (`nodejs_compat`).
  - `[ ]` Integrate OpenNext adapter for deploying the Next.js app to Workers.
  - `[ ]` Set up environment variables and secrets management within Cloudflare Workers (for API keys, etc.).
  - `[ ]` Configure routing for API endpoints and potentially the main application.

- **File**: (e.g., `/app/routes/api/realtimekit/token.ts` or similar within Next.js/Remix structure)
- **Purpose**: Securely generates Cloudflare RealtimeKit JWT tokens for frontend clients, running as an API route within the Worker.
- **Tasks**:
  - `[ ]` Create API route/loader function.
  - `[ ]` Implement authentication (e.g., verify Shopify session or App Proxy signature).
  - `[ ]` Read Cloudflare RealtimeKit credentials (`REALTIMEKIT_URL`, `REALTIMEKIT_KEY`, `REALTIMEKIT_SECRET`) from Worker environment secrets.
  - `[ ]` Use `livekit-server-sdk` (Node.js version) to generate `AccessToken`.
  - `[ ]` Define room name and participant identity strategies.
  - `[ ]` Set appropriate token permissions (`canPublish: true`, `canSubscribe: true`, `canPublishData: true`).
  - `[ ]` Return `{ realtimekitUrl: process.env.REALTIMEKIT_URL, token: jwt }` as JSON.
  - `[ ]` Implement CORS headers if accessed directly (not via App Proxy).

### 2.3. Agent Service (Cloudflare RealtimeKit Server SDK, nodejs_compat) `[ ]` (HIGH PRIORITY)
- **Location**: Worker backend logic (e.g., `/lib/agent/agentService.ts`)
- **Purpose**: Joins Cloudflare RealtimeKit room as bot, subscribes to user audio, buffers/processes audio, sends to Gemini, handles function calls, publishes text responses via Data Channel.
- **Tasks**:
  - `[ ]` Instantiate Cloudflare RealtimeKit Server SDK in Worker (ensure `nodejs_compat` is enabled).
  - `[ ]` Join room as bot participant.
  - `[ ]` Subscribe to user audio tracks.
  - `[ ]` Buffer/process audio, detect end-of-utterance.
  - `[ ]` Send audio/context to Gemini API (function calling enabled).
  - `[ ]` Handle Gemini function call responses, trigger Tool Execution Engine.
  - `[ ]` Publish text responses (and tool results) via Data Channel.
  - `[ ]` Use Durable Objects for session/context management.

### 2.4. Tool Execution Engine `[ ]` (HIGH PRIORITY)
- **Location**: Worker backend logic (e.g., `/lib/agent/toolEngine.ts`)
- **Purpose**: Executes Shopify API calls based on Gemini function requests.
- **Tasks**:
  - `[ ]` Implement functions for each tool schema (e.g., `searchProducts`, `addToCart`).
  - `[ ]` Integrate with Shopify APIs.
  - `[ ]` Handle authentication and error handling.
  - `[ ]` Return results to Agent Service for Gemini response.

### 2.5. Durable Objects for Session/Context `[ ]` (HIGH PRIORITY)
- **Purpose**: Manage conversation/session state for multi-turn and long conversations.
- **Tasks**:
  - `[ ]` Implement Durable Object class (e.g., `SessionStateDO`).
  - `[ ]` Store/retrieve conversation history, context, and state.
  - `[ ]` Integrate with Agent Service.

### 2.6. E2E and Integration Testing `[ ]` (HIGH PRIORITY)
- **Tasks**:
  - `[ ]` E2E test: User speaks → audio to Cloudflare RealtimeKit → agent → Gemini → function call → Shopify → text response via Data Channel → UI.
  - `[ ]` Unit/integration tests for agent logic, tool execution, and context management.

## 3. Client-Side Implementation (Next.js Theme App Extension)

### 3.1. Frontend: Cloudflare RealtimeKit Connection & Audio/Data Handling `[ ]`
- **File**: (e.g., `/extensions/voice-assistant/components/VoiceAssistant.tsx`, `/extensions/voice-assistant/hooks/useCloudflareRealtimeKit.ts`)
- **Tasks**:
  - `[ ]` Implement function to fetch Cloudflare RealtimeKit token from the backend Worker API endpoint.
  - `[ ]` Use `livekit-client` SDK to connect to Cloudflare RealtimeKit server (`room.connect`).
  - `[ ]` Handle Cloudflare RealtimeKit connection states (`RoomEvent.ConnectionStateChanged`) for UI feedback.
  - `[ ]` Implement high-quality audio capture (`createLocalAudioTrack`).
  - `[ ]` Publish local audio track to Cloudflare RealtimeKit room (`room.localParticipant.publishTrack`).
  - `[ ]` **Subscribe to Cloudflare RealtimeKit Data Channel** (`RoomEvent.DataReceived`).
  - `[ ]` **Handle incoming text messages** from the Data Channel.
  - `[ ]` Implement Cloudflare RealtimeKit disconnection logic (`room.disconnect`).
  - `[ ]` Provide audio level data (from local track) for UI visualization.
  - `[ ]` Remove any obsolete SSE/POST connection logic.

### 3.2. Frontend: Voice Assistant Interface Updates `[ ]`
- **File**: (e.g., `/extensions/voice-assistant/components/VoiceAssistantUI.tsx`)
- **Tasks**:
  - `[ ]` Update UI state management (e.g., React state/context) to reflect Cloudflare RealtimeKit connection states and assistant status (listening, thinking, responding).
  - `[ ]` **Implement display area for text responses** received via the Data Channel.
  - `[ ]` Ensure responsive visualization works with audio levels from the local Cloudflare RealtimeKit track.
  - `[ ]` Handle errors reported via Cloudflare RealtimeKit connection state or Data Channel messages.
  - `[ ]` Update UI dynamically based on tool execution results (e.g., display product cards if data is sent via Data Channel).
  - `[ ]` Ensure proper cleanup of Cloudflare RealtimeKit listeners on component unmount.
  - `[ ]` Remove any client-side TTS logic.

### 3.3. Frontend: App Block Configuration `[ ]`
- **File**: `/extensions/voice-assistant/blocks/voice-assistant.liquid` (or equivalent structure for Next.js TAE)
- **Tasks**:
  - `[ ]` Ensure Next.js component scripts (`livekit-client`, component bundles) are loaded correctly within the Theme App Extension.
  - `[ ]` Verify HTML structure supports the updated UI states and text display area.
  - `[ ]` Update Liquid variables or settings if needed.

## 4. AI Model Integration (Gemini 2.5 Pro)

### 4.1. Gemini API Integration `[ ]`
- **Interaction Point**: AI Agent Logic (in Worker)
- **Tasks**:
  - `[ ]` Set up Google Cloud project and enable Vertex AI API.
  - `[ ]` Create service account credentials and store securely in Worker secrets.
  - `[ ]` Use Google AI SDK (Node.js) or REST API to interact with `gemini-2.5-flash-preview-03-25` (or latest stable version).
  - `[ ]` Implement audio data preparation (ensure correct format/encoding for Gemini API).
  - `[ ]` Implement robust request/response handling, including parsing function call requests and text content.
  - `[ ]` Manage conversation history (passing relevant turns in prompts).
  - `[ ]` Configure model parameters (temperature, safety settings).
  - `[ ]` Handle API errors and rate limits.

### 4.2. Environment Configuration `[ ]`
- **File**: Cloudflare Worker environment variables/secrets configuration.
- **Tasks**:
  - `[ ]` Add Google Cloud credentials (e.g., `GOOGLE_APPLICATION_CREDENTIALS_JSON`).
  - `[ ]` Add Gemini model name (`GEMINI_MODEL_NAME`).
  - `[ ]` Add Cloudflare RealtimeKit Server connection details (`REALTIMEKIT_URL`, `REALTIMEKIT_KEY`, `REALTIMEKIT_SECRET`).
  - `[ ]` Add Shopify API credentials if needed by backend tools (`SHOPIFY_API_KEY`, `SHOPIFY_API_SECRET`, `SHOPIFY_STOREFRONT_ACCESS_TOKEN`).
  - `[ ]` Ensure secure storage and access for all secrets.
  - `[ ]` Update documentation (`README.md`, `voice-assistant-setup.md`) for required environment variables.
  - `[ ]` Remove obsolete variables (Replicate, Play.ht, etc.).

## 5. Function Calling Framework

### 5.1. Define Tool Schema (OpenAPI compatible) `[ ]`
- **Location**: Defined within AI Agent Logic / Shared constants.
- **Tasks**:
  - `[ ]` Define JSON schema (following OpenAPI 3.0.3 subset) for each Shopify tool (e.g., `searchProducts`, `addToCart`, `checkInventory`).
  - `[ ]` Include clear descriptions for tools and parameters.
  - `[ ]` Ensure schemas are passed correctly to the Gemini API.

### 5.2. Implement Tool Execution Engine `[ ]`
- **Location**: Integrated within Worker backend logic.
- **Tasks**:
  - `[ ]` Implement functions in Worker backend matching the tool schemas.
  - `[ ]` Integrate with Shopify Storefront/Admin APIs.
  - `[ ]` Handle authentication and authorization for Shopify API calls.
  - `[ ]` Format results for Gemini or direct use.

## 6. MVP Features to Implement (Gemini & Text Output)

### 6.1. Cloudflare Worker Setup & Cloudflare RealtimeKit Connection `[ ]` (HIGHEST PRIORITY)
- **Components**: Worker environment, Token Endpoint, Frontend Cloudflare RealtimeKit client.
- **Tasks**:
  - `[ ]` Set up basic Next.js/Remix app deployable to Workers.
  - `[ ]` Implement Cloudflare RealtimeKit token generation endpoint within the Worker.
  - `[ ]` Implement frontend logic to fetch token and connect to Cloudflare RealtimeKit server.
  - `[ ]` Handle connection states and basic audio publishing.

### 6.2. AI Agent Logic - Basic Flow `[ ]` (HIGH PRIORITY)
- **Components**: AI Agent Logic (in Worker).
- **Tasks**:
  - `[ ]` Implement Agent joining Cloudflare RealtimeKit room and subscribing to audio.
  - `[ ]` Implement basic audio buffering and end-of-utterance detection.
  - `[ ]` Integrate basic Gemini API call (send audio, get text response).
  - `[ ]` Publish text response via Cloudflare RealtimeKit Data Channel.

### 6.3. Frontend Data Channel Handling `[ ]` (HIGH PRIORITY)
- **Components**: Frontend UI.
- **Tasks**:
  - `[ ]` Implement frontend logic to subscribe to Data Channel.
  - `[ ]` Display received text messages in the UI.

### 6.4. Basic Function Calling (e.g., Product Search) `[ ]`
- **Components**: AI Agent Logic, Tool Execution Engine, Gemini API.
- **Tasks**:
  - `[ ]` Define schema for `searchProducts` tool.
  - `[ ]` Update Agent logic to handle function call requests/responses from Gemini.
  - `[ ]` Implement `searchProducts` function in Tool Execution Engine using Shopify Storefront API.
  - `[ ]` Test the full function calling loop (Audio -> Gemini -> Tool Call -> Shopify API -> Gemini -> Text Response -> Data Channel -> UI).

### 6.5. Context Management & Multilingual Prompting `[ ]`
- **Components**: AI Agent Logic.
- **Tasks**:
  - `[ ]` Implement basic conversation history tracking within the Agent.
  - `[ ]` Pass history context to Gemini API calls.
  - `[ ]` Add system prompts instructing Gemini on its role and multilingual/code-switching behavior.

### 6.6. Remove Obsolete Code `[ ]`
- **Components**: Entire codebase.
- **Tasks**:
  - `[ ]` Remove all code related to Replicate, Ultravox, Play.ht.
  - `[ ]` Remove old SSE/POST logic if any remains.
  - `[ ]` Clean up unused environment variables and configurations.

## 7. Testing & Deployment (Gemini & Cloudflare Workers)

### 7.1. Local & Staging Testing
- **Details**: Use Wrangler for local Worker development. Test against a staging Cloudflare RealtimeKit server and Shopify development store.
- **Tasks**:
  - `[ ]` Test Worker deployment and routing (`wrangler dev`).
  - `[ ]` Test Cloudflare RealtimeKit connection and audio/data transport.
  - `[ ]` Verify Gemini API integration (authentication, audio processing, function calls).
  - `[ ]` Test function call execution via Tool Engine and Shopify API interaction.
  - `[ ]` Verify text responses are correctly sent/received via Data Channel and displayed.
  - `[ ]` Test multilingual input and code-switching scenarios.
  - `[ ]` Measure end-to-end latency (User speaks -> Agent processes -> Gemini -> Agent sends -> Frontend displays).
  - `[ ]` Test context retention across multiple turns.
  - `[ ]` Test error handling (API failures, connection issues).

### 7.2. Shopify App Configuration
- **File**: `shopify.app.toml`
- **Tasks**:
  - `[ ]` Ensure Theme App Extension points and API scopes are correct.
  - `[ ]` Configure App Proxy if used for the token endpoint, ensuring signature verification.

### 7.3. Production Deployment
- **Details**: Deploy the Worker application using `wrangler deploy`.
- **Tasks**:
  - `[ ]` Configure production environment variables/secrets in Cloudflare dashboard.
  - `[ ]` Set up production Cloudflare RealtimeKit server instance.
  - `[ ]` Connect Cloudflare Workers Builds for CI/CD from Git repository.
  - `[ ]` Implement monitoring and logging using Cloudflare Worker analytics and potentially external services.
  - `[ ]` Perform load testing if necessary.

## 8. Current Status and Next Steps (Gemini Architecture)

### 8.1. Completed Tasks (Conceptual)
- ✅ Architectural Design for Gemini, Text Output, and Cloudflare Workers.
- ✅ Initial Documentation Update (`system-architecture.md`).

### 8.2. Next Immediate Steps
1. `[ ]` Finalize rewrite of `implementation-plan.md` (this document).
2. `[ ]` Update `voice-assistant-setup.md`, `technical-guidelines.md`, `product-requirements-document.md`, and `README.md`.
3. `[ ]` Set up Cloudflare Worker project structure with Next.js/OpenNext.
4. `[ ]` Implement Cloudflare RealtimeKit Token endpoint in Worker.
5. `[ ]` Implement basic Frontend Cloudflare RealtimeKit connection and audio publishing.
6. `[ ]` Begin implementing AI Agent Logic (Cloudflare RealtimeKit connection, basic audio handling).

## 9. Data Flow Summary (Gemini & Text Output)

1.  **Activation & Token:** User activates UI -> Frontend requests token from Worker API -> Worker returns Cloudflare RealtimeKit URL & Token.
2.  **Cloudflare RealtimeKit Connection:** Frontend connects to Cloudflare RealtimeKit Server.
3.  **Audio Publishing:** Frontend captures audio -> Publishes audio track.
4.  **Agent Subscription:** AI Agent Logic (Worker) joins room -> Subscribes to user audio track.
5.  **Gemini Processing:** Agent sends audio + context to Gemini API -> Receives text or function call.
6.  **Function Execution (if needed):** Agent triggers Tool Engine (Worker) -> Calls Shopify API -> Agent sends result to Gemini -> Gemini returns final text.
7.  **Text Response Delivery:** Agent publishes final text response via Cloudflare RealtimeKit Data Channel.
8.  **Frontend Display:** Frontend receives Data Message -> Displays text in UI.

## 10. Build Log
*(Previous build log entries removed as they pertain to the old architecture)*

### YYYY-MM-DD (Begin Gemini/Cloudflare Migration)
- Rewriting documentation (`system-architecture.md`, `implementation-plan.md`, etc.) to reflect new stack.
- Planning initial Worker setup and Cloudflare RealtimeKit integration steps.
