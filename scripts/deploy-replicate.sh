#!/bin/bash

# <PERSON>ript to deploy the Ultravox model to Replicate

echo "Deploying Ultravox model to Replicate..."

# Check if cog is installed
if ! command -v cog &> /dev/null; then
    echo "Error: cog is not installed. Installing now..."
    curl -o /usr/local/bin/cog -L https://github.com/replicate/cog/releases/latest/download/cog_$(uname -s)_$(uname -m)
    chmod +x /usr/local/bin/cog
fi

# Check if the ultravox-backend directory exists
if [ ! -d "ultravox-backend" ]; then
    echo "Error: ultravox-backend directory not found."
    exit 1
fi

# Prompt for Replicate username
read -p "Enter your Replicate username: " REPLICATE_USERNAME

# Login to Replicate
echo "Logging in to Replicate..."
cog login

# Navigate to the ultravox-backend directory
cd ultravox-backend

# Validate cog.yaml exists
if [ ! -f "cog.yaml" ]; then
    echo "Error: cog.yaml not found in ultravox-backend directory."
    exit 1
fi

# Validate predict.py exists
if [ ! -f "predict.py" ]; then
    echo "Error: predict.py not found in ultravox-backend directory."
    exit 1
fi

# Ensure cog.yaml is properly configured with image and no invalid fields
cat > cog.yaml << EOF
build:
 gpu: true
 python_version: "3.9"
 system_packages:
 - "libsndfile1"
 - "ffmpeg"
 python_packages:
 - "transformers==4.40.0"
 - "torch==2.1.2"
 - "librosa==0.10.2"
 - "soundfile==0.12.1"
 - "accelerate==0.27.2"
 - "bitsandbytes==0.41.3"
predict: "predict.py:Predictor"
image: "r8.im/$REPLICATE_USERNAME/ultravox-shopify"
EOF

# Try pushing to Replicate
echo "Attempting to push model to Replicate..."
if ! cog push; then
    echo "Initial deployment failed. Attempting CPU-only deployment..."
    
    # Update cog.yaml to disable GPU
    cat > cog.yaml << EOF
build:
 gpu: false
 python_version: "3.9"
 system_packages:
 - "libsndfile1"
 - "ffmpeg"
 python_packages:
 - "transformers==4.40.0"
 - "torch==2.1.2"
 - "librosa==0.10.2"
 - "soundfile==0.12.1"
 - "accelerate==0.27.2"
 - "bitsandbytes==0.41.3"
predict: "predict.py:Predictor"
image: "r8.im/$REPLICATE_USERNAME/ultravox-shopify"
EOF
    
    # Try again with CPU mode
    if ! cog push; then
        echo "Deployment failed in both GPU and CPU modes."
        echo "Check the error messages above for more details."
        exit 1
    fi
fi

# Get and store the model version - extract from output
MODEL_VERSION=$(cog push --just-print | grep -o 'r8.im/[^:]*')

# Set MODEL_ID to username/model-name
MODEL_ID="$REPLICATE_USERNAME/ultravox-shopify"

echo ""
echo "=========================================================="
echo "Model deployed successfully!"
echo "=========================================================="
echo "Model ID: $MODEL_ID"
echo "Model Version: $MODEL_VERSION"
echo ""
echo "Add these to your .env file:"
echo "ULTRAVOX_MODEL_VERSION=$MODEL_ID:$MODEL_VERSION"
echo "REPLICATE_API_TOKEN=<your_replicate_api_token>"
echo ""
echo "IMPORTANT: Make sure to use the full model version ID with hash"
echo "Follow the format: username/model:version-hash"
echo ""
echo "Then update app/bot/nlu.service.ts to use this model."
echo "=========================================================="