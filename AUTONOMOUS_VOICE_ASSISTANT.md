# Autonomous Voice Assistant with Tool Calling

## Overview

The Aura voice assistant has been enhanced with autonomous tool calling capabilities, enabling it to perform browser automation and web research without user intervention. When users speak to the assistant, it can now:

1. **Directly answer questions** using its built-in knowledge
2. **Execute browser automation** (clicking buttons, filling forms, navigating pages) via the Magician worker
3. **Perform web research** (fetching current information, prices, reviews) via the Research worker

## Architecture

### Backend (Cloudflare Worker)

**Enhanced Gemini Client** (`backend-worker/src/gemini.ts`)
- Added function calling support with tool definitions
- Supports `browserAutomation` and `webResearch` functions
- Enhanced prompt engineering for tool selection

**NLU Service** (`backend-worker/src/nlu.service.ts`)
- Tool call processing and routing
- Integration with Magician and Research workers
- Page context handling for automation

**Main Worker** (`backend-worker/src/index.ts`)
- New `/nlu-with-context` endpoint for enhanced requests
- JSON payload support with audio + page context
- Backward compatibility with existing `/nlu` endpoint

### Frontend (Shopify Extension)

**Simplified Integration** (`voice-assistant-integration.js`)
- Replaced LiveKit with direct HTTP calls
- DOM context capture (interactive elements + screenshot)
- Tool execution engine for automation steps
- Built-in TTS for responses

**Enhanced Main Controller** (`voice-assistant.js`)
- Event-driven architecture for tool execution
- UI feedback for tool actions
- Error handling and status updates

## Tool Calling Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Backend
    participant Gemini
    participant Tools
    
    User->>Frontend: Speaks command
    Frontend->>Frontend: Capture audio + DOM context
    Frontend->>Backend: POST /nlu-with-context
    Backend->>Gemini: Process audio with function definitions
    Gemini->>Backend: Function call + text response
    Backend->>Tools: Call Magician/Research worker
    Tools->>Backend: Return steps/content
    Backend->>Frontend: Combined response
    Frontend->>Frontend: Execute automation steps
    Frontend->>User: Speak result + perform actions
```

## Configuration

### Environment Variables

Add to `backend-worker/wrangler.toml`:

```toml
# Tool calling worker URLs
MAGICIAN_URL = "https://ai-agent-magician.fy.studio"
RESEARCH_URL = "https://ai-agent-research.fy.studio"
```

### Gemini Function Definitions

The system defines two main functions for Gemini:

```javascript
{
  name: "browserAutomation",
  description: "Execute browser automation tasks like clicking buttons, filling forms, or navigating pages",
  parameters: {
    type: "object",
    properties: {
      goal: {
        type: "string",
        description: "The specific goal to achieve (e.g. 'Add the first product to cart', 'Open product page')"
      }
    },
    required: ["goal"]
  }
},
{
  name: "webResearch", 
  description: "Search the web or lookup information from external sources",
  parameters: {
    type: "object",
    properties: {
      query: {
        type: "string",
        description: "The search query or information to lookup"
      }
    },
    required: ["query"]
  }
}
```

## Usage Examples

### Browser Automation

**User:** "Add this product to my cart"

1. Gemini calls `browserAutomation` function with goal: "Add product to cart"
2. Backend sends page context to Magician worker
3. Magician returns automation steps (e.g., click button with ID 5)
4. Frontend executes steps on the DOM
5. User sees the action happen automatically

### Web Research

**User:** "What are today's best deals?"

1. Gemini calls `webResearch` function with query: "today's best deals"
2. Backend sends query to Research worker
3. Research worker fetches current information
4. Backend returns research content
5. Frontend speaks the results to the user

### Direct Answers

**User:** "What's your return policy?"

1. Gemini provides direct answer without tool calls
2. Frontend speaks the response immediately

## Page Context Capture

The frontend automatically captures:

**Interactive Elements:**
- Buttons, links, inputs, selects
- Element properties: tag, type, text, placeholder, value, href, className, id
- Assigned `data-localid` attributes for reference

**Page Screenshot (Optional):**
- Currently simplified for MVP
- Can be enhanced with html2canvas for visual context

## Tool Execution

### Browser Automation Steps

Automation steps from Magician are executed sequentially:

```javascript
{
  action: "click",
  elementId: 5,
  value: ""
}
```

Actions supported:
- `click`: Triggers click event on element
- `type`: Sets value and dispatches input/change events
- `select`: Sets dropdown value and dispatches change event

### Research Content Display

Research results are displayed and spoken to the user as assistant responses.

## Error Handling

- **Tool failures:** Gracefully handled with user feedback
- **Missing elements:** Logged warnings, execution continues
- **Network errors:** Fallback messages and retry logic
- **Audio capture issues:** Permission prompts and error messages

## Testing

Run the test script to validate the implementation:

```bash
node test-tool-calling.js
```

The test validates:
- Backend endpoint functionality
- Tool call processing
- Frontend integration readiness
- Configuration validation

## Deployment

1. **Backend Worker:**
   ```bash
   cd backend-worker
   npm run build
   wrangler deploy
   ```

2. **Frontend Assets:**
   - Updated files are automatically served by Shopify
   - No additional deployment needed for extensions

## Security Considerations

- **DOM Access:** Only interacts with explicitly captured elements
- **Tool Validation:** All tool calls go through backend validation
- **CORS:** Proper headers for cross-origin requests
- **Input Sanitization:** Audio data is validated and processed safely

## Performance

- **Single Request Cycle:** All tool processing happens in one HTTP request
- **Optimized Audio:** 16kHz mono for efficient processing
- **Minimal DOM Scanning:** Only captures interactive elements
- **Cached Tool Responses:** Results included in initial response

## Limitations & Future Enhancements

**Current Limitations:**
- Screenshot capture simplified for MVP
- No multi-turn tool conversations
- Limited to predefined tool set

**Future Enhancements:**
- Real-time streaming with tool calls
- Multi-step tool workflows
- Visual element recognition
- Custom tool definitions per store

## Troubleshooting

### Common Issues

1. **Tool calls not working:**
   - Check MAGICIAN_URL and RESEARCH_URL in wrangler.toml
   - Verify workers are deployed and accessible
   - Check browser console for errors

2. **Audio capture failing:**
   - Ensure HTTPS context
   - Check microphone permissions
   - Verify audio constraints support

3. **DOM automation not working:**
   - Check element `data-localid` attributes
   - Verify element visibility and interactability
   - Check console for element selection errors

### Debug Logging

Enable verbose logging by checking browser console for:
- `[VA-Integration]` - Integration layer logs
- `[NLU-Context]` - Backend processing logs
- `[Gemini]` - Function call detection logs

## API Reference

### Backend Endpoints

#### POST /nlu-with-context

Enhanced NLU endpoint with tool calling support.

**Request:**
```json
{
  "audio": "base64_encoded_audio",
  "elements": [
    {
      "localId": 0,
      "tag": "button",
      "text": "Add to Cart",
      "className": "btn btn-primary"
    }
  ],
  "screenshot": "base64_screenshot_optional",
  "sampleRate": 16000,
  "channels": 1
}
```

**Response:**
```json
{
  "text": "Adding that to your cart now",
  "toolCalls": [
    {
      "tool": "browserAutomation",
      "steps": [
        {
          "action": "click",
          "elementId": 0,
          "value": ""
        }
      ]
    }
  ],
  "error": null
}
```

### Frontend Events

#### voice-assistant-message
Dispatched when the assistant has a message to display/speak.

```javascript
document.addEventListener('voice-assistant-message', (event) => {
  console.log(event.detail.message, event.detail.type);
});
```

#### voice-assistant-error
Dispatched when an error occurs during processing.

```javascript
document.addEventListener('voice-assistant-error', (event) => {
  console.error(event.detail.message);
});
```

---

## Summary

The autonomous voice assistant transforms the user experience by:

- **Eliminating friction:** Users can speak commands and see immediate results
- **Reducing cognitive load:** No need to navigate complex interfaces
- **Providing intelligence:** Combines automation with research capabilities
- **Maintaining simplicity:** One voice command handles complex workflows

This implementation provides a solid foundation for autonomous e-commerce assistance while maintaining performance, security, and scalability.