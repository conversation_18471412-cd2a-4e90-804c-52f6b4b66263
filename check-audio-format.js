const fs = require('fs');

// Read and decode the audio
const audioFileContent = fs.readFileSync('sample-request.json', 'utf8');
const audioData = JSON.parse(audioFileContent);
const audioBuffer = Buffer.from(audioData.audio, 'base64');

console.log('Audio buffer size:', audioBuffer.length);
console.log('First 20 bytes (hex):', Array.from(audioBuffer.slice(0, 20)).map(b => b.toString(16).padStart(2, '0')).join(' '));
console.log('First 20 bytes (ASCII):', audioBuffer.slice(0, 20).toString('ascii'));

// Check for common audio file signatures
const firstBytes = audioBuffer.slice(0, 12);
if (firstBytes[0] === 0x1A && firstBytes[1] === 0x45 && firstBytes[2] === 0xDF && firstBytes[3] === 0xA3) {
  console.log('Format: WebM/Matroska');
} else if (firstBytes[0] === 0x4F && firstBytes[1] === 0x67 && firstBytes[2] === 0x67 && firstBytes[3] === 0x53) {
  console.log('Format: OGG');
} else if (firstBytes[0] === 0x52 && firstBytes[1] === 0x49 && firstBytes[2] === 0x46 && firstBytes[3] === 0x46) {
  console.log('Format: WAV');
} else if (firstBytes[0] === 0xFF && (firstBytes[1] & 0xE0) === 0xE0) {
  console.log('Format: MP3');
} else {
  console.log('Format: Unknown');
} 