#!/bin/bash
set -e

echo "Setting up Aura monorepo development environment..."

# Update system packages
sudo apt-get update

# Install Node.js 18 (required by the project)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install pnpm using npm (more reliable in this environment)
sudo npm install -g pnpm@latest

# Add pnpm to PATH permanently
echo 'export PATH="$PATH:/usr/local/bin"' >> $HOME/.profile

# Verify versions
echo "Node.js version: $(node --version)"
echo "npm version: $(npm --version)"
echo "pnpm version: $(pnpm --version)"

# Navigate to workspace
cd /mnt/persist/workspace

# Install dependencies using pnpm
echo "Installing dependencies with pnpm..."
pnpm install

# Build the projects to verify setup
echo "Building all workspaces..."
pnpm build

# Verify the build outputs exist
echo "Verifying build outputs..."
if [ -d "aura/build" ]; then
    echo "✅ Frontend build successful - aura/build directory exists"
else
    echo "❌ Frontend build failed - aura/build directory not found"
    exit 1
fi

if [ -d "backend-worker/dist" ]; then
    echo "✅ Backend build successful - backend-worker/dist directory exists"
else
    echo "❌ Backend build failed - backend-worker/dist directory not found"
    exit 1
fi

echo "Development environment setup complete!"
echo "✅ All dependencies installed"
echo "✅ All workspaces built successfully"
echo "✅ Environment ready for development"