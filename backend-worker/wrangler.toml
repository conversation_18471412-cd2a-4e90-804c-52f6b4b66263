name = "aura-backend-worker"
main = "./dist/index.js"
compatibility_date = "2024-05-01"

[vars]
GEMINI_API_KEY = "AIzaSyCFzbQ7M4R0S7nssbku0hu4pxF7ybO48qg"
GOOGLE_AI_STUDIO_API_KEY = "AIzaSyCFzbQ7M4R0S7nssbku0hu4pxF7ybO48qg"
CARTESIA_API_KEY = "sk_car_7XpSJxeKpX2LSvdj5hXeWL"
# CF_ACCOUNT_ID and CF_GATEWAY_ID are required for Gemini API calls
# SHOPIFY_API_KEY, SHOPIFY_API_SECRET, SHOPIFY_STORE_DOMAIN for Shopify integration
# REALTIMEKIT_API_KEY for RealtimeKit integration
# AI_GATEWAY_URL = "https://gateway.ai.cloudflare.com/v1/d39391642ca519e39cdea4721edd0d6e/aura-shopify/google-ai-studio"
AI_GATEWAY_TOKEN = "NVnSDZ76hHoVGHEXpNlOqBBlKognikaiOelYsaYY" # if required
NEO4J_URL = "https://daeeb0eb.databases.neo4j.io/db/neo4j/query/v2"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "3Od_04Ezu1gf-b0-sSQ7yAyKkAgT7e8fAS9wWDLg1wQ"
SHOPIFY_STORE_DOMAIN = "feistyagency.myshopify.com"
SHOPIFY_MCP_ENDPOINT = "https://feistyagency.myshopify.com/api/mcp"
MAGICIAN_URL = "https://automation-worker.feisty-agency.workers.dev"
RESEARCH_URL = "https://ai-researcher-backend.feisty-agency.workers.dev"
AUTORAG_INSTANCE_NAME = "aura-store-rag"
GRAPH_MEMORY_URL = "https://aura-backend-worker.feisty-agency.workers.dev"

# Service bindings for worker-to-worker communication (recommended approach)
[[services]]
binding = "AUTOMATION_WORKER"
service = "automation-worker"

[[services]]
binding = "RESEARCH_WORKER"
service = "ai-researcher-backend"

# Durable Object bindings
[[durable_objects.bindings]]
name = "SESSION_DO"
class_name = "SessionDO"
script_name = "aura-backend-worker"

[[durable_objects.bindings]]
name = "GRAPH_MEMORY_DO"
class_name = "GraphMemoryDO"
script_name = "aura-backend-worker"

# R2 bucket for knowledge base documents
[[r2_buckets]]
binding = "KNOWLEDGE_BUCKET"
bucket_name = "aura-knowledge-base"

# AI binding for AutoRAG and Workers AI
[ai]
binding = "AI"

# D1 database
[[d1_databases]]
binding = "AURA_DB"
database_name = "aura-db"
database_id = "28e17f2b-3a5e-438c-955c-d190d41df1b3"

# Migrations
[[migrations]]
tag = "v1"
new_classes = ["SessionDO"]

[[migrations]]
tag = "v2"
new_classes = ["GraphMemoryDO"]

[[durable_objects.bindings]]
name = "GraphMemoryDO"
class_name = "GraphMemoryDO"

