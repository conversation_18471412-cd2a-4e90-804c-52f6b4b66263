# Backend Worker (Gemini NLU)

This Cloudflare Worker provides a secure, edge-native backend for audio-to-text NLU using Gemini 2.5 Pro. It is designed to be deployed and tested independently from the Shopify frontend.

## Features
- Accepts audio (WAV/PCM) via HTTP POST
- Calls Gemini API for transcription and understanding
- Returns structured JSON output
- No legacy Ultravox/Replicate code
- Edge-compatible, secure, and fast

## API
### POST `/nlu`
- **Body:** Binary audio (WAV or PCM)
- **Query Params:**
  - `sampleRate` (default: 16000)
  - `channels` (default: 1)
- **Response:**
  - `200 OK` with JSON `{ text, toolCalls, error? }`

#### Example
```sh
curl -X POST "https://<your-backend-worker-url>/nlu?sampleRate=16000&channels=1" \
  --header "Content-Type: audio/wav" \
  --data-binary "@test-audio.wav"
```

## Deployment
1. Install dependencies:
   ```sh
   npm install
   ```
2. Set your Gemini API key:
   ```sh
   wrangler secret put GEMINI_API_KEY
   ```
3. Deploy:
   ```sh
   npm run deploy
   ```

## Local Development
```sh
npm run dev
```

## Testing
- Use the `curl` example above with a sample WAV file.
- Confirm you receive a valid JSON response from Gemini.

## Next Steps
- Integrate with the Shopify frontend after backend validation.
- Add authentication, rate limiting, and monitoring as needed. 