// TODO: replace with actual D1Database type when available
type D1Database = any;
export interface StatsSummary {
  crPercent: number;
  voiceRevenue: number;
  events: any[];
}

export async function getStatsSummary(db: D1Database, store: string): Promise<StatsSummary> {
  // Query conversion and revenue for today
  const summarySql = `
    SELECT
      SUM(CASE WHEN event = 'purchase' THEN 1 ELSE 0 END) as purchases,
      SUM(CASE WHEN event = 'visit' THEN 1 ELSE 0 END) as visits,
      SUM(CASE WHEN event = 'purchase' AND channel = 'voice' THEN revenue ELSE 0 END) as voiceRevenue
    FROM voice_events
    WHERE store = ? AND date(timestamp, 'unixepoch') = date('now', 'localtime')
  `;
  const summaryRes = await db.prepare(summarySql).bind(store).first();

  const eventsSql = `
    SELECT event, revenue, timestamp, channel
    FROM voice_events
    WHERE store = ?
    ORDER BY timestamp DESC
    LIMIT 20
  `;
  const eventsRes = await db.prepare(eventsSql).bind(store).all();

  const purchases = summaryRes?.purchases ?? 0;
  const visits = summaryRes?.visits ?? 0;
  const voiceRevenue = summaryRes?.voiceRevenue ?? 0;
  const crPercent = visits > 0 ? (purchases / visits) * 100 : 0;

  return { crPercent, voiceRevenue, events: eventsRes.results || [] };
}
