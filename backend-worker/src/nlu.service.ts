// Gemini NLU Service for Cloudflare Worker
// Handles audio-to-text using Gemini 2.5 Pro API Client (delegates to gemini.ts)
// Enhanced with autonomous tool calling for Magician and Research workers

import { GeminiClient, GeminiAudioResult } from './gemini';
import { searchProducts, updateCart } from './shopify-tools';

export interface NluResult {
  text: string;
  toolCalls: any[];
  error?: string;
}

export interface NluContext {
  participantIdentity: string;
  roomName: string;
  elements?: any[];
  screenshot?: string;
}

export interface PageContext {
  elements: any[];
  screenshot?: string;
}

export class NluService {
  private gemini: GeminiClient;
  private env: any;

  constructor(geminiApiKey: string, env: any) {
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY is required');
    }
    this.gemini = new GeminiClient(geminiApiKey);
    this.env = env;
  }

  async processAudio(
    audioBuffer: Uint8Array,
    sampleRate: number,
    channels: number,
    context: NluContext,
    mimeType: string = 'audio/wav',
    history: Array<{ role: string; content: string }> = []
  ): Promise<NluResult> {
    try {
      let processedAudioBuffer = audioBuffer;
      let finalMimeType = mimeType;

      // If the incoming data is FLAC, convert it to WAV for OpenAI-compatible endpoint
      if (mimeType === 'audio/flac') {
        console.log('[NLU] Converting FLAC to WAV for OpenAI-compatible endpoint...');
        
        try {
          // For now, we'll decode FLAC to PCM and re-encode as WAV
          // Since we don't have a FLAC decoder in the worker, we'll use a simpler approach:
          // Assume the FLAC contains standard PCM data and encode it as WAV
          
          // This is a simplified conversion - in practice, you'd want to use a proper FLAC decoder
          // For now, we'll create a WAV from the raw audio assuming it's already PCM-like
          const wavBuffer = this.encodeWav(audioBuffer, sampleRate, channels);
          processedAudioBuffer = wavBuffer;
          finalMimeType = 'audio/wav';
          
          console.log('[NLU] ✅ FLAC converted to WAV:', {
            originalSize: audioBuffer.length,
            convertedSize: wavBuffer.length,
            sampleRate,
            channels
          });
        } catch (conversionError) {
          console.error('[NLU] FLAC conversion failed:', conversionError);
          // Fall back to treating as raw PCM
          const wavBuffer = this.encodeWav(audioBuffer, sampleRate, channels);
          processedAudioBuffer = wavBuffer;
          finalMimeType = 'audio/wav';
        }
      }
      
      // If the incoming data is raw PCM, encode it as WAV
      else if (
        mimeType === 'audio/pcm' ||
        mimeType === 'application/octet-stream'
      ) {
        console.log('[NLU] Converting PCM to WAV for Gemini...');
        const wavBuffer = this.encodeWav(processedAudioBuffer, sampleRate, channels);
        processedAudioBuffer = wavBuffer;
        finalMimeType = 'audio/wav';
      }

      // Prepare page context for Gemini if available
      const pageContext = context.elements || context.screenshot ? {
        elements: context.elements,
        screenshot: context.screenshot
      } : undefined;

      // -------- First attempt --------
      const geminiResult: GeminiAudioResult = await this.gemini.audioToText(
        processedAudioBuffer,
        finalMimeType,
        this.env,
        pageContext,
        history
      );

      // Detect common Gemini fallback phrases
      const failed = !geminiResult.toolCalls?.length && /i (?:am )?(?:sorry|an ai).*cannot|don\'t have access|cannot process audio/i.test(geminiResult.text || '');

      let result = geminiResult;

      if (failed && this.env.AI) {
        console.log('[NLU] Gemini transcription low-confidence – attempting noise suppression via Workers AI');
        try {
          const denoised = await this.denoiseAudio(processedAudioBuffer, sampleRate);
          if (denoised) {
            result = await this.gemini.audioToText(
              denoised,
              'audio/wav',
              this.env,
              pageContext
            );
            // Preserve any error info from 2nd attempt
          }
        } catch (denoiseErr) {
          console.warn('[NLU] Noise suppression retry failed:', denoiseErr);
        }
      }

      let toolCalls = result.toolCalls || [];
      let responseText = result.text;

      // Process any tool calls
      if (toolCalls.length > 0) {
        console.log('[NLU] Processing tool calls:', toolCalls);
        const processedCalls = await this.processToolCalls(toolCalls, context);
        toolCalls = processedCalls;
      }

      return { 
        text: responseText, 
        toolCalls, 
        error: result.error 
      };
    } catch (error: any) {
      return { text: '', toolCalls: [], error: error.message || String(error) };
    }
  }

  /**
   * Process tool calls from Gemini and execute them via appropriate workers
   */
  private async processToolCalls(toolCalls: any[], context: NluContext): Promise<any[]> {
    const processedCalls: any[] = [];

    for (const call of toolCalls) {
      try {
        console.log('[NLU] Processing tool call:', call.tool, call.args);
        
        switch (call.tool) {
          case 'browserAutomation':
            const automationResult = await this.callMagician(
              call.args.goal, 
              context.elements, 
              context.screenshot
            );
            processedCalls.push({
              tool: 'browserAutomation',
              steps: automationResult.steps || [],
              error: automationResult.error
            });
            break;

          case 'webResearch':
            const researchResult = await this.callResearch(call.args.query);
            processedCalls.push({
              tool: 'webResearch',
              query: call.args.query,
              content: researchResult.content || '',
              error: researchResult.error
            });
            break;

          case 'searchProducts': {
            const searchResult = await searchProducts(
              { query: call.args.query, limit: call.args.limit },
              this.env
            );
            processedCalls.push({
              tool: 'searchProducts',
              products: searchResult.products || [],
              error: searchResult.error,
              debug: searchResult.debug
            });
            break;
          }

          case 'updateCart': {
            const cartResult = await updateCart(
              {
                cartId: call.args.cartId,
                productId: call.args.productId,
                quantity: call.args.quantity
              },
              this.env
            );
            processedCalls.push({
              tool: 'updateCart',
              cart: cartResult,
              error: cartResult.error
            });
            break;
          }

          default:
            console.warn('[NLU] Unknown tool:', call.tool);
            processedCalls.push({
              tool: call.tool,
              error: `Unknown tool: ${call.tool}`
            });
        }
      } catch (error: any) {
        console.error('[NLU] Error processing tool call:', error);
        processedCalls.push({
          tool: call.tool,
          error: error.message || String(error)
        });
      }
    }

    return processedCalls;
  }

  /**
   * Call the Magician worker for browser automation using Service Bindings
   */
  private async callMagician(goal: string, elements?: any[], screenshot?: string | any): Promise<any> {
    try {
      if (!elements || elements.length === 0) {
        return { error: 'No page context available for automation' };
      }

      // Create a proper screenshot object that matches the automation worker's expected format
      let screenshotObj;
      if (screenshot && typeof screenshot === 'object' && screenshot.width && screenshot.height && screenshot.data) {
        // Check if this is PNG base64 data (new format)
        if (screenshot.format === 'png' && typeof screenshot.data === 'string') {
          console.log('[NLU] Using PNG base64 screenshot from frontend:', {
            width: screenshot.width,
            height: screenshot.height,
            format: screenshot.format,
            dataLength: screenshot.data.length
          });
          screenshotObj = {
            width: screenshot.width,
            height: screenshot.height,
            data: `data:image/png;base64,${screenshot.data}`, // Format as proper data URI
            format: 'png'
          };
        } else if (Array.isArray(screenshot.data)) {
          // Legacy raw pixel data format
          console.log('[NLU] Using raw pixel screenshot from frontend:', {
            width: screenshot.width,
            height: screenshot.height,
            dataLength: screenshot.data.length
          });
          screenshotObj = {
            width: screenshot.width,
            height: screenshot.height,
            data: screenshot.data
          };
        } else {
          console.log('[NLU] Unknown screenshot format, using placeholder');
          const width = 50;
          const height = 50;
          const pixelCount = width * height * 4;
          const whitePixelData = new Array(pixelCount).fill(255);
          
          screenshotObj = {
            width,
            height,
            data: whitePixelData
          };
        }
      } else if (screenshot && typeof screenshot === 'string' && screenshot.length > 0) {
        // Base64 screenshot string (would need parsing)
        console.log('[NLU] Received base64 screenshot, using placeholder for now');
        const width = 50;  // Reduced from 100 to 50
        const height = 50; // Reduced from 100 to 50
        const pixelCount = width * height * 4; // RGBA
        const whitePixelData = new Array(pixelCount).fill(255);
        
        screenshotObj = {
          width,
          height,
          data: whitePixelData
        };
      } else {
        // Create a minimal white screenshot as placeholder
        console.log('[NLU] No screenshot provided, using white placeholder');
        const width = 50;  // Reduced from 100 to 50
        const height = 50; // Reduced from 100 to 50
        const pixelCount = width * height * 4; // RGBA
        const whitePixelData = new Array(pixelCount).fill(255); // All white pixels
        
        screenshotObj = {
          width,
          height,
          data: whitePixelData
        };
      }

      // Format elements to match automation worker expectations
      const formattedElements = elements.map((el, index) => ({
        localId: el.localId || index,
        type: el.type || '',
        text: el.text || '',
        tag: el.tag || '',
        placeholder: el.placeholder || '',
        value: el.value || '',
        attributes: {
          type: el.type || '',
          class: el.className || '',
          style: {
            position: 'static',
            display: 'block',
            visibility: 'visible'
          }
        },
        bounds: {
          x: 0,
          y: 0,
          width: 100,
          height: 30
        }
      }));

      const payload = {
        goal,
        elements: formattedElements,
        screenshot: screenshotObj,
        html: '' // Optional, could be included if needed
      };

      console.log('[NLU] Calling Automation Worker via Service Binding:', { 
        goal, 
        elementsCount: formattedElements.length,
        screenshotType: typeof screenshotObj,
        screenshotWidth: screenshotObj.width,
        screenshotHeight: screenshotObj.height,
        screenshotDataLength: screenshotObj.data.length,
        usingServiceBinding: true
      });
      
      // Don't log the full payload as it's too large with screenshot data
      console.log('[NLU] Payload structure:', {
        goal: payload.goal,
        elementsCount: payload.elements.length,
        screenshotDimensions: `${payload.screenshot.width}x${payload.screenshot.height}`,
        html: payload.html,
        sampleElement: payload.elements[0] || 'none'
      });

      // Use Service Binding instead of direct fetch
      const request = new Request('https://automation-worker/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      console.log('[NLU] Making Service Binding call to AUTOMATION_WORKER...');
      const response = await this.env.AUTOMATION_WORKER.fetch(request);

      console.log('[NLU] Automation Worker response status:', response.status);
      console.log('[NLU] Automation Worker response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[NLU] Automation Worker error response:', errorText);
        return { error: `Automation Worker request failed: ${response.status} - ${errorText}` };
      }

      const result = await response.json();
      console.log('[NLU] Automation Worker success response:', result);
      return {
        steps: result.steps || [],
        error: result.error
      };
    } catch (error: any) {
      console.error('[NLU] Automation Worker call error:', error);
      return { error: error.message || String(error) };
    }
  }

  /**
   * Call the Research worker for web queries using Service Bindings
   */
  private async callResearch(query: string): Promise<any> {
    try {
      const payload = { query };

      console.log('[NLU] Calling Research Worker via Service Binding with query:', query);

      // Use Service Binding instead of direct fetch
      const request = new Request('https://research-worker/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload)
      });

      console.log('[NLU] Making Service Binding call to RESEARCH_WORKER...');
      const response = await this.env.RESEARCH_WORKER.fetch(request);

      console.log('[NLU] Research Worker response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[NLU] Research Worker error response:', errorText);
        return { error: `Research Worker request failed: ${response.status} - ${errorText}` };
      }

      const result = await response.json();
      console.log('[NLU] Research Worker success response:', result);
      return {
        content: result.content || result.answer || result.result || '',
        error: result.error
      };
    } catch (error: any) {
      console.error('[NLU] Research Worker call error:', error);
      return { error: error.message || String(error) };
    }
  }

  private encodeWav(pcm: Uint8Array, sampleRate: number, channels: number): Uint8Array {
    // 16-bit PCM WAV header
    const bitsPerSample = 16;
    const byteRate = sampleRate * channels * (bitsPerSample / 8);
    const blockAlign = channels * (bitsPerSample / 8);
    const dataSize = pcm.length;
    const buffer = new Uint8Array(44 + dataSize);
    const view = new DataView(buffer.buffer);
    // RIFF header
    this.writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + dataSize, true);
    this.writeString(view, 8, 'WAVE');
    this.writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true); // Subchunk1Size
    view.setUint16(20, 1, true); // AudioFormat (PCM)
    view.setUint16(22, channels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitsPerSample, true);
    this.writeString(view, 36, 'data');
    view.setUint32(40, dataSize, true);
    // PCM data
    buffer.set(pcm, 44);
    return buffer;
  }

  private writeString(view: DataView, offset: number, str: string) {
    for (let i = 0; i < str.length; i++) {
      view.setUint8(offset + i, str.charCodeAt(i));
    }
  }

  /**
   * Run CF Workers AI noise suppressor model and return cleaned PCM (WAV-encoded)
   */
  private async denoiseAudio(wavBytes: Uint8Array, sampleRate: number): Promise<Uint8Array | null> {
    if (!this.env.AI || typeof this.env.AI.run !== 'function') {
      console.warn('[NLU] Workers AI binding not available');
      return null;
    }
    try {
      // Encode to base64 string for AI request
      const b64 = btoa(String.fromCharCode(...wavBytes));
      const aiInput = {
        audio: {
          data: b64,
          format: 'wav',
          sample_rate: sampleRate
        }
      };
      // Model id per CF docs (edge-ai/noise-suppressor-v1). Change if needed.
      const response = await this.env.AI.run('@cf/edge-ai/noise-suppressor-v1', aiInput);
      if (response?.audio?.data) {
        console.log('[NLU] Workers AI noise suppression succeeded');
        // Convert base64 back to Uint8Array
        const bin = atob(response.audio.data);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; i++) arr[i] = bin.charCodeAt(i);
        return arr;
      }
      console.warn('[NLU] Workers AI noise suppressor returned no audio');
      return null;
    } catch (err: any) {
      console.error('[NLU] Workers AI noise suppression error:', err);
      return null;
    }
  }
} 