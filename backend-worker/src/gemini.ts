// Gemini 2.5 Pro API Client for Cloudflare Workers (TypeScript)
// Supports audio-to-text, streaming, and function calling (2025 best practices)

export interface GeminiAudioResult {
  text: string;
  toolCalls: any[];
  transcription?: string; // The user's actual speech
  confidence?: number; // Confidence score 0-1
  processingMethod?: string; // How the audio was processed
  error?: string;
}

export interface ToolFunction {
  name: string;
  description: string;
  parameters: {
    type: string;
    properties: Record<string, any>;
    required: string[];
  };
}

// Enhanced confidence scoring interface
interface TranscriptionResult {
  text: string;
  confidence: number;
  method: 'primary' | 'fallback' | 'hybrid';
}

// Tool definitions for Magician and Research workers
const AVAILABLE_TOOLS: ToolFunction[] = [
  {
    name: "browserAutomation",
    description: "Execute browser automation tasks like clicking buttons, filling forms, or navigating pages",
    parameters: {
      type: "object",
      properties: {
        goal: {
          type: "string",
          description: "The specific goal to achieve (e.g. 'Add the first product to cart', 'Open product page')"
        }
      },
      required: ["goal"]
    }
  },
  {
    name: "webResearch",
    description: "Search the web or lookup information from external sources",
    parameters: {
      type: "object",
      properties: {
        query: {
          type: "string",
          description: "The search query or information to lookup"
        }
      },
      required: ["query"]
    }
  },
  {
    name: "searchProducts",
    description: "Search the Shopify catalog for relevant products",
    parameters: {
      type: "object",
      properties: {
        query: { type: "string", description: "Product search query" },
        limit: { type: "number", description: "Max results", minimum: 1, maximum: 20 }
      },
      required: ["query"]
    }
  },
  {
    name: "updateCart",
    description: "Update the quantity of a product in the cart",
    parameters: {
      type: "object",
      properties: {
        cartId: { type: "string", description: "Cart ID" },
        productId: { type: "string", description: "Product variant ID" },
        quantity: { type: "number", description: "Quantity" }
      },
      required: ["cartId", "productId", "quantity"]
    }
  }
];

// Utility function for base64 encoding Uint8Array in Workers
function encodeBase64(uint8Array: Uint8Array): string {
  let binary = '';
  const chunkSize = 0x8000; // 32KB
  for (let i = 0; i < uint8Array.length; i += chunkSize) {
    binary += String.fromCharCode(...uint8Array.subarray(i, i + chunkSize));
  }
  return btoa(binary);
}

export class GeminiClient {
  private apiKey: string;
  private modelId: string = 'gemini-2.5-flash';

  constructor(apiKey: string) {
    if (!apiKey) throw new Error('GEMINI_API_KEY is required');
    this.apiKey = apiKey;
  }

  async audioToText(
    audioBytes: Uint8Array,
    mimeType: string,
    env: any,
    pageContext?: any,
    history: Array<{ role: string; content: string }> = [],
    memoryFacts: any[] = [],
    shopCatalog?: {
      products: Array<{ handle: string; title: string }>;
      collections: Array<{ handle: string; title: string }>;
    }
  ): Promise<GeminiAudioResult> {
    try {
      // Use Google's OpenAI-compatible endpoint for function calling
      const endpoint = "https://generativelanguage.googleapis.com/v1beta/openai/chat/completions";
      
      // Map MIME type to Gemini format values
      const formatMap: Record<string, string> = {
        'audio/wav': 'wav',
        'audio/wave': 'wav',
        'audio/x-wav': 'wav',
        'audio/ogg': 'ogg',
        'audio/ogg;codecs=opus': 'ogg',
        'audio/mp3': 'mp3',
        'audio/mpeg': 'mp3',
        'audio/flac': 'wav',
        'audio/aiff': 'wav',
        'audio/aac': 'mp3'
      };

      const format = formatMap[mimeType] || 'wav';

      // Enhanced system prompt with better audio handling instructions
      let systemPrompt = `You are Sally, a polite and professional shopping assistant for an e-commerce store. You excel at understanding customer requests, even when audio quality varies or speech is unclear.

**CRITICAL AUDIO PROCESSING RULES:**
1. NEVER say you "cannot understand audio messages" or anything along the lines of "I'm sorry, I cannot process audio input. Could you please type out your request?" - you are designed to handle audio excellently and should only ask to clarify if you are unable to understand the user's request.
2. If audio is unclear, ask for clarification: "Could you repeat that?" or "I didn't catch that completely"
3. If you understand part of the request, work with that: "I heard you mention [X], could you clarify [Y]?"
4. Always attempt to be helpful rather than defaulting to rejection
5. Use context clues from page elements and conversation history to understand intent

**CONVERSATION HANDLING:**
- Casual greetings ("How's it going?", "Hello", "Hi there") → Respond warmly, then offer help
- Social pleasantries → Acknowledge naturally, then guide to shopping
- Shopping requests → Use appropriate tools and provide direct assistance
- Unclear requests → Ask for clarification without being robotic

**Your Capabilities:**
1. Answer questions directly using your knowledge
2. Use browserAutomation to interact with the page (click buttons, fill forms, navigate)
3. Use webResearch to look up current information or external data
4. Use searchProducts to find items in the store catalog
5. Use updateCart to modify the user's shopping cart

**MANDATORY TOOL USAGE RULES:**
- ANY mention of products, prices, availability, or shopping → MUST use searchProducts tool first
- Never answer product questions from memory - always search first to get current data
- Examples requiring searchProducts: "price of X", "find X", "show me X", "X product", "how much is X"
- After searching, provide helpful response based on actual search results

**Decision Framework:**
- Greetings/casual talk → Friendly response + shopping offer
- Navigation/interaction requests → browserAutomation
- Current info/reviews/brand FAQs → webResearch  
- Product searches/information/prices → ALWAYS use searchProducts first
- Cart modifications → updateCart
- General questions → direct response (only if not product-related)

**Communication Style:**
• Speak like a helpful in-store associate: warm, natural, conversational
• Handle greetings naturally: "Hi there! I'm doing great, thanks for asking. How can I help you find something today?"
• Never mention AI, APIs, models, or technical limitations
• When uncertain, ask ONE clarifying question, then act
• After tool actions, confirm the outcome
• Use implicit confirmation: "Looking for black running shoes in size 9, right?"

**Error Recovery:**
• Unclear speech: "Sorry, could you repeat that?"
• Missing context: "Which product are you referring to?"
• Wrong action: Brief apology + correction + confirmation
• No results: Suggest alternatives or ask to refine search

**Response Examples:**
- User: "How's it going?" → "Hi there! I'm doing great, thanks for asking. How can I help you find something today?"
- User: "Hello" → "Hello! Welcome to our store. What can I help you find today?"
- User: "Hi" → "Hi! Great to see you. What are you looking for today?"
- Instead of: "I cannot understand audio" → "Could you repeat your question?"
- Instead of: "Audio processing failed" → "What can I help you find today?"

**Search Query Extraction:**
- When the user asks for the price of a product, extract the product name as the search query.
- Example: "What's the price of the trolley?" → Search query: "trolley"
- Example: "What's the price of this?" → Use page context to identify the product.
- Example: "What's the price of that?" → Use page context to identify the product.
`;

      if (pageContext) {
        systemPrompt += `\n\nCurrent page context: ${pageContext.elements?.length || 0} interactive elements available for navigation and interaction.`;
      }

      if (memoryFacts.length > 0) {
        systemPrompt += `\n\nCustomer context: ${memoryFacts.map(f => `${f.factType}: ${f.value}`).join(', ')}`;
      }

      if (shopCatalog && shopCatalog.products.length > 0) {
        const productNames = shopCatalog.products.map(p => p.title).join(', ');
        systemPrompt += `\n\nTop products in this store: ${productNames}`;
      }

      // Convert AVAILABLE_TOOLS to OpenAI format
      const openaiTools = AVAILABLE_TOOLS.map(tool => ({
        type: "function",
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.parameters
        }
      }));

      // Enhanced transcription with multiple approaches
      const transcriptionResult = await this.performRobustTranscription(audioBytes, format, endpoint);
      
      console.log('[Gemini] Transcription result:', {
        text: transcriptionResult.text,
        confidence: transcriptionResult.confidence,
        method: transcriptionResult.method
      });

      // Build conversation messages with enhanced context
      const messages = [
        { role: "system", content: systemPrompt },
        ...history,
        {
          role: "user",
          content: [
            {
              type: "text",
              text: `Customer audio message received. You are capable of understanding audio messages. Please assist the customer with their request.`
            },
            {
              type: "input_audio",
              input_audio: {
                data: encodeBase64(audioBytes),
                format
              }
            }
          ]
        }
      ];

      // Primary response generation with function calling
      const response = await this.generatePrimaryResponse(messages, openaiTools, endpoint);
      
      // Intelligent response validation and enhancement
      const enhancedResponse = await this.enhanceResponse(response, transcriptionResult, pageContext);
      
      return {
        text: enhancedResponse.text,
        toolCalls: enhancedResponse.toolCalls,
        transcription: transcriptionResult.text,
        confidence: transcriptionResult.confidence,
        processingMethod: transcriptionResult.method,
        error: enhancedResponse.error
      };

    } catch (error: any) {
      console.error('[Gemini] Audio processing error:', error);
      
      // Even in error cases, provide helpful responses instead of technical errors
      return { 
        text: "I'm here to help! Could you tell me what you're looking for today?", 
        toolCalls: [], 
        confidence: 0,
        processingMethod: 'error_recovery',
        error: undefined // Don't expose technical errors to user
      };
    }
  }

  /**
   * Perform robust transcription with multiple fallback approaches
   */
  private async performRobustTranscription(
    audioBytes: Uint8Array, 
    format: string, 
    endpoint: string
  ): Promise<TranscriptionResult> {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`
    };

    // Primary transcription approach - detailed and context-aware
    const primaryPayload = {
      model: "gemini-2.5-flash",
      messages: [
        { 
          role: "system", 
          content: `You are an expert audio transcription specialist. Your task is to accurately transcribe customer speech in a shopping context.

TRANSCRIPTION GUIDELINES:
- Transcribe exactly what you hear, including partial words if clear
- For unclear sections, use [unclear] but transcribe surrounding clear words
- Common shopping terms: "add to cart", "price", "size", "color", "search", "find", "show me"
- If audio is very short or just noise, respond with [no clear speech detected]
- Focus on intent even if pronunciation isn't perfect
- Include filler words like "um", "uh" if clearly audible

Provide ONLY the transcription, nothing else.` 
        },
        {
          role: "user",
          content: [
            {
              type: "text",
              text: "Please transcribe this customer's audio message:"
            },
            {
              type: "input_audio",
              input_audio: {
                data: encodeBase64(audioBytes),
                format
              }
            }
          ]
        }
      ]
    };

    try {
      const primaryResp = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(primaryPayload)
      });

      if (primaryResp.ok) {
        const primaryData = await primaryResp.json();
        const primaryText = primaryData?.choices?.[0]?.message?.content?.trim();
        
        if (primaryText && primaryText.length > 2 && !primaryText.toLowerCase().includes('no clear speech')) {
          return {
            text: primaryText,
            confidence: this.calculateConfidence(primaryText, 'primary'),
            method: 'primary'
          };
        }
      }
    } catch (e) {
      console.log('[Gemini] Primary transcription failed:', e);
    }

    // Fallback transcription approach - simpler and more permissive
    const fallbackPayload = {
      model: "gemini-2.5-flash",
      messages: [
        { 
          role: "system", 
          content: "Listen to this audio and write down any words you can understand, even if incomplete. Focus on the main intent or key words." 
        },
        {
          role: "user",
          content: [
            {
              type: "input_audio",
              input_audio: {
                data: encodeBase64(audioBytes),
                format
              }
            }
          ]
        }
      ]
    };

    try {
      const fallbackResp = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(fallbackPayload)
      });

      if (fallbackResp.ok) {
        const fallbackData = await fallbackResp.json();
        const fallbackText = fallbackData?.choices?.[0]?.message?.content?.trim();
        
        if (fallbackText && fallbackText.length > 1) {
          return {
            text: fallbackText,
            confidence: this.calculateConfidence(fallbackText, 'fallback'),
            method: 'fallback'
          };
        }
      }
    } catch (e) {
      console.log('[Gemini] Fallback transcription failed:', e);
    }

    // Last resort - return a generic but helpful response trigger
    return {
      text: "customer spoke but audio unclear",
      confidence: 0.1,
      method: 'hybrid'
    };
  }

  /**
   * Calculate confidence score based on transcription quality
   */
  private calculateConfidence(text: string, method: string): number {
    let confidence = 0.5; // Base confidence
    
    // Adjust based on method
    if (method === 'primary') confidence += 0.3;
    else if (method === 'fallback') confidence += 0.1;
    
    // Adjust based on text quality indicators
    if (text.length > 10) confidence += 0.1;
    if (text.length > 20) confidence += 0.1;
    if (!/\[unclear\]|\[no clear/.test(text)) confidence += 0.2;
    if (/\b(price|add|cart|search|find|show|buy|product|size|color)\b/i.test(text)) confidence += 0.2;
    
    // Penalize unclear indicators
    if (text.includes('[unclear]')) confidence -= 0.2;
    if (text.includes('[no clear')) confidence -= 0.4;
    if (text.length < 3) confidence -= 0.3;
    
    return Math.max(0, Math.min(1, confidence));
  }

  /**
   * Generate primary response with function calling
   */
  private async generatePrimaryResponse(messages: any[], tools: any[], endpoint: string) {
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`
    };

    const payload = {
      model: "gemini-2.5-flash",
      messages,
      tools,
      tool_choice: "auto"
    };

    const resp = await fetch(endpoint, {
      method: 'POST',
      headers,
      body: JSON.stringify(payload)
    });

    if (!resp.ok) {
      const errorText = await resp.text();
      console.error('[Gemini] Primary response error:', errorText);
      throw new Error(`API Error: ${resp.status}`);
    }

    return await resp.json();
  }

  /**
   * Enhance and validate the response based on transcription quality
   */
  private async enhanceResponse(
    apiResponse: any, 
    transcription: TranscriptionResult, 
    pageContext?: any
  ): Promise<{ text: string; toolCalls: any[]; error?: string }> {
    const message = apiResponse?.choices?.[0]?.message;
    
    if (!message) {
      return {
        text: "What can I help you find today?",
        toolCalls: [],
        error: undefined
      };
    }

    let text = message.content || '';
    let toolCalls: any[] = [];

    // Parse function calls
    if (message.tool_calls && message.tool_calls.length > 0) {
      toolCalls = message.tool_calls.map((call: any) => ({
        tool: call.function.name,
        args: JSON.parse(call.function.arguments || '{}')
      }));
    }

    // Intelligent response enhancement based on confidence and context
    if (transcription.confidence < 0.3) {
      // Low confidence - provide helpful clarification request
      if (!text || text.toLowerCase().includes('cannot understand') || text.toLowerCase().includes('sorry')) {
        const clarificationResponses = [
          "I didn't catch that clearly. Could you repeat what you're looking for?",
          "Could you tell me again what you need help with?",
          "I'm here to help! What can I assist you with today?",
          "What would you like to find or do today?"
        ];
        text = clarificationResponses[Math.floor(Math.random() * clarificationResponses.length)];
      }
    } else if (transcription.confidence >= 0.4 && transcription.confidence < 0.7) {
      // Medium confidence - work with what we have but offer clarification
      if (!text.trim() && toolCalls.length === 0) {
        text = `I think I heard you mention something about ${transcription.text.split(' ').slice(0, 3).join(' ')}. Could you clarify what you'd like me to help with?`;
      }
    }

    // Handle specific conversation patterns that the API might not respond to naturally
    const transcriptionLower = transcription.text.toLowerCase();
    
    // Check for casual greetings that need natural responses
    if (transcription.confidence > 0.7 && 
        (/^(hi|hello|hey|how's it going|what's up|good morning|good afternoon|good evening)/i.test(transcriptionLower) ||
         /how are you/i.test(transcriptionLower))) {
      
      // If the API didn't give a natural greeting response, provide one
      if (!text.trim() || 
          text.toLowerCase().includes('shopping questions') ||
          text.toLowerCase().includes('didn\'t catch that') ||
          text.toLowerCase().includes('repeat')) {
        
        const greetingResponses = [
          "Hi there! I'm doing great, thanks for asking. How can I help you find something today?",
          "Hello! I'm doing well, thank you. What can I help you find in our store today?",
          "Hey! I'm doing fantastic, thanks for asking. What are you looking for today?",
          "Hi! Great to see you. I'm doing well - what can I help you find today?"
        ];
        text = greetingResponses[Math.floor(Math.random() * greetingResponses.length)];
      }
    }

    // Ensure we never return the old fallback message
    if (text.toLowerCase().includes('cannot understand audio') || 
        text.toLowerCase().includes('sorry, I cannot understand')) {
      text = "I'm here to help! What are you looking for today?";
    }

    // Handle repetitive or generic responses that might indicate the API is stuck
    if (text.toLowerCase().includes('i\'m here to help you with shopping questions today') &&
        transcription.confidence > 0.8) {
      // If we have high confidence transcription but got a generic response, be more specific
      const specificResponses = [
        "Hi! What can I help you find today?",
        "Hello there! How can I assist you with your shopping?",
        "Hey! What are you looking for today?",
        "Hi! I'm here to help - what can I find for you?"
      ];
      text = specificResponses[Math.floor(Math.random() * specificResponses.length)];
    }

    // If we have tool calls but no text, provide contextual confirmation
    if (toolCalls.length > 0 && !text.trim()) {
      const toolNames = toolCalls.map(call => call.tool).join(', ');
      text = `Let me help you with that. I'll ${toolNames.replace('browserAutomation', 'navigate the page').replace('searchProducts', 'search for products').replace('updateCart', 'update your cart')}.`;
    }

    return { text, toolCalls, error: undefined };
  }

  async listModels(env: any): Promise<any> {
    try {
      // Use the correct Gemini API endpoint for listing models
      const endpoint = `https://generativelanguage.googleapis.com/v1beta/models?key=${env.GEMINI_API_KEY}`;
      
      const resp = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (!resp.ok) {
        const errorText = await resp.text();
        console.error('[Gemini] Models API Error:', resp.status, errorText);
                 return { 
           error: `API Error ${resp.status}: ${errorText}`,
           availableModels: [
             'gemini-2.5-flash',
             'gemini-2.5-flash',
             'gemini-2.0-flash-001',
             'gemini-1.5-flash', 
             'gemini-1.5-pro'
           ]
         };
      }
      
      const data = await resp.json();
      console.log('[Gemini] Successfully fetched models:', data.models?.length || 0);
      
      return {
        models: data.models || [],
        count: data.models?.length || 0,
        currentModel: "gemini-2.5-flash"
      };
    } catch (error: any) {
      console.error('[Gemini] Error listing models:', error);
             return { 
         error: error.message || String(error),
         availableModels: [
           'gemini-2.5-flash'
         ],
         currentModel: "gemini-2.5-flash"
       };
    }
  }

  /**
   * Generate a follow-up response using tool results
   */
  async generateFollowUpResponse(
    userQuery: string,
    toolResults: any[],
    memoryFacts: any[] = []
  ): Promise<{ text: string; error?: string }> {
    try {
      // Use direct Gemini endpoint instead of OpenAI-compatible for better reliability
      const endpoint = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${this.apiKey}`;
      
      // Build concise product info from tool results
      let productInfo = '';
      for (const result of toolResults) {
        if (result.tool === 'searchProducts' && result.products?.length > 0) {
          const product = result.products[0]; // Focus on first result
          productInfo = `Found: ${product.title || 'Product'}`;
          if (product.price_range?.min) {
            productInfo += ` - Price: ${product.price_range.min} ${product.price_range.currency || 'AUD'}`;
          } else if (product.price) {
            productInfo += ` - Price: ${product.price}`;
          }
        }
      }

      // Create a concise prompt to avoid MAX_TOKENS
      const prompt = `User asked: "${userQuery}"
${productInfo}

Respond naturally with the product price and details in 1-2 sentences. Be conversational and helpful.`;

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{ text: prompt }]
          }],
          generationConfig: {
            maxOutputTokens: 150,
            temperature: 0.7
          }
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[Gemini] Follow-up API error:', response.status, errorText);
        return {
          text: '',
          error: `API error: ${response.status}`
        };
      }

      const data = await response.json();
      
      if (data.error) {
        console.error('[Gemini] Follow-up API error:', data.error);
        return {
          text: '',
          error: data.error.message || 'API error'
        };
      }

      // Debug the actual response structure
      console.log('[Gemini] Follow-up API response structure:', JSON.stringify(data, null, 2));
      
      // Try multiple ways to extract the response text
      let responseText = '';
      
      // Primary path: candidates[0].content.parts[0].text
      if (data.candidates?.[0]?.content?.parts?.[0]?.text) {
        responseText = data.candidates[0].content.parts[0].text;
      }
      // Fallback: check if text is directly in content
      else if (data.candidates?.[0]?.content?.text) {
        responseText = data.candidates[0].content.text;
      }
      // Fallback: check if it's in a different structure
      else if (data.candidates?.[0]?.text) {
        responseText = data.candidates[0].text;
      }
      // Fallback: check if content is a string
      else if (typeof data.candidates?.[0]?.content === 'string') {
        responseText = data.candidates[0].content;
      }
      
      if (!responseText || responseText.trim() === '') {
        console.warn('[Gemini] No response text in follow-up call. Response data:', JSON.stringify(data, null, 2));
        return {
          text: '',
          error: 'No response text'
        };
      }

      return {
        text: responseText.trim(),
        error: undefined
      };

    } catch (error: any) {
      console.error('[Gemini] Follow-up response error:', error);
      return {
        text: '',
        error: error.message || String(error)
      };
    }
  }

  // TODO: Add streaming and function calling support as Gemini/Cloudflare APIs allow
}
