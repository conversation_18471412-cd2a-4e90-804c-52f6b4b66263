// Durable Object for session/context management in Cloudflare Workers
// April 2025 best practices

// TODO: Replace 'any' with the correct DurableObjectState type from your project's types when available.
type DurableObjectState = any;

/**
 * SessionState defines the shape of the persistent state for a session.
 */
export interface SessionState {
  conversation: Array<{ role: string; content: string }>;
  lastUpdated: number;
  // Add more fields as needed for context
}

/**
 * Durable Object for managing per-session state.
 */
export class SessionDO {
  private state: DurableObjectState;
  private env: any;
  private session: SessionState | null = null;

  constructor(state: DurableObjectState, env: any) {
    this.state = state;
    this.env = env;
  }

  /**
   * Handle incoming requests to the Durable Object.
   */
  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    console.log('[SessionDO] Received request:', request.method, url.pathname);
    
    // Handle both /state and /session/state for backwards compatibility
    if (request.method === 'GET' && (url.pathname === '/state' || url.pathname === '/session/state')) {
      await this.load();
      console.log('[SessionDO] Returning session data:', this.session);
      return new Response(JSON.stringify(this.session), { headers: { 'Content-Type': 'application/json' } });
    }
    if (request.method === 'POST' && (url.pathname === '/state' || url.pathname === '/session/state')) {
      const data = await request.json();
      console.log('[SessionDO] Setting session data:', data);
      await this.setState(data);
      return new Response(JSON.stringify({ success: true }), { headers: { 'Content-Type': 'application/json' } });
    }
    
    console.log('[SessionDO] Not found:', request.method, url.pathname);
    return new Response('Not found', { status: 404 });
  }

  /**
   * Load session state from storage.
   */
  private async load() {
    this.session = (await this.state.storage.get('session')) || {
      conversation: [],
      lastUpdated: Date.now(),
    };
  }

  /**
   * Set and persist session state.
   */
  private async setState(data: Partial<SessionState>) {
    await this.load();
    this.session = { ...this.session, ...data, conversation: data.conversation ?? this.session?.conversation ?? [], lastUpdated: Date.now() };
    await this.state.storage.put('session', this.session);
  }
} 