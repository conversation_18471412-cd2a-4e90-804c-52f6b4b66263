// Neo4j Graph Memory Durable Object for Aura Voice AI Assistant
// Implements temporal knowledge graph with edge caching for fast retrieval

export interface MemoryFact {
  id?: string;
  userId: string;
  factType:
    | 'LIKES'
    | 'DISLIKES'
    | 'ASKED_ABOUT'
    | 'HAS_PERSONA'
    | 'GOAL'
    | 'HAS_POLICY'
    | 'INTEREST_PRODUCT'
    | 'INTEREST_COLLECTION';
  value: string;
  metadata?: Record<string, any>;
  timestamp?: number;
}

interface CachedUserMemory {
  facts: MemoryFact[];
  lastUpdated: number;
  ttl: number; // Time to live in ms
}

export class GraphMemoryDO {
  private state: any;
  private env: any;
  private memoryCache: Map<string, CachedUserMemory> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private neo4jUrl: string;
  private neo4jAuth: string;

  constructor(state: any, env: any) {
    this.state = state;
    this.env = env;
    this.neo4jUrl = env.NEO4J_URL || 'https://localhost:7474/db/neo4j/query/v2';
    this.neo4jAuth = this.buildAuth(env.NEO4J_USER || 'neo4j', env.NEO4J_PASSWORD || 'password');
  }

  private buildAuth(username: string, password: string): string {
    return `Basic ${btoa(`${username}:${password}`)}`;
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    if (request.method === 'OPTIONS') {
      return new Response(null, { status: 204, headers: corsHeaders });
    }

    try {
      if (request.method === 'POST' && url.pathname === '/addFact') {
        const { userId, factType, value, metadata } = await request.json();
        const result = await this.addFact(userId, factType, value, metadata);
        return new Response(JSON.stringify(result), { 
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      if (request.method === 'GET' && url.pathname === '/queryFacts') {
        const userId = url.searchParams.get('userId');
        const query = url.searchParams.get('query');
        if (!userId) {
          return new Response('Missing userId', { status: 400, headers: corsHeaders });
        }
        const facts = await this.queryFacts(userId, query || undefined);
        return new Response(JSON.stringify({ facts }), { 
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      if (request.method === 'GET' && url.pathname === '/getUserProfile') {
        const userId = url.searchParams.get('userId');
        if (!userId) {
          return new Response('Missing userId', { status: 400, headers: corsHeaders });
        }
        const profile = await this.getUserProfile(userId);
        return new Response(JSON.stringify(profile), { 
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }

      if (request.method === 'POST' && url.pathname === '/recordInterest') {
        const { userId, type, handle, metadata } = await request.json();
        if (!userId || !type || !handle) {
          return new Response('Missing parameters', { status: 400, headers: corsHeaders });
        }
        const factType = type === 'collection' ? 'INTEREST_COLLECTION' : 'INTEREST_PRODUCT';
        const result = await this.addFact(userId, factType, handle, metadata);
        return new Response(JSON.stringify(result), { headers: { 'Content-Type': 'application/json', ...corsHeaders } });
      }

      if (request.method === 'GET' && url.pathname === '/getRecommendations') {
        const userId = url.searchParams.get('userId');
        const limitParam = url.searchParams.get('limit');
        const limit = limitParam ? parseInt(limitParam, 10) : 10;
        if (!userId) {
          return new Response('Missing userId', { status: 400, headers: corsHeaders });
        }
        const recs = await this.getRecommendations(userId, limit);
        return new Response(JSON.stringify({ recommendations: recs }), { headers: { 'Content-Type': 'application/json', ...corsHeaders } });
      }

      // === Shopify Webhook: syncCatalog ===
      if (request.method === 'POST' && url.pathname === '/syncCatalog') {
        const { type, handle, title } = await request.json();
        if (!type || !handle) {
          return new Response('Missing parameters', { status: 400, headers: corsHeaders });
        }
        const label = type === 'collection' ? 'Collection' : 'Product';
        const cypher = `MERGE (n:${label} {name: $handle}) SET n.title = $title RETURN n`;
        try {
          await this.executeCypher(cypher, { handle, title: title || '' });
          return new Response(JSON.stringify({ success: true }), { headers: { 'Content-Type': 'application/json', ...corsHeaders } });
        } catch (err: any) {
          return new Response(JSON.stringify({ error: err.message }), { status: 500, headers: { 'Content-Type': 'application/json', ...corsHeaders } });
        }
      }

      return new Response('Not found', { status: 404, headers: corsHeaders });
    } catch (error: any) {
      console.error('[GraphMemoryDO] Error:', error);
      return new Response(JSON.stringify({ error: error.message }), { 
        status: 500, 
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }
  }

  /**
   * Add a memory fact to the Neo4j graph
   */
  async addFact(userId: string, factType: string, value: string, metadata?: Record<string, any>): Promise<{ success: boolean; error?: string }> {
    try {
      // Invalidate cache for this user
      this.memoryCache.delete(userId);

      const targetLabel = factType === 'INTEREST_PRODUCT' ? 'Product' : factType === 'INTEREST_COLLECTION' ? 'Collection' : 'Interest';

      const cypher = `
        MERGE (u:User {id: $userId})
        MERGE (target:${targetLabel} {name: $value})
        MERGE (u)-[r:${factType} {
          created_at: $timestamp,
          metadata: $metadata
        }]->(target)
        RETURN r
      `;

      const parameters = {
        userId,
        value,
        timestamp: Date.now(),
        metadata: JSON.stringify(metadata || {})
      };

      const result = await this.executeCypher(cypher, parameters);
      
      if (result.errors && result.errors.length > 0) {
        console.error('[GraphMemoryDO] Neo4j errors:', result.errors);
        return { success: false, error: result.errors[0].message };
      }

      console.log('[GraphMemoryDO] Added fact:', { userId, factType, value });
      return { success: true };
    } catch (error: any) {
      console.error('[GraphMemoryDO] Error adding fact:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Query relevant facts for a user, with optional query filtering
   */
  async queryFacts(userId: string, query?: string): Promise<MemoryFact[]> {
    try {
      // Check cache first
      const cached = this.memoryCache.get(userId);
      if (cached && Date.now() - cached.lastUpdated < cached.ttl) {
        console.log('[GraphMemoryDO] Using cached facts for user:', userId);
        return this.filterFactsByQuery(cached.facts, query);
      }

      // Fetch from Neo4j
      const cypher = `
        MATCH (u:User {id: $userId})-[r]->(target)
        RETURN type(r) as factType, target.name as value, r.created_at as timestamp, r.metadata as metadata
        ORDER BY r.created_at DESC
        LIMIT 50
      `;

      const result = await this.executeCypher(cypher, { userId });
      
      if (result.errors && result.errors.length > 0) {
        console.error('[GraphMemoryDO] Neo4j query errors:', result.errors);
        return [];
      }

      // Parse the Query API response format
      const facts: MemoryFact[] = [];
      if (result.data && result.data.values) {
        const fields = result.data.fields || [];
        const factTypeIndex = fields.indexOf('factType');
        const valueIndex = fields.indexOf('value');
        const timestampIndex = fields.indexOf('timestamp');
        const metadataIndex = fields.indexOf('metadata');

        result.data.values.forEach((row: any[]) => {
          let metadata = {};
          try {
            metadata = row[metadataIndex] ? JSON.parse(row[metadataIndex]) : {};
          } catch (e) {
            metadata = {};
          }
          
          facts.push({
            userId,
            factType: row[factTypeIndex],
            value: row[valueIndex],
            timestamp: row[timestampIndex],
            metadata
          });
        });
      }

      // Cache the results
      this.memoryCache.set(userId, {
        facts,
        lastUpdated: Date.now(),
        ttl: this.CACHE_TTL
      });

      console.log('[GraphMemoryDO] Retrieved facts for user:', userId, 'count:', facts.length);
      return this.filterFactsByQuery(facts, query);
    } catch (error: any) {
      console.error('[GraphMemoryDO] Error querying facts:', error);
      return [];
    }
  }

  /**
   * Get a user profile summary
   */
  async getUserProfile(userId: string): Promise<{ preferences: string[]; persona: string[]; goals: string[] }> {
    const facts = await this.queryFacts(userId);
    
    const preferences = facts
      .filter(f => f.factType === 'LIKES')
      .map(f => f.value);
      
    const persona = facts
      .filter(f => f.factType === 'HAS_PERSONA')
      .map(f => f.value);
      
    const goals = facts
      .filter(f => f.factType === 'GOAL')
      .map(f => f.value);

    return { preferences, persona, goals };
  }

  /**
   * Filter facts by query string using simple keyword matching
   */
  private filterFactsByQuery(facts: MemoryFact[], query?: string): MemoryFact[] {
    if (!query) return facts;
    
    const queryLower = query.toLowerCase();
    return facts.filter(fact => 
      fact.value.toLowerCase().includes(queryLower) ||
      fact.factType.toLowerCase().includes(queryLower)
    );
  }

  /**
   * Execute Cypher query against Neo4j Aura Query API
   */
  private async executeCypher(cypher: string, parameters: Record<string, any> = {}) {
    const endpoint = this.neo4jUrl; // Already includes full path
    
    const payload = {
      statement: cypher,
      parameters
    };

    console.log('[GraphMemoryDO] Executing Cypher:', cypher, 'params:', parameters);

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': this.neo4jAuth
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Neo4j request failed: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('[GraphMemoryDO] Neo4j response:', result);
    return result;
  }

  /**
   * Initialize Neo4j constraints and indexes (call once during setup)
   */
  async initializeSchema(): Promise<{ success: boolean; error?: string }> {
    try {
      const constraints = [
        'CREATE CONSTRAINT user_id_unique IF NOT EXISTS FOR (u:User) REQUIRE u.id IS UNIQUE',
        'CREATE INDEX interest_name_index IF NOT EXISTS FOR (i:Interest) ON (i.name)',
        'CREATE INDEX product_name_index IF NOT EXISTS FOR (p:Product) ON (p.name)',
        'CREATE INDEX collection_name_index IF NOT EXISTS FOR (c:Collection) ON (c.name)'
      ];

      for (const constraint of constraints) {
        await this.executeCypher(constraint);
      }

      console.log('[GraphMemoryDO] Schema initialized successfully');
      return { success: true };
    } catch (error: any) {
      console.error('[GraphMemoryDO] Error initializing schema:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Return product/collection recommendations based on INTEREST_ facts frequency & recency
   */
  async getRecommendations(userId: string, limit = 10): Promise<Array<{ handle: string; count: number; type: 'product' | 'collection' }>> {
    const facts = await this.queryFacts(userId);

    // Filter interest facts
    const interests = facts.filter(f => f.factType === 'INTEREST_PRODUCT' || f.factType === 'INTEREST_COLLECTION');

    const counts: Record<string, { count: number; type: 'product' | 'collection' }> = {};

    interests.forEach(f => {
      const key = f.value;
      if (!counts[key]) {
        counts[key] = { count: 0, type: f.factType === 'INTEREST_COLLECTION' ? 'collection' : 'product' };
      }
      counts[key].count += 1;
    });

    // Convert to array and sort by count desc
    const sorted = Object.entries(counts)
      .map(([handle, data]) => ({ handle, count: data.count, type: data.type }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit);

    return sorted;
  }
}