// Comprehensive Shopify MCP Service Implementation
// Handles both Storefront (unauthenticated) and Customer Account (authenticated) MCP servers

export interface ShopifyMcpConfig {
  storeDomain: string;
  customerAccountDomain?: string;
  accessToken?: string; // For authenticated Customer Account API calls
}

export interface ProductSearchInput {
  query: string;
  limit?: number;
  context?: string;
}

export interface CartInput {
  cartId?: string;
  lines?: Array<{
    merchandiseId: string;
    quantity: number;
    lineItemId?: string;
  }>;
}

export interface PolicySearchInput {
  query: string;
  context?: string;
}

export interface OrderInput {
  orderId?: string;
  orderNumber?: string;
  limit?: number;
}

/**
 * Main Shopify MCP Service Class
 * Handles both Storefront and Customer Account MCP servers
 */
export class ShopifyMcpService {
  private config: ShopifyMcpConfig;

  constructor(config: ShopifyMcpConfig) {
    this.config = config;
  }

  // ===== STOREFRONT MCP SERVER METHODS (Unauthenticated) =====

  /**
   * Search products using Storefront MCP server
   */
  async searchProducts(input: ProductSearchInput): Promise<any> {
    return this.callStorefrontMcp('search_shop_catalog', {
      query: input.query,
      limit: input.limit || 10,
      context: input.context || ''
    });
  }

  /**
   * Search store policies and FAQs using Storefront MCP server
   */
  async searchPoliciesAndFaqs(input: PolicySearchInput): Promise<any> {
    return this.callStorefrontMcp('search_shop_policies_and_faqs', {
      query: input.query,
      context: input.context || ''
    });
  }

  /**
   * Get cart contents using Storefront MCP server
   */
  async getCart(cartId: string): Promise<any> {
    return this.callStorefrontMcp('get_cart', {
      cart_id: cartId
    });
  }

  /**
   * Update cart using Storefront MCP server
   */
  async updateCart(input: CartInput): Promise<any> {
    const lines = input.lines?.map(line => ({
      quantity: line.quantity,
      merchandise_id: line.merchandiseId,
      ...(line.lineItemId && { line_item_id: line.lineItemId })
    })) || [];

    const args: any = { lines };
    if (input.cartId) {
      args.cart_id = input.cartId;
    }

    return this.callStorefrontMcp('update_cart', args);
  }

  // ===== CUSTOMER ACCOUNT MCP SERVER METHODS (Authenticated) =====

  /**
   * Get customer orders using Customer Account MCP server
   */
  async getCustomerOrders(input: OrderInput = {}): Promise<any> {
    if (!this.config.accessToken) {
      return { error: 'Customer authentication required for order access' };
    }

    const args: any = {};
    if (input.limit) args.limit = input.limit;
    if (input.orderId) args.order_id = input.orderId;
    if (input.orderNumber) args.order_number = input.orderNumber;

    return this.callCustomerAccountMcp('get_orders', args);
  }

  /**
   * Get specific order details using Customer Account MCP server
   */
  async getOrderDetails(orderIdOrNumber: string): Promise<any> {
    if (!this.config.accessToken) {
      return { error: 'Customer authentication required for order details' };
    }

    // Determine if it's an ID or order number
    const args = orderIdOrNumber.startsWith('gid://') 
      ? { order_id: orderIdOrNumber }
      : { order_number: orderIdOrNumber };

    return this.callCustomerAccountMcp('get_order_details', args);
  }

  /**
   * Get customer account details using Customer Account MCP server
   */
  async getCustomerAccount(): Promise<any> {
    if (!this.config.accessToken) {
      return { error: 'Customer authentication required for account access' };
    }

    return this.callCustomerAccountMcp('get_customer_account', {});
  }

  /**
   * Update customer account details using Customer Account MCP server
   */
  async updateCustomerAccount(updates: any): Promise<any> {
    if (!this.config.accessToken) {
      return { error: 'Customer authentication required for account updates' };
    }

    return this.callCustomerAccountMcp('update_customer_account', updates);
  }

  // ===== PRIVATE HELPER METHODS =====

  /**
   * Make a call to the Storefront MCP server (unauthenticated)
   */
  private async callStorefrontMcp(toolName: string, args: any): Promise<any> {
    const endpoint = `https://${this.config.storeDomain}/api/mcp`;
    return this.makeMcpCall(endpoint, toolName, args, false);
  }

  /**
   * Make a call to the Customer Account MCP server (authenticated)
   */
  private async callCustomerAccountMcp(toolName: string, args: any): Promise<any> {
    if (!this.config.customerAccountDomain) {
      return { error: 'Customer account domain not configured' };
    }

    const endpoint = `https://${this.config.customerAccountDomain}/customer/api/mcp`;
    return this.makeMcpCall(endpoint, toolName, args, true);
  }

  /**
   * Generic MCP call method
   */
  private async makeMcpCall(
    endpoint: string, 
    toolName: string, 
    args: any, 
    requiresAuth: boolean
  ): Promise<any> {
    const body = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'tools/call',
      params: {
        name: toolName,
        arguments: args
      }
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    // Add authentication header for Customer Account MCP calls
    if (requiresAuth && this.config.accessToken) {
      headers['Authorization'] = this.config.accessToken;
    }

    try {
      const resp = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(body)
      });

      if (!resp.ok) {
        if (resp.status === 401 && requiresAuth) {
          return { 
            error: 'Authentication required', 
            requiresAuth: true,
            authUrl: await this.getAuthUrl()
          };
        }

        const errorText = await resp.text();
        console.error(`[SHOPIFY MCP] HTTP error for ${toolName}:`, resp.status, errorText);
        return { error: `MCP request failed: HTTP ${resp.status}` };
      }

      const data = await resp.json();
      if (data.error) {
        console.error(`[SHOPIFY MCP] RPC error for ${toolName}:`, data.error);
        return { error: data.error.message || 'MCP error' };
      }

      // Parse MCP response content
      if (data.result && data.result.content && Array.isArray(data.result.content)) {
        const textContent = data.result.content.find((item: any) => item.type === 'text');
        if (textContent && textContent.text) {
          try {
            return JSON.parse(textContent.text);
          } catch (e) {
            console.error(`[SHOPIFY MCP] Failed to parse response for ${toolName}:`, e);
            return { error: 'Failed to parse response content' };
          }
        }
      }

      return data.result || {};
    } catch (error: any) {
      console.error(`[SHOPIFY MCP] Fetch error for ${toolName}:`, error);
      return { error: error.message || 'Unknown MCP error' };
    }
  }

  /**
   * Get OAuth authorization URL for Customer Account authentication
   */
  private async getAuthUrl(): Promise<string | null> {
    if (!this.config.customerAccountDomain) {
      return null;
    }

    try {
      // Fetch OAuth discovery endpoint
      const discoveryUrl = `https://${this.config.customerAccountDomain}/.well-known/oauth-authorization-server`;
      const resp = await fetch(discoveryUrl);
      
      if (!resp.ok) {
        return null;
      }

      const discovery = await resp.json();
      return discovery.authorization_endpoint || null;
    } catch (error) {
      console.error('[SHOPIFY MCP] Failed to get auth URL:', error);
      return null;
    }
  }

  // ===== UTILITY METHODS =====

  /**
   * List available tools from both MCP servers
   */
  async listAvailableTools(): Promise<any> {
    const results = {
      storefront: await this.listStorefrontTools(),
      customerAccount: await this.listCustomerAccountTools()
    };

    return results;
  }

  /**
   * List tools available on Storefront MCP server
   */
  private async listStorefrontTools(): Promise<any> {
    const endpoint = `https://${this.config.storeDomain}/api/mcp`;
    const body = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'tools/list',
      params: {}
    };

    try {
      const resp = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      if (!resp.ok) {
        return { error: `HTTP ${resp.status}` };
      }

      const data = await resp.json();
      return data.result || [];
    } catch (error: any) {
      return { error: error.message };
    }
  }

  /**
   * List tools available on Customer Account MCP server
   */
  private async listCustomerAccountTools(): Promise<any> {
    if (!this.config.customerAccountDomain) {
      return { error: 'Customer account domain not configured' };
    }

    const endpoint = `https://${this.config.customerAccountDomain}/customer/api/mcp`;
    const body = {
      jsonrpc: '2.0',
      id: Date.now(),
      method: 'tools/list',
      params: {}
    };

    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (this.config.accessToken) {
      headers['Authorization'] = this.config.accessToken;
    }

    try {
      const resp = await fetch(endpoint, {
        method: 'POST',
        headers,
        body: JSON.stringify(body)
      });

      if (!resp.ok) {
        return { error: `HTTP ${resp.status}` };
      }

      const data = await resp.json();
      return data.result || [];
    } catch (error: any) {
      return { error: error.message };
    }
  }
}

/**
 * Factory function to create ShopifyMcpService instance
 */
export function createShopifyMcpService(env: any, accessToken?: string): ShopifyMcpService {
  const config: ShopifyMcpConfig = {
    storeDomain: env.SHOPIFY_STORE_DOMAIN,
    customerAccountDomain: env.SHOPIFY_CUSTOMER_ACCOUNT_DOMAIN,
    accessToken
  };

  return new ShopifyMcpService(config);
}
