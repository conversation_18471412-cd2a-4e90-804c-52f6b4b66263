/**
 * Cartesia TTS Service for high-quality text-to-speech using Sonic voices
 * Integration with Cartesia API following best practices
 */

export interface CartesiaVoiceConfig {
  mode: 'id' | 'embedding';
  id?: string;
  embedding?: number[];
}

export interface CartesiaOutputFormat {
  container: 'wav' | 'mp3' | 'flac';
  encoding: 'pcm_f32le' | 'pcm_s16le' | 'mp3' | 'flac';
  sample_rate: 8000 | 16000 | 22050 | 24000 | 44100 | 48000;
}

export interface CartesiaTtsRequest {
  transcript: string;
  model_id: 'sonic-turbo' | 'sonic-2' | 'sonic-1';
  voice: CartesiaVoiceConfig;
  output_format: CartesiaOutputFormat;
  language?: string;
  // Note: speed and emotion are not supported in the current API
}

export interface CartesiaTtsResult {
  audioData: Uint8Array;
  mimeType: string;
  error?: string;
}

export class CartesiaTtsService {
  private apiKey: string;
  private baseUrl = 'https://api.cartesia.ai';
  private version = '2025-04-16';

  // High-quality voice options
  private readonly VOICES = {
    // Professional female voices
    'professional-female': '70ede8cf-57ed-429f-8fc1-5d7032ecf17c',
    'friendly-female': 'a167e0f3-df7e-4d52-a9c3-f949145efdab',
    'warm-female': '2ee87190-8f84-4925-97da-e52547f9462c',
    
    // Professional male voices  
    'professional-male': '820a3788-2b37-4d21-847a-b65d8a68c99a',
    'friendly-male': '87748186-23bb-4158-a1eb-332911b0b708',
    'warm-male': 'fb26447f-308b-471e-8b00-8e9f04284eb5',
    
    // Default (best quality) - Updated to your preferred voice
    'default': '70ede8cf-57ed-429f-8fc1-5d7032ecf17c'
  };

  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error('CARTESIA_API_KEY is required');
    }
    this.apiKey = apiKey;
  }

  /**
   * Generate speech from text using Cartesia Sonic
   */
  async textToSpeech(
    text: string,
    options: {
      voice?: string;
      speed?: number;
      format?: 'wav' | 'mp3';
      quality?: 'high' | 'medium';
    } = {}
  ): Promise<CartesiaTtsResult> {
    try {
      console.log('[CartesiaTTS] Generating speech for text:', text.substring(0, 100));

      // Validate and prepare voice
      const voiceId = this.VOICES[options.voice as keyof typeof this.VOICES] || this.VOICES.default;
      
      // Configure output format based on requirements
      const outputFormat: CartesiaOutputFormat = {
        container: 'wav', // Force WAV for fastest processing
        encoding: 'pcm_f32le',
        sample_rate: options.quality === 'medium' ? 22050 : 44100
      };

      const requestPayload: CartesiaTtsRequest = {
        transcript: text,
        model_id: 'sonic-turbo', // Latest and best model
        voice: {
          mode: 'id',
          id: voiceId
        },
        output_format: outputFormat,
        language: 'en' // Can be made configurable
        // Note: speed control is handled by Cartesia internally, not via API parameter
      };

             console.log('[CartesiaTTS] Request config:', {
         voice: options.voice || 'default',
         voiceId,
         format: outputFormat,
         modelId: requestPayload.model_id
       });

      const response = await fetch(`${this.baseUrl}/tts/bytes`, {
        method: 'POST',
        headers: {
          'Cartesia-Version': this.version,
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestPayload)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[CartesiaTTS] API Error:', response.status, errorText);
        throw new Error(`Cartesia API error ${response.status}: ${errorText}`);
      }

      // Get audio data as ArrayBuffer
      const audioBuffer = await response.arrayBuffer();
      const audioData = new Uint8Array(audioBuffer);

      console.log('[CartesiaTTS] ✅ Generated audio:', {
        size: `${(audioData.length / 1024).toFixed(1)}KB`,
        format: outputFormat.container,
        sampleRate: outputFormat.sample_rate
      });

      return {
        audioData,
        mimeType: outputFormat.container === 'mp3' ? 'audio/mpeg' : 'audio/wav'
      };

    } catch (error: any) {
      console.error('[CartesiaTTS] Error generating speech:', error);
      return {
        audioData: new Uint8Array(0),
        mimeType: 'audio/wav',
        error: error.message || String(error)
      };
    }
  }

  /**
   * Stream text-to-speech (for future implementation)
   */
  async streamTextToSpeech(text: string, options: any = {}): Promise<ReadableStream> {
    // TODO: Implement streaming TTS when Cartesia supports it
    throw new Error('Streaming TTS not yet implemented');
  }

  /**
   * Get available voices
   */
  getAvailableVoices(): Record<string, string> {
    return { ...this.VOICES };
  }

  /**
   * Create access token for client-side usage (security best practice)
   */
  async createAccessToken(expiresIn: number = 3600): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/auth/access_token`, {
        method: 'POST',
        headers: {
          'Cartesia-Version': this.version,
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          expires_in: expiresIn
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[CartesiaTTS] Access token error:', response.status, errorText);
        throw new Error(`Failed to create access token: ${errorText}`);
      }

      const data = await response.json();
      return data.access_token;

    } catch (error: any) {
      console.error('[CartesiaTTS] Error creating access token:', error);
      throw error;
    }
  }
} 