import { NluService } from './nlu.service';
import { EnhancedNluService } from './enhancedNlu.service';
import { SessionDO } from './durable-objects/session';
import { GraphMemoryDO } from './durable-objects/graphMemory';
import { AdminRoutes } from './api/adminRoutes';
import { RealtimeKitService } from './realtimekit';
import { getStatsSummary } from './stats';
import { CartesiaTtsService } from './cartesiaTts.service';

// CORS helper function
function corsHeaders(origin?: string) {
  return {
    'Access-Control-Allow-Origin': origin || '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
    'Access-Control-Max-Age': '86400',
  };
}

function jsonResponse(data: any, status = 200, origin?: string) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders(origin),
    },
  });
}

// Helper function to decode base64 to Uint8Array
function base64ToUint8Array(base64String: string): Uint8Array {
  const binaryString = atob(base64String);
  const bytes = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  return bytes;
}

/**
 * Strip markdown formatting from text for TTS to avoid pronouncing formatting characters
 */
function stripMarkdownForTTS(text: string): string {
  return text
    // Remove bold/italic asterisks and underscores
    .replace(/\*\*([^*]+)\*\*/g, '$1')  // **bold** -> bold
    .replace(/\*([^*]+)\*/g, '$1')      // *italic* -> italic
    .replace(/__([^_]+)__/g, '$1')      // __bold__ -> bold
    .replace(/_([^_]+)_/g, '$1')        // _italic_ -> italic
    // Remove strikethrough
    .replace(/~~([^~]+)~~/g, '$1')      // ~~strikethrough~~ -> strikethrough
    // Remove code formatting
    .replace(/`([^`]+)`/g, '$1')        // `code` -> code
    .replace(/```[\s\S]*?```/g, '')     // Remove code blocks entirely
    // Remove links but keep text
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')  // [text](url) -> text
    // Remove headers
    .replace(/^#{1,6}\s+/gm, '')        // # Header -> Header
    // Remove list markers
    .replace(/^\s*[-*+]\s+/gm, '')      // - item -> item
    .replace(/^\s*\d+\.\s+/gm, '')      // 1. item -> item
    // Remove blockquotes
    .replace(/^\s*>\s+/gm, '')          // > quote -> quote
    // Clean up extra whitespace
    .replace(/\s+/g, ' ')
    .trim();
}

// ---------------- Summary helper ----------------
async function summariseHistory(msgs: Array<{ role: string; content: string }>): Promise<string> {
  // Very lightweight heuristic summary (no external API cost)
  const userLines = msgs.filter(m => m.role === 'user').slice(-10).map(m => m.content.trim()).filter(Boolean);
  if (userLines.length === 0) return '';
  const bullets = Array.from(new Set(userLines)).map(l => `• ${l}`);
  return bullets.join('\n');
}

export default {
  async fetch(request: Request, env: any, ctx?: any): Promise<Response> {
    const url = new URL(request.url);
    const origin = request.headers.get('Origin') || undefined;

    // Handle preflight OPTIONS requests
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 204,
        headers: corsHeaders(origin),
      });
    }

    // Admin routes for memory and knowledge management
    if (url.pathname.startsWith('/api/memory/') || url.pathname.startsWith('/api/knowledge/')) {
      const adminRoutes = new AdminRoutes(env);
      return await adminRoutes.handleRequest({ request, env });
    }

    // Cartesia TTS endpoint for high-quality text-to-speech
    if (request.method === 'POST' && url.pathname === '/api/tts') {
      try {
        console.log('[TTS] Processing Cartesia TTS request...');
        
        const body = await request.json();
        const { text, voice = 'default', speed = 1.0, format = 'wav', quality = 'high' } = body;
        
        if (!text || typeof text !== 'string') {
          return jsonResponse({ error: 'Missing or invalid text parameter' }, 400, origin);
        }

        if (!env.CARTESIA_API_KEY) {
          return jsonResponse({ error: 'Cartesia API key not configured' }, 500, origin);
        }
        
        // Strip markdown formatting for better TTS pronunciation
        const cleanText = stripMarkdownForTTS(text);
        
        console.log('[TTS] Request details:');
        console.log('  - Original Text Length:', text.length);
        console.log('  - Cleaned Text Length:', cleanText.length);
        console.log('  - Voice:', voice);
        console.log('  - Speed:', speed);
        console.log('  - Format:', format);
        console.log('  - Quality:', quality);
        
        const ttsService = new CartesiaTtsService(env.CARTESIA_API_KEY);
        const result = await ttsService.textToSpeech(cleanText, {
          voice,
          speed,
          format,
          quality
        });
        
        if (result.error) {
          console.error('[TTS] Generation failed:', result.error);
          return jsonResponse({ error: result.error }, 500, origin);
        }
        
        console.log('[TTS] ✅ Successfully generated audio:', {
          size: `${(result.audioData.length / 1024).toFixed(1)}KB`,
          mimeType: result.mimeType
        });
        
        // Return audio as base64 for easy transport
        // Use proper binary-to-base64 encoding for Cloudflare Workers
        let base64Audio = '';
        
        try {
          // Use proper binary string approach that works in Workers
          let binaryString = '';
          const chunkSize = 0x8000; // 32KB chunks to avoid call stack overflow
          
          // Convert Uint8Array to binary string in chunks
          for (let i = 0; i < result.audioData.length; i += chunkSize) {
            const chunk = result.audioData.subarray(i, i + chunkSize);
            binaryString += String.fromCharCode(...chunk);
          }
          
          // Now encode the complete binary string to base64
          base64Audio = btoa(binaryString);
          
        } catch (encodingError) {
          console.error('[TTS] Base64 encoding error:', encodingError);
          throw new Error('Failed to encode audio data');
        }
        
        return jsonResponse({
          audioData: base64Audio,
          mimeType: result.mimeType,
          size: result.audioData.length
        }, 200, origin);
        
      } catch (error: any) {
        console.error('[TTS] Error processing request:', error);
        return jsonResponse({ 
          error: error.message || String(error) 
        }, 500, origin);
      }
    }

    // Get TTS access token for client-side usage
    if (request.method === 'POST' && url.pathname === '/api/tts/token') {
      try {
        if (!env.CARTESIA_API_KEY) {
          return jsonResponse({ error: 'Cartesia API key not configured' }, 500, origin);
        }
        
        const body = await request.json();
        const { expiresIn = 3600 } = body;
        
        const ttsService = new CartesiaTtsService(env.CARTESIA_API_KEY);
        const accessToken = await ttsService.createAccessToken(expiresIn);
        
        return jsonResponse({
          accessToken,
          expiresIn,
          baseUrl: 'https://api.cartesia.ai'
        }, 200, origin);
        
      } catch (error: any) {
        console.error('[TTS] Error creating access token:', error);
        return jsonResponse({ 
          error: error.message || String(error) 
        }, 500, origin);
      }
    }

    // Enhanced NLU endpoint with memory and knowledge integration
    if (request.method === 'POST' && url.pathname === '/nlu-enhanced') {
      try {
        console.log('[Enhanced-NLU] Processing enhanced NLU request with memory and knowledge...');
        
        const body = await request.json();
        const { 
          audio, 
          elements, 
          screenshot, 
          sampleRate = 16000, 
          channels = 1, 
          mimeType = 'audio/ogg', 
          sessionId,
          storeId,
          enableMemory = true,
          enableKnowledge = true,
          participantIdentity
        } = body;
        
        // Use provided sessionId, participantIdentity, or generate unique one
        const finalSessionId = sessionId || participantIdentity || `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        if (!audio) {
          return jsonResponse({ error: 'Missing audio data' }, 400, origin);
        }
        
        // Decode base64 audio
        const audioBuffer = base64ToUint8Array(audio);
        
        console.log('[Enhanced-NLU] Request details:');
        console.log('  - Audio Buffer Size:', audioBuffer.length);
        console.log('  - Elements Count:', elements?.length || 0);
        console.log('  - Has Screenshot:', !!screenshot);
        console.log('  - Store ID:', storeId);
        console.log('  - Session ID:', finalSessionId);
        console.log('  - Memory Enabled:', enableMemory);
        console.log('  - Knowledge Enabled:', enableKnowledge);
        
        // Get conversation history from session DO
        const id = env.SESSION_DO.idFromName(finalSessionId);
        const stub = env.SESSION_DO.get(id);
        let history: Array<{ role: string; content: string }> = [];
        let sessionSummary = '';
        try {
          const resp = await stub.fetch('https://do/session/state');
          if (resp.ok) {
            const data = await resp.json();
            history = data.conversation || [];
            sessionSummary = data.summary || '';
          }
        } catch (err) {
          console.warn('[Enhanced-NLU] Failed to load session history:', err);
        }

        // Prepend summary as system message if present
        const enhancedHistory = sessionSummary ? [{ role: 'system', content: sessionSummary }, ...history] : history;

        // Use enhanced NLU service
        const enhancedNlu = new EnhancedNluService(env.GEMINI_API_KEY, env);
        const result = await enhancedNlu.processAudio(
          audioBuffer,
          sampleRate,
          channels,
          {
            participantIdentity: finalSessionId,
            roomName: finalSessionId,
            elements,
            screenshot,
            storeId,
            enableMemory,
            enableKnowledge,
            waitUntil: (promise: Promise<any>) => {
              // CloudFlare Workers waitUntil to keep async operations alive
              if (ctx?.waitUntil) {
                ctx.waitUntil(promise);
              }
            }
          },
          mimeType,
          enhancedHistory
        );

        // Update conversation history
        const shouldStore = (msg: string) => {
          const lower = msg.toLowerCase();
          if (lower.includes("sorry") && (lower.includes("didn't catch") || lower.includes("didn't catch") || lower.includes("seem to have missed"))) {
            return false; // skip filler failure messages
          }
          return true;
        };

        const newMessages: Array<{ role: string; content: string }> = [];
        const userText = result.message || (result.toolCalls?.find((c: any) => c.query)?.query ?? '');
        if (userText && shouldStore(userText)) newMessages.push({ role: 'user', content: userText });
        for (const call of result.toolCalls || []) {
          if (call.content && shouldStore(call.content)) {
            newMessages.push({ role: 'assistant', content: call.content });
          }
        }

        if (newMessages.length > 0) {
          const updated = [...enhancedHistory, ...newMessages].slice(-50); // keep last 50 messages max
          await stub.fetch('https://do/session/state', {
            method: 'POST',
            body: JSON.stringify({ conversation: updated })
          });

          ctx?.waitUntil((async () => {
            const summary = await summariseHistory(updated);
            if (summary) {
              await stub.fetch('https://do/session/state', {
                method: 'POST',
                body: JSON.stringify({ summary })
              });
            }
          })());
        }
        
        console.log('[Enhanced-NLU] Processing result:');
        console.log('  - Message:', result.message);
        console.log('  - Tool Calls:', result.toolCalls?.length || 0);
        console.log('  - Context Used:', result.contextUsed);
        console.log('  - Error:', result.error);
        
        return jsonResponse(result, 200, origin);
      } catch (error: any) {
        console.error('[Enhanced-NLU] Error processing request:', error);
        return jsonResponse({ 
          text: '', 
          toolCalls: [], 
          error: error.message || String(error) 
        }, 500, origin);
      }
    }

    // Enhanced NLU endpoint with page context support for tool calling
    if (request.method === 'POST' && url.pathname === '/nlu-with-context') {
      try {
        console.log('[NLU-Context] Processing enhanced NLU request with tool support...');
        
        const body = await request.json();
        const { audio, elements, screenshot, sampleRate = 16000, channels = 1, mimeType = 'audio/ogg', sessionId, participantIdentity } = body;
        
        // Use provided sessionId, participantIdentity, or generate unique one
        const finalSessionId = sessionId || participantIdentity || `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        if (!audio) {
          return jsonResponse({ error: 'Missing audio data' }, 400, origin);
        }
        
        // Decode base64 audio
        const audioBuffer = base64ToUint8Array(audio);
        
        console.log('[NLU-Context] Request details:');
        console.log('  - Audio Buffer Size:', audioBuffer.length);
        console.log('  - Elements Count:', elements?.length || 0);
        console.log('  - Has Screenshot:', !!screenshot);
        console.log('  - Sample Rate:', sampleRate);
        console.log('  - Channels:', channels);
        console.log('  - Session ID:', finalSessionId);
        
        const id = env.SESSION_DO.idFromName(finalSessionId);
        const stub = env.SESSION_DO.get(id);
        let history: Array<{ role: string; content: string }> = [];
        let sessionSummary = '';
        try {
          const resp = await stub.fetch('https://do/session/state');
          if (resp.ok) {
            const data = await resp.json();
            history = data.conversation || [];
            sessionSummary = data.summary || '';
          }
        } catch (err) {
          console.warn('[NLU] Failed to load session history:', err);
        }

        // Prepend summary as system message if present
        const nluHistory = sessionSummary ? [{ role: 'system', content: sessionSummary }, ...history] : history;

        const nlu = new NluService(env.GEMINI_API_KEY, env);
        const result = await nlu.processAudio(
          audioBuffer,
          sampleRate,
          channels,
          {
            participantIdentity: finalSessionId,
            roomName: finalSessionId,
            elements,
            screenshot
          },
          mimeType,
          nluHistory
        );

        const shouldStore = (msg: string) => {
          const lower = msg.toLowerCase();
          if (lower.includes("sorry") && (lower.includes("didn't catch") || lower.includes("didn't catch") || lower.includes("seem to have missed"))) {
            return false; // skip filler failure messages
          }
          return true;
        };

        const newMessages: Array<{ role: string; content: string }> = [];
        const userText = result.text || (result.toolCalls?.find((c: any) => c.query)?.query ?? '');
        if (userText && shouldStore(userText)) newMessages.push({ role: 'user', content: userText });
        for (const call of result.toolCalls || []) {
          if (call.content && shouldStore(call.content)) {
            newMessages.push({ role: 'assistant', content: call.content });
          }
        }

        if (newMessages.length > 0) {
          const updated = [...nluHistory, ...newMessages].slice(-50); // keep last 50 messages max
          await stub.fetch('https://do/session/state', {
            method: 'POST',
            body: JSON.stringify({ conversation: updated })
          });

          ctx?.waitUntil((async () => {
            const summary = await summariseHistory(updated);
            if (summary) {
              await stub.fetch('https://do/session/state', {
                method: 'POST',
                body: JSON.stringify({ summary })
              });
            }
          })());
        }
        
        console.log('[NLU-Context] Processing result:');
        console.log('  - Text:', result.text);
        console.log('  - Tool Calls:', result.toolCalls?.length || 0);
        console.log('  - Error:', result.error);
        
        return jsonResponse(result, 200, origin);
      } catch (error: any) {
        console.error('[NLU-Context] Error processing request:', error);
        return jsonResponse({ 
          text: '', 
          toolCalls: [], 
          error: error.message || String(error) 
        }, 500, origin);
      }
    }

    // Original NLU endpoint (for backward compatibility)
    if (request.method === 'POST' && url.pathname === '/nlu') {
      const sampleRate = Number(url.searchParams.get('sampleRate') || 16000);
      const channels = Number(url.searchParams.get('channels') || 1);
      const audioBuffer = new Uint8Array(await request.arrayBuffer());
      const mimeType = request.headers.get('Content-Type') || 'application/octet-stream';
      
      console.log('[NLU Debug] Received audio request:');
      console.log('  - Sample Rate:', sampleRate);
      console.log('  - Channels:', channels);
      console.log('  - Audio Buffer Size:', audioBuffer.length);
      console.log('  - MIME Type:', mimeType);
      console.log('  - Origin:', origin);
      
      // Log first few bytes of audio data for debugging
      const firstBytes = Array.from(audioBuffer.slice(0, 20)).map(b => b.toString(16).padStart(2, '0')).join(' ');
      console.log('  - First 20 bytes (hex):', firstBytes);
      
      const sessionId = 'test';
      const id = env.SESSION_DO.idFromName(sessionId);
      const stub = env.SESSION_DO.get(id);
      let history: Array<{ role: string; content: string }> = [];
      let sessionSummary = '';
      try {
        const resp = await stub.fetch('https://do/session/state');
        if (resp.ok) {
          const data = await resp.json();
          history = data.conversation || [];
          sessionSummary = data.summary || '';
        }
      } catch (err) {
        console.warn('[NLU] Failed to load session history:', err);
      }

      // Prepend summary as system message if present
      const nluHistory = sessionSummary ? [{ role: 'system', content: sessionSummary }, ...history] : history;

      const nlu = new NluService(env.GEMINI_API_KEY, env);
      const result = await nlu.processAudio(
        audioBuffer,
        sampleRate,
        channels,
        { participantIdentity: 'test', roomName: sessionId },
        mimeType,
        nluHistory
      );

      const shouldStore = (msg: string) => {
        const lower = msg.toLowerCase();
        if (lower.includes("sorry") && (lower.includes("didn't catch") || lower.includes("didn't catch") || lower.includes("seem to have missed"))) {
          return false; // skip filler failure messages
        }
        return true;
      };

      const newMessages: Array<{ role: string; content: string }> = [];
        const userText = result.text || (result.toolCalls?.find((c: any) => c.query)?.query ?? '');
      if (userText && shouldStore(userText)) newMessages.push({ role: 'user', content: userText });
      for (const call of result.toolCalls || []) {
        if (call.content && shouldStore(call.content)) {
          newMessages.push({ role: 'assistant', content: call.content });
        }
      }

      if (newMessages.length > 0) {
        const updated = [...nluHistory, ...newMessages].slice(-50); // keep last 50 messages max
        await stub.fetch('https://do/session/state', {
          method: 'POST',
          body: JSON.stringify({ conversation: updated })
        });

        ctx?.waitUntil((async () => {
          const summary = await summariseHistory(updated);
          if (summary) {
            await stub.fetch('https://do/session/state', {
              method: 'POST',
              body: JSON.stringify({ summary })
            });
          }
        })());
      }
      
      console.log('[NLU Debug] Processing result:');
      console.log('  - Text:', result.text);
      console.log('  - Error:', result.error);
      console.log('  - Tool Calls:', result.toolCalls?.length || 0);
      
      return jsonResponse(result, 200, origin);
    }

    // RealtimeKit bot start endpoint
    if (request.method === 'POST' && url.pathname === '/realtimekit/bot/start') {
      try {
        const body = await request.json();
        const roomName = body.roomName || url.searchParams.get('roomName');
        const identity = body.identity || url.searchParams.get('identity') || 'aura-bot';
        const token = body.token || url.searchParams.get('token');
        if (!roomName || !token) {
          return jsonResponse({ error: 'Missing roomName or token' }, 400, origin);
        }
        const kit = new RealtimeKitService(env);
        // Start the bot (no-op callback for now)
        kit.joinRoomAsBot(roomName, identity, token, () => {});
        return jsonResponse({ status: 'started', roomName, identity }, 200, origin);
      } catch (err: any) {
        return jsonResponse({ error: err.message || String(err) }, 500, origin);
      }
    }

    // Durable Object session state endpoints
    // GET /session/:id/state
    const sessionGetMatch = url.pathname.match(/^\/session\/(.+)\/state$/);
    if (request.method === 'GET' && sessionGetMatch) {
      const sessionId = sessionGetMatch[1];
      const id = env.SESSION_DO.idFromName(sessionId);
      const stub = env.SESSION_DO.get(id);
      const response = await stub.fetch(new Request('https://do/session/state', { method: 'GET' }));
      const data = await response.json();
      return jsonResponse(data, response.status, origin);
    }

    // POST /session/:id/state
    const sessionPostMatch = url.pathname.match(/^\/session\/(.+)\/state$/);
    if (request.method === 'POST' && sessionPostMatch) {
      const sessionId = sessionPostMatch[1];
      const id = env.SESSION_DO.idFromName(sessionId);
      const stub = env.SESSION_DO.get(id);
      const response = await stub.fetch(new Request('https://do/session/state', { method: 'POST', body: request.body }));
      const data = await response.json();
      return jsonResponse(data, response.status, origin);
    }

    // Stats summary endpoint
    if (request.method === 'GET' && url.pathname === '/v1/stats/summary') {
      const store = url.searchParams.get('store');
      if (!store) {
        return jsonResponse({ error: 'Missing store' }, 400, origin);
      }
      const summary = await getStatsSummary(env.AURA_DB, store);
      return jsonResponse(summary, 200, origin);
    }

    // List available Gemini models
    if (request.method === 'GET' && url.pathname === '/models') {
      const nlu = new NluService(env.GEMINI_API_KEY, env);
      const models = await nlu['gemini'].listModels(env);
      return jsonResponse(models, 200, origin);
    }

    // Proxy GraphMemory routes to Durable Object
    if (url.pathname === '/recordInterest' || url.pathname === '/getRecommendations' || url.pathname === '/syncCatalog') {
      const gmId = env.GRAPH_MEMORY_DO.idFromName('global');
      const gmStub = env.GRAPH_MEMORY_DO.get(gmId);

      // Build internal DO URL (https://do/<path>?<query>)
      const internalUrl = `https://do${url.pathname}${url.search}`;
      const init: RequestInit = {
        method: request.method,
        headers: request.headers,
        body:
          request.method === 'GET' || request.method === 'HEAD'
            ? undefined
            : await request.clone().arrayBuffer(),
      };
      const proxyReq = new Request(internalUrl, init);
      return await gmStub.fetch(proxyReq);
    }

    return new Response('Not found', { 
      status: 404,
      headers: corsHeaders(origin),
    });
  }
};

export { SessionDO, GraphMemoryDO };
