// Shopify Tools for Function Calling Orchestration (Cloudflare Workers, TypeScript)
// April 2025 best practices - Now using comprehensive MCP service

import { createShopifyMcpService } from './shopifyMcp.service';

/**
 * Input for product search action.
 */
export interface ProductSearchInput {
  query: string;
  limit?: number;
}

/**
 * Input for cart update action.
 */
export interface CartUpdateInput {
  cartId: string;
  productId: string;
  quantity: number;
}

/**
 * Search products in the Shopify store using the comprehensive MCP service.
 */
export async function searchProducts(
  input: ProductSearchInput,
  env: any,
  customerAccessToken?: string
): Promise<any> {
  const mcpService = createShopifyMcpService(env, customerAccessToken);
  return await mcpService.searchProducts({
    query: input.query,
    limit: input.limit
  });
}

/**
 * Update cart using the comprehensive MCP service.
 */
export async function updateCart(
  input: CartUpdateInput,
  env: any,
  customerAccessToken?: string
): Promise<any> {
  const mcpService = createShopifyMcpService(env, customerAccessToken);
  return await mcpService.updateCart({
    cartId: input.cartId,
    lines: [{
      merchandiseId: input.productId,
      quantity: input.quantity
    }]
  });
}

/**
 * Get cart contents using the comprehensive MCP service.
 */
export async function getCart(
  cartId: string,
  env: any,
  customerAccessToken?: string
): Promise<any> {
  const mcpService = createShopifyMcpService(env, customerAccessToken);
  return await mcpService.getCart(cartId);
}

/**
 * Search store policies and FAQs using the comprehensive MCP service.
 */
export async function searchPoliciesAndFaqs(
  query: string,
  env: any,
  context?: string
): Promise<any> {
  const mcpService = createShopifyMcpService(env);
  return await mcpService.searchPoliciesAndFaqs({
    query,
    context
  });
}

/**
 * Get customer orders using the comprehensive MCP service (requires authentication).
 */
export async function getCustomerOrders(
  env: any,
  customerAccessToken: string,
  options: { orderId?: string; orderNumber?: string; limit?: number } = {}
): Promise<any> {
  const mcpService = createShopifyMcpService(env, customerAccessToken);
  return await mcpService.getCustomerOrders(options);
}

/**
 * Get specific order details using the comprehensive MCP service (requires authentication).
 */
export async function getOrderDetails(
  orderIdOrNumber: string,
  env: any,
  customerAccessToken: string
): Promise<any> {
  const mcpService = createShopifyMcpService(env, customerAccessToken);
  return await mcpService.getOrderDetails(orderIdOrNumber);
}

/**
 * Get customer account details using the comprehensive MCP service (requires authentication).
 */
export async function getCustomerAccount(
  env: any,
  customerAccessToken: string
): Promise<any> {
  const mcpService = createShopifyMcpService(env, customerAccessToken);
  return await mcpService.getCustomerAccount();
}

/**
 * Return OpenAPI-style tool definitions for use with @cloudflare/ai-utils.
 */
export function getShopifyToolDefinitions() {
  return [
    {
      name: 'searchProducts',
      description: 'Search for products in the Shopify store.',
      parameters: {
        type: 'object',
        properties: {
          query: { type: 'string', description: 'Search query' },
          limit: { type: 'number', description: 'Maximum number of results', minimum: 1, maximum: 50 }
        },
        required: ['query']
      }
    },
    {
      name: 'updateCart',
      description: 'Update the quantity of a product in the cart.',
      parameters: {
        type: 'object',
        properties: {
          cartId: { type: 'string', description: 'Cart ID' },
          productId: { type: 'string', description: 'Product ID' },
          quantity: { type: 'number', description: 'Quantity to set' }
        },
        required: ['cartId', 'productId', 'quantity']
      }
    }
  ];
}

// The updateCart and getShopifyToolDefinitions functions are not currently used with the MCP flow,
// but are kept here for potential future use or different authentication models.
