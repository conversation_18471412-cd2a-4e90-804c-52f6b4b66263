// Admin API Routes for Memory and Knowledge Management
// Provides HTTP endpoints for managing graph memory and knowledge base

import { KnowledgeRetrievalService } from '../services/knowledgeRetrieval';
import { ContextHydrationService } from '../services/contextHydration';

interface RouteContext {
  request: Request;
  env: any;
  params?: Record<string, string>;
}

export class AdminRoutes {
  private knowledgeService: KnowledgeRetrievalService;
  private contextService: ContextHydrationService;

  constructor(env: any) {
    this.knowledgeService = new KnowledgeRetrievalService(env);
    this.contextService = new ContextHydrationService(env);
  }

  /**
   * Route handler for all admin endpoints
   */
  async handleRequest({ request, env, params }: RouteContext): Promise<Response> {
    const url = new URL(request.url);
    const corsHeaders = this.getCorsHeaders();

    if (request.method === 'OPTIONS') {
      return new Response(null, { status: 204, headers: corsHeaders });
    }

    try {
      // Route to appropriate handler
      if (url.pathname.startsWith('/api/memory/')) {
        return await this.handleMemoryRoutes(request, url, corsHeaders);
      }
      
      if (url.pathname.startsWith('/api/knowledge/')) {
        return await this.handleKnowledgeRoutes(request, url, corsHeaders);
      }

      return this.jsonResponse({ error: 'Not found' }, 404, corsHeaders);

    } catch (error: any) {
      console.error('[AdminRoutes] Error:', error);
      return this.jsonResponse({ error: error.message }, 500, corsHeaders);
    }
  }

  /**
   * Handle memory-related API routes
   */
  private async handleMemoryRoutes(request: Request, url: URL, corsHeaders: Record<string, string>): Promise<Response> {
    
    // POST /api/memory/fact - Add a memory fact
    if (request.method === 'POST' && url.pathname === '/api/memory/fact') {
      const { userId, factType, value, metadata } = await request.json();
      
      if (!userId || !factType || !value) {
        return this.jsonResponse({ error: 'Missing required fields: userId, factType, value' }, 400, corsHeaders);
      }

      const result = await this.contextService.addMemoryFact(userId, factType, value, metadata);
      return this.jsonResponse(result, result.success ? 200 : 400, corsHeaders);
    }

    // GET /api/memory/facts/:userId - Get user memory facts
    if (request.method === 'GET' && url.pathname.startsWith('/api/memory/facts/')) {
      const userId = url.pathname.split('/').pop();
      const query = url.searchParams.get('query');
      
      if (!userId) {
        return this.jsonResponse({ error: 'Missing userId' }, 400, corsHeaders);
      }

      // Get memory facts via GraphMemoryDO
      const id = this.contextService['env'].GRAPH_MEMORY_DO.idFromName(userId);
      const stub = this.contextService['env'].GRAPH_MEMORY_DO.get(id);
      
      const response = await stub.fetch(new Request(`https://do/queryFacts?userId=${userId}&query=${query || ''}`));
      const data = await response.json();
      
      return this.jsonResponse(data, 200, corsHeaders);
    }

    // GET /api/memory/profile/:userId - Get user profile
    if (request.method === 'GET' && url.pathname.startsWith('/api/memory/profile/')) {
      const userId = url.pathname.split('/').pop();
      
      if (!userId) {
        return this.jsonResponse({ error: 'Missing userId' }, 400, corsHeaders);
      }

      const profile = await this.contextService.getUserProfile(userId);
      return this.jsonResponse(profile, 200, corsHeaders);
    }

    // POST /api/memory/extract-facts - Extract facts from user message
    if (request.method === 'POST' && url.pathname === '/api/memory/extract-facts') {
      const { userId, userMessage } = await request.json();
      
      if (!userId || !userMessage) {
        return this.jsonResponse({ error: 'userId and userMessage required' }, 400, corsHeaders);
      }

      try {
        await this.contextService.extractAndStoreFacts(userId, userMessage);
        return this.jsonResponse({ success: true, message: 'Facts extracted and stored' }, 200, corsHeaders);
      } catch (error: any) {
        return this.jsonResponse({ error: error.message }, 500, corsHeaders);
      }
    }

    // POST /api/memory/initialize - Initialize Neo4j schema
    if (request.method === 'POST' && url.pathname === '/api/memory/initialize') {
      // This would require direct access to GraphMemoryDO initialization
      // For now, return a placeholder response
      return this.jsonResponse({ 
        success: true, 
        message: 'Schema initialization should be done during deployment' 
      }, 200, corsHeaders);
    }

    return this.jsonResponse({ error: 'Memory route not found' }, 404, corsHeaders);
  }

  /**
   * Handle knowledge-related API routes
   */
  private async handleKnowledgeRoutes(request: Request, url: URL, corsHeaders: Record<string, string>): Promise<Response> {
    
    // POST /api/knowledge/upload - Upload document
    if (request.method === 'POST' && url.pathname === '/api/knowledge/upload') {
      const storeId = url.searchParams.get('storeId');
      
      if (!storeId) {
        return this.jsonResponse({ error: 'Missing storeId parameter' }, 400, corsHeaders);
      }

      try {
        const contentType = request.headers.get('content-type') || '';
        
        if (contentType.includes('multipart/form-data')) {
          // Handle multipart form data for file uploads
          const formData = await request.formData();
          const file = formData.get('file') as File;
          const filename = formData.get('filename') as string || undefined;
          
          if (!file) {
            return this.jsonResponse({ error: 'No file provided' }, 400, corsHeaders);
          }

          const result = await this.knowledgeService.uploadDocument(file, storeId, filename);
          return this.jsonResponse(result, result.success ? 200 : 400, corsHeaders);
        } else {
          return this.jsonResponse({ error: 'Content-Type must be multipart/form-data' }, 400, corsHeaders);
        }
      } catch (error: any) {
        return this.jsonResponse({ error: error.message }, 500, corsHeaders);
      }
    }

    // GET /api/knowledge/files/:storeId - List files
    if (request.method === 'GET' && url.pathname.startsWith('/api/knowledge/files/')) {
      const storeId = url.pathname.split('/').pop();
      
      if (!storeId) {
        return this.jsonResponse({ error: 'Missing storeId' }, 400, corsHeaders);
      }

      const result = await this.knowledgeService.listDocuments(storeId);
      return this.jsonResponse(result, 200, corsHeaders);
    }

    // DELETE /api/knowledge/file - Delete file
    if (request.method === 'DELETE' && url.pathname === '/api/knowledge/file') {
      const { key } = await request.json();
      
      if (!key) {
        return this.jsonResponse({ error: 'Missing file key' }, 400, corsHeaders);
      }

      const result = await this.knowledgeService.deleteDocument(key);
      return this.jsonResponse(result, result.success ? 200 : 400, corsHeaders);
    }

    // POST /api/knowledge/sync - Sync Shopify content
    if (request.method === 'POST' && url.pathname === '/api/knowledge/sync') {
      const { storeId, shopifyDomain, accessToken } = await request.json();
      
      if (!storeId || !shopifyDomain || !accessToken) {
        return this.jsonResponse({ 
          error: 'Missing required fields: storeId, shopifyDomain, accessToken' 
        }, 400, corsHeaders);
      }

      const result = await this.knowledgeService.syncShopifyContent(storeId, shopifyDomain, accessToken);
      return this.jsonResponse(result, result.success ? 200 : 400, corsHeaders);
    }

    // POST /api/knowledge/search - Search knowledge base
    if (request.method === 'POST' && url.pathname === '/api/knowledge/search') {
      const { query, storeId, maxResults } = await request.json();
      
      if (!query) {
        return this.jsonResponse({ error: 'Missing query' }, 400, corsHeaders);
      }

      const chunks = await this.knowledgeService.searchKnowledge({
        query,
        storeId,
        maxResults: maxResults || 5
      });

      return this.jsonResponse({ chunks }, 200, corsHeaders);
    }

    return this.jsonResponse({ error: 'Knowledge route not found' }, 404, corsHeaders);
  }

  /**
   * Helper to create JSON responses
   */
  private jsonResponse(data: any, status: number = 200, additionalHeaders: Record<string, string> = {}): Response {
    return new Response(JSON.stringify(data), {
      status,
      headers: {
        'Content-Type': 'application/json',
        ...additionalHeaders
      }
    });
  }

  /**
   * Get CORS headers
   */
  private getCorsHeaders(): Record<string, string> {
    return {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With'
    };
  }
}