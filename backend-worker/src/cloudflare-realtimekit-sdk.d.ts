declare module 'cloudflare-realtimekit-sdk' {
  export interface RealtimeKitClientOptions {
    url: string;
    token: string;
    identity?: string;
  }
  export class RealtimeKitClient {
    constructor(options: RealtimeKitClientOptions);
    connect(room: string): Promise<void>;
    disconnect(): Promise<void>;
    on(event: string, listener: (...args: any[]) => void): void;
    sendDataMessage(room: string, data: string | Uint8Array): Promise<void>;
  }
  export enum RoomEvent {
    AudioTrackSubscribed = 'AudioTrackSubscribed',
    DataMessageReceived = 'DataMessageReceived',
  }
  export interface AudioTrack {
    on(event: string, listener: (...args: any[]) => void): void;
  }
  export interface DataMessage {
    data: Uint8Array;
  }
}
