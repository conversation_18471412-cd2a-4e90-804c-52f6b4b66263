// Enhanced NLU Service with Memory and Knowledge Integration
// Integrates context hydration for personalized and informed responses

import { GeminiClient, GeminiAudioResult } from './gemini';
import { 
  searchProducts, 
  updateCart, 
  getCart,
  searchPoliciesAndFaqs,
  getCustomerOrders,
  getOrderDetails,
  getCustomerAccount
} from './shopify-tools';
import { ContextHydrationService, ConversationMessage } from './services/contextHydration';
import { MemoryCacheService } from './services/memoryCacheService';

export interface ProductSuggestion {
  handle: string;
  title?: string;
  type?: 'product' | 'collection';
  score?: number;
}

export interface EnhancedNluResult {
  message?: string;
  cards: CardData[];
  toolCalls?: any[]; // Maintain backward compatibility
  productSuggestions?: ProductSuggestion[];
  products?: any[]; // Maintain backward compatibility
  error?: string;
  contextUsed?: {
    memoryFactsCount: number;
    knowledgeChunksCount: number;
  };
  metadata?: {
    toolCallsExecuted?: string[];
    responseTime?: number;
  };
}

export interface CardData {
  type: string;
  id?: string;
  title?: string;
  subtitle?: string;
  data: any;
  layout: string;
  actions?: Array<{
    type: string;
    label: string;
    action: string;
    data?: any;
    style?: string;
  }>;
  style?: any;
  priority?: number;
}

export interface EnhancedNluContext extends NluContext {
  storeId?: string;
  enableMemory?: boolean;
  enableKnowledge?: boolean;
  waitUntil?: (promise: Promise<any>) => void;
  accessToken?: string; // Customer access token for authenticated Shopify MCP calls
  shopCatalog?: {
    products: Array<{ handle: string; title: string }>;
    collections: Array<{ handle: string; title: string }>;
  };
}

export interface NluContext {
  participantIdentity: string;
  roomName: string;
  elements?: any[];
  screenshot?: string;
}

export class EnhancedNluService {
  private gemini: GeminiClient;
  private env: any;
  private contextHydration: ContextHydrationService;
  private memoryCache: MemoryCacheService;

  constructor(geminiApiKey: string, env: any) {
    if (!geminiApiKey) {
      throw new Error('GEMINI_API_KEY is required');
    }
    this.gemini = new GeminiClient(geminiApiKey);
    this.env = env;
    this.contextHydration = new ContextHydrationService(env);
    this.memoryCache = new MemoryCacheService(env);
  }

  async processAudio(
    audioBuffer: Uint8Array,
    sampleRate: number,
    channels: number,
    context: EnhancedNluContext,
    mimeType: string = 'audio/wav',
    history: Array<{ role: string; content: string }> = []
  ): Promise<EnhancedNluResult> {
    try {
      let audioBytes = audioBuffer;
      let finalMimeType = mimeType;

      // Handle PCM encoding if needed
      if (mimeType === 'audio/pcm' || mimeType === 'application/octet-stream') {
        audioBytes = this.encodeWav(audioBuffer, sampleRate, channels);
        finalMimeType = 'audio/wav';
      }

      // Prepare page context for Gemini if available
      const pageContext = context.elements || context.screenshot ? {
        elements: context.elements,
        screenshot: context.screenshot
      } : undefined;

      // Extract userId from context for memory operations
      const userId = context.participantIdentity || context.roomName || 'anonymous';

      // Pre-load user context for personalized responses
      let hydratedContext;
      if (context.enableMemory !== false || context.enableKnowledge !== false) {
        console.log('[EnhancedNLU] Pre-loading user context...');
        hydratedContext = await this.contextHydration.gatherContext({
          userId,
          userQuery: '', // We don't have the query yet, but we can get user profile
          storeId: context.storeId,
          includeMemory: context.enableMemory !== false,
          includeKnowledge: false, // Don't load knowledge without query
          maxMemoryFacts: 5,
          maxKnowledgeChunks: 0
        });
      }

      // Get the multimodal response from Gemini (transcription + response + tools)
      console.log('[EnhancedNLU] Getting multimodal response...');
      const geminiResult: GeminiAudioResult = await this.gemini.audioToText(
        audioBytes,
        finalMimeType,
        this.env,
        pageContext,
        history,
        hydratedContext?.memoryFacts || [], // Pass user context to Gemini
        context.shopCatalog
      );

      // Check if we need to try denoising
      const failed = !geminiResult.toolCalls?.length && 
        /i (?:am )?(?:sorry|an ai).*cannot|don\'t have access|cannot process audio/i.test(geminiResult.text || '');

      let finalResult = geminiResult;
      if (failed && this.env.AI) {
        console.log('[EnhancedNLU] Attempting audio denoising...');
        try {
          const denoised = await this.denoiseAudio(audioBytes, sampleRate);
          if (denoised) {
            finalResult = await this.gemini.audioToText(
              denoised,
              'audio/wav',
              this.env,
              pageContext,
              history,
              hydratedContext?.memoryFacts || []
            );
          }
        } catch (denoiseErr) {
          console.error('[EnhancedNLU] Audio denoising error:', denoiseErr);
        }
      }

      const responseText = finalResult.text || '';
      console.log('[EnhancedNLU] Gemini response:', responseText);

              // ASYNC: Extract facts from user transcription in background (don't block response)
        if (context.enableMemory !== false && finalResult.transcription) {
          console.log('[EnhancedNLU] Triggering async fact extraction for user:', userId);
          console.log('[EnhancedNLU] User transcription:', finalResult.transcription);
          
          // Use waitUntil to keep the async operation alive after response is sent
          const factExtractionPromise = this.contextHydration.extractAndStoreFacts(userId, finalResult.transcription).catch((err: any) => {
            console.error('[EnhancedNLU] Async fact extraction failed:', err);
          });
          
          // Keep the promise alive with waitUntil if available
          if (context.waitUntil) {
            context.waitUntil(factExtractionPromise);
          }
        } else if (context.enableMemory !== false) {
          console.log('[EnhancedNLU] No transcription available, skipping fact extraction');
        } else {
          console.log('[EnhancedNLU] Memory disabled, skipping fact extraction');
        }

      // Process tool calls if any
      let toolCalls = finalResult.toolCalls || [];
      let enhancedResponseText = responseText;
      if (toolCalls.length > 0) {
        console.log('[EnhancedNLU] Processing tool calls:', toolCalls);
        const processedCalls = await this.processToolCalls(toolCalls, context);
        toolCalls = processedCalls;

        // Check if we have meaningful tool results that warrant a follow-up response
        const hasProductResults = processedCalls.some(call => 
          call.tool === 'searchProducts' && 
          call.products && 
          call.products.length > 0 && 
          !call.error
        );

        const hasResearchResults = processedCalls.some(call => 
          call.tool === 'webResearch' && 
          call.content && 
          !call.error
        );

        // If we have meaningful results, generate an enhanced response
        if (hasProductResults || hasResearchResults) {
          console.log('[EnhancedNLU] Generating enhanced response with tool results...');
          try {
            const followUpResponse = await this.gemini.generateFollowUpResponse(
              finalResult.transcription || '',
              processedCalls,
              hydratedContext?.memoryFacts || []
            );
            
            if (followUpResponse.text && !followUpResponse.error) {
              enhancedResponseText = followUpResponse.text;
              console.log('[EnhancedNLU] Enhanced response generated:', enhancedResponseText);
            } else {
              console.warn('[EnhancedNLU] Follow-up response failed, using fallback:', followUpResponse.error);
              // Generate a simple fallback response from product results
              const productCall = processedCalls.find(call => call.tool === 'searchProducts' && call.products?.length > 0);
              if (productCall && productCall.products.length > 0) {
                const product = productCall.products[0];
                if (product.price_range?.min) {
                  enhancedResponseText = `The ${product.title || 'product'} is priced from ${product.price_range.min} ${product.price_range.currency || 'AUD'}.`;
                } else if (product.price) {
                  enhancedResponseText = `The ${product.title || 'product'} is priced at ${product.price}.`;
                } else {
                  enhancedResponseText = `I found the ${product.title || 'product'} you're looking for.`;
                }
                console.log('[EnhancedNLU] Using fallback product response:', enhancedResponseText);
              }
            }
          } catch (followUpError) {
            console.error('[EnhancedNLU] Follow-up response error:', followUpError);
            // Generate fallback response from product results
            const productCall = processedCalls.find(call => call.tool === 'searchProducts' && call.products?.length > 0);
            if (productCall && productCall.products.length > 0) {
              const product = productCall.products[0];
              if (product.price_range?.min) {
                enhancedResponseText = `The ${product.title || 'product'} is priced from ${product.price_range.min} ${product.price_range.currency || 'AUD'}.`;
              } else if (product.price) {
                enhancedResponseText = `The ${product.title || 'product'} is priced at ${product.price}.`;
              } else {
                enhancedResponseText = `I found the ${product.title || 'product'} you're looking for.`;
              }
              console.log('[EnhancedNLU] Using fallback product response after error:', enhancedResponseText);
            }
          }
        }
      }

      // ===== SMART PRODUCT CARD DETECTION =====
      // If response mentions pricing/products but no tool calls were made,
      // trigger a background search to get product cards for better UX
      let backgroundProducts: any[] = [];
      
      if (toolCalls.length === 0 && this.containsProductInfo(enhancedResponseText)) {
        console.log('[EnhancedNLU] Detected product response without tool calls, triggering background search...');
        try {
          const productQuery = this.extractProductQuery(finalResult.transcription || '', enhancedResponseText);
          if (productQuery) {
            const backgroundSearch = await this.processToolCalls([{
              tool: 'searchProducts',
              args: { query: productQuery }
            }], context);
            
            if (backgroundSearch[0]?.products?.length > 0) {
              backgroundProducts = backgroundSearch[0].products;
              console.log('[EnhancedNLU] Background search found products:', backgroundProducts.length);
            }
          }
        } catch (bgErr) {
          console.warn('[EnhancedNLU] Background product search failed:', bgErr);
        }
      }

      // ===== Fetch personalised recommendations from GraphMemory =====
      let productSuggestions: ProductSuggestion[] | undefined;
      try {
        productSuggestions = await this.fetchRecommendations(userId, 5);
      } catch (recErr) {
        console.warn('[EnhancedNLU] Recommendation fetch failed:', recErr);
      }

      // Generate cards from tool calls and background products
      const cards = this.generateCardsFromResults(toolCalls, backgroundProducts, enhancedResponseText);

      return {
        message: enhancedResponseText,
        cards,
        toolCalls,
        products: backgroundProducts.length > 0 ? backgroundProducts : undefined,
        productSuggestions,
        error: finalResult.error,
        contextUsed: hydratedContext ? {
          memoryFactsCount: hydratedContext.memoryFacts.length,
          knowledgeChunksCount: hydratedContext.knowledgeChunks.length
        } : undefined,
        metadata: {
          toolCallsExecuted: toolCalls.map(call => call.tool)
        }
      };

    } catch (error: any) {
      console.error('[EnhancedNLU] Processing error:', error);
      return { 
        message: '', 
        cards: [],
        toolCalls: [], 
        error: error.message || String(error) 
      };
    }
  }

  /**
   * Process tool calls from Gemini and execute them via appropriate workers
   */
  private async processToolCalls(toolCalls: any[], context: EnhancedNluContext): Promise<any[]> {
    const processedCalls: any[] = [];

    for (const call of toolCalls) {
      try {
        console.log('[EnhancedNLU] Processing tool call:', call.tool, call.args);
        
        switch (call.tool) {
          case 'browserAutomation':
            const automationResult = await this.callMagician(
              call.args.goal, 
              context.elements, 
              context.screenshot
            );
            processedCalls.push({
              tool: 'browserAutomation',
              steps: automationResult.steps || [],
              error: automationResult.error
            });
            break;

          case 'webResearch':
            const researchResult = await this.callResearch(call.args.query);
            processedCalls.push({
              tool: 'webResearch',
              query: call.args.query,
              content: researchResult.content || '',
              error: researchResult.error
            });
            break;

          case 'searchProducts': {
            const searchResult = await searchProducts(
              { query: call.args.query, limit: call.args.limit },
              this.env
            );
            processedCalls.push({
              tool: 'searchProducts',
              products: searchResult.products || [],
              error: searchResult.error,
              debug: searchResult.debug
            });
            break;
          }

          case 'updateCart': {
            const cartResult = await updateCart(
              {
                cartId: call.args.cartId,
                productId: call.args.productId,
                quantity: call.args.quantity
              },
              this.env,
              context.accessToken // Pass customer access token if available
            );
            processedCalls.push({
              tool: 'updateCart',
              cart: cartResult,
              error: cartResult.error
            });
            break;
          }

          case 'getCart': {
            const cartResult = await getCart(
              call.args.cartId,
              this.env,
              context.accessToken
            );
            processedCalls.push({
              tool: 'getCart',
              cart: cartResult,
              error: cartResult.error
            });
            break;
          }

          case 'searchPoliciesAndFaqs': {
            const policyResult = await searchPoliciesAndFaqs(
              call.args.query,
              this.env,
              call.args.context
            );
            processedCalls.push({
              tool: 'searchPoliciesAndFaqs',
              policies: policyResult.policies || [],
              faqs: policyResult.faqs || [],
              error: policyResult.error
            });
            break;
          }

          case 'getCustomerOrders': {
            if (!context.accessToken) {
              processedCalls.push({
                tool: 'getCustomerOrders',
                error: 'Customer authentication required',
                requiresAuth: true
              });
              break;
            }

            const ordersResult = await getCustomerOrders(
              this.env,
              context.accessToken,
              {
                orderId: call.args.orderId,
                orderNumber: call.args.orderNumber,
                limit: call.args.limit || 10
              }
            );
            processedCalls.push({
              tool: 'getCustomerOrders',
              orders: ordersResult.orders || [],
              error: ordersResult.error,
              requiresAuth: ordersResult.requiresAuth
            });
            break;
          }

          case 'getOrderDetails': {
            if (!context.accessToken) {
              processedCalls.push({
                tool: 'getOrderDetails',
                error: 'Customer authentication required',
                requiresAuth: true
              });
              break;
            }

            const orderResult = await getOrderDetails(
              call.args.orderIdOrNumber,
              this.env,
              context.accessToken
            );
            processedCalls.push({
              tool: 'getOrderDetails',
              order: orderResult.order || null,
              error: orderResult.error,
              requiresAuth: orderResult.requiresAuth
            });
            break;
          }

          case 'getCustomerAccount': {
            if (!context.accessToken) {
              processedCalls.push({
                tool: 'getCustomerAccount',
                error: 'Customer authentication required',
                requiresAuth: true
              });
              break;
            }

            const accountResult = await getCustomerAccount(
              this.env,
              context.accessToken
            );
            processedCalls.push({
              tool: 'getCustomerAccount',
              account: accountResult.account || null,
              error: accountResult.error,
              requiresAuth: accountResult.requiresAuth
            });
            break;
          }

          case 'rememberFact': {
            // Custom tool for storing memory facts
            const userId = context.participantIdentity || context.roomName || 'anonymous';
            const result = await this.contextHydration.addMemoryFact(
              userId,
              call.args.factType,
              call.args.value,
              call.args.metadata
            );
            processedCalls.push({
              tool: 'rememberFact',
              success: result.success,
              error: result.error
            });
            break;
          }

          default:
            console.warn('[EnhancedNLU] Unknown tool:', call.tool);
            processedCalls.push({
              tool: call.tool,
              error: `Unknown tool: ${call.tool}`
            });
        }
      } catch (error: any) {
        console.error('[EnhancedNLU] Error processing tool call:', error);
        processedCalls.push({
          tool: call.tool,
          error: error.message || String(error)
        });
      }
    }

    return processedCalls;
  }

  /**
   * Call the Magician worker for browser automation using Service Bindings
   */
  private async callMagician(goal: string, elements?: any[], screenshot?: string | any): Promise<any> {
    try {
      if (!elements || elements.length === 0) {
        return { error: 'No page context available for automation' };
      }

      // Create screenshot object - same logic as original
      let screenshotObj;
      if (screenshot && typeof screenshot === 'object' && screenshot.width && screenshot.height && screenshot.data) {
        if (screenshot.format === 'png' && typeof screenshot.data === 'string') {
          screenshotObj = {
            width: screenshot.width,
            height: screenshot.height,
            data: `data:image/png;base64,${screenshot.data}`,
            format: 'png'
          };
        } else if (Array.isArray(screenshot.data)) {
          screenshotObj = {
            width: screenshot.width,
            height: screenshot.height,
            data: screenshot.data
          };
        } else {
          const width = 50, height = 50;
          const pixelCount = width * height * 4;
          const whitePixelData = new Array(pixelCount).fill(255);
          screenshotObj = { width, height, data: whitePixelData };
        }
      } else {
        const width = 50, height = 50;
        const pixelCount = width * height * 4;
        const whitePixelData = new Array(pixelCount).fill(255);
        screenshotObj = { width, height, data: whitePixelData };
      }

      // Format elements
      const formattedElements = elements.map((el, index) => ({
        localId: el.localId || index,
        type: el.type || '',
        text: el.text || '',
        tag: el.tag || '',
        placeholder: el.placeholder || '',
        value: el.value || '',
        attributes: {
          type: el.type || '',
          class: el.className || '',
          style: { position: 'static', display: 'block', visibility: 'visible' }
        },
        bounds: { x: 0, y: 0, width: 100, height: 30 }
      }));

      const payload = {
        goal,
        elements: formattedElements,
        screenshot: screenshotObj,
        html: ''
      };

      const request = new Request('https://automation-worker/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const response = await this.env.AUTOMATION_WORKER.fetch(request);

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `Automation Worker request failed: ${response.status} - ${errorText}` };
      }

      const result = await response.json();
      return { steps: result.steps || [], error: result.error };

    } catch (error: any) {
      console.error('[EnhancedNLU] Automation Worker call error:', error);
      return { error: error.message || String(error) };
    }
  }

  /**
   * Call the Research worker for web queries using Service Bindings
   */
  private async callResearch(query: string): Promise<any> {
    try {
      const payload = { query };
      const request = new Request('https://research-worker/', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload)
      });

      const response = await this.env.RESEARCH_WORKER.fetch(request);

      if (!response.ok) {
        const errorText = await response.text();
        return { error: `Research Worker request failed: ${response.status} - ${errorText}` };
      }

      const result = await response.json();
      return {
        content: result.content || result.answer || result.result || '',
        error: result.error
      };
    } catch (error: any) {
      console.error('[EnhancedNLU] Research Worker call error:', error);
      return { error: error.message || String(error) };
    }
  }

  private encodeWav(pcm: Uint8Array, sampleRate: number, channels: number): Uint8Array {
    const bitsPerSample = 16;
    const byteRate = sampleRate * channels * (bitsPerSample / 8);
    const blockAlign = channels * (bitsPerSample / 8);
    const dataSize = pcm.length;
    const buffer = new Uint8Array(44 + dataSize);
    const view = new DataView(buffer.buffer);
    
    this.writeString(view, 0, 'RIFF');
    view.setUint32(4, 36 + dataSize, true);
    this.writeString(view, 8, 'WAVE');
    this.writeString(view, 12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, channels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, byteRate, true);
    view.setUint16(32, blockAlign, true);
    view.setUint16(34, bitsPerSample, true);
    this.writeString(view, 36, 'data');
    view.setUint32(40, dataSize, true);
    buffer.set(pcm, 44);
    return buffer;
  }

  private writeString(view: DataView, offset: number, str: string) {
    for (let i = 0; i < str.length; i++) {
      view.setUint8(offset + i, str.charCodeAt(i));
    }
  }

  private async denoiseAudio(wavBytes: Uint8Array, sampleRate: number): Promise<Uint8Array | null> {
    if (!this.env.AI || typeof this.env.AI.run !== 'function') {
      console.warn('[EnhancedNLU] Workers AI binding not available');
      return null;
    }
    try {
      const b64 = btoa(String.fromCharCode(...wavBytes));
      const aiInput = {
        audio: { data: b64, format: 'wav', sample_rate: sampleRate }
      };
      const response = await this.env.AI.run('@cf/edge-ai/noise-suppressor-v1', aiInput);
      if (response?.audio?.data) {
        const bin = atob(response.audio.data);
        const arr = new Uint8Array(bin.length);
        for (let i = 0; i < bin.length; i++) arr[i] = bin.charCodeAt(i);
        return arr;
      }
      return null;
    } catch (err: any) {
      console.error('[EnhancedNLU] Audio denoising error:', err);
      return null;
    }
  }

  /**
   * Async method to extract user speech and facts from audio
   * This runs in background to not block the main response
   */
  private async extractUserSpeechAsync(
    audioBytes: Uint8Array, 
    mimeType: string, 
    userId: string, 
    sampleRate: number
  ): Promise<void> {
    try {
      console.log('[EnhancedNLU] Starting async user speech extraction for:', userId);
      console.log('[EnhancedNLU] Audio details:', { 
        size: audioBytes.length, 
        mimeType, 
        sampleRate,
        hasGeminiApiKey: !!this.env.GEMINI_API_KEY 
      });
      
      // Create a new GeminiClient instance for transcription-only
      const transcriptionClient = new (this.gemini.constructor as any)(this.env.GEMINI_API_KEY);
      console.log('[EnhancedNLU] Created transcription client');
      
      // Use the same audioToText method but with transcription-only history
      const transcriptionHistory = [
        {
          role: 'system',
          content: 'You are a transcription service. Transcribe the user\'s speech exactly as they said it. Do not respond to the user or provide assistance - only transcribe their words.'
        }
      ];
      
      console.log('[EnhancedNLU] Calling transcription client...');
      const transcriptionResult = await transcriptionClient.audioToText(
        audioBytes,
        mimeType,
        this.env,
        null, // no page context needed for transcription
        transcriptionHistory,
        [] // no memory facts needed for transcription
      );
      
      console.log('[EnhancedNLU] Transcription result:', {
        text: transcriptionResult.text,
        textLength: transcriptionResult.text?.length || 0,
        hasError: !!transcriptionResult.error
      });
      
      if (transcriptionResult.text && transcriptionResult.text.length > 3) {
        console.log('[EnhancedNLU] User speech transcribed:', transcriptionResult.text);
        
        // Extract facts from the actual user speech
        await this.contextHydration.extractAndStoreFacts(userId, transcriptionResult.text);
        console.log('[EnhancedNLU] Async fact extraction completed for:', userId);
      } else {
        console.log('[EnhancedNLU] No meaningful transcription received:', transcriptionResult.text);
      }
    } catch (error) {
      console.error('[EnhancedNLU] Async extraction error:', error);
    }
  }

  /**
   * Detect if response contains product pricing or product information
   */
  private containsProductInfo(text: string): boolean {
    const productIndicators = [
      /\bpriced?\b.*\$|AUD|USD|EUR|GBP/i,
      /\bfrom\s+[\d.]+\s*(AUD|USD|EUR|GBP)/i,
      /\$[\d.]+|\b[\d.]+\s*(AUD|USD|EUR|GBP)/i,
      /\b(price|cost|pricing)\b/i,
      /\b(product|item)\b.*\b(priced?|costs?)\b/i
    ];
    
    return productIndicators.some(pattern => pattern.test(text));
  }

  /**
   * Extract product search query from user question and response
   */
  private extractProductQuery(userQuestion: string, response: string): string | null {
    // First try to extract from user question
    const userProductMatch = userQuestion.match(/(?:price|cost).*?(?:of|for)\s+(.+?)(?:\?|$)/i);
    if (userProductMatch) {
      return userProductMatch[1].trim();
    }

    // Then try to extract product name from response
    const responseProductMatch = response.match(/The\s+(.+?)\s+product\s+is\s+priced/i);
    if (responseProductMatch) {
      return responseProductMatch[1].trim();
    }

    // Fallback: extract any product-like terms
    const generalMatch = userQuestion.match(/\b(blue\s+hill|hill|blue\s*\w+)\b/i);
    if (generalMatch) {
      return generalMatch[1].trim();
    }

    return null;
  }

  /**
   * Generate cards from tool call results and background data
   */
  private generateCardsFromResults(toolCalls: any[], backgroundProducts: any[], responseText: string): CardData[] {
    const cards: CardData[] = [];

    // Process tool call results into cards
    toolCalls.forEach(call => {
      try {
        switch (call.tool) {
          case 'searchProducts':
            if (call.products && call.products.length > 0) {
              cards.push({
                type: 'product',
                title: `Found ${call.products.length} products`,
                data: { 
                  products: call.products.slice(0, 3),
                  showAddToCart: true,
                  showQuickView: true,
                  maxItems: 3
                },
                layout: 'horizontal-scroll',
                priority: 1
              });
            }
            break;

          case 'getCart':
            if (call.cart) {
              cards.push({
                type: 'cart',
                title: `Your Cart (${call.cart.itemCount || 0} items)`,
                subtitle: `Total: ${call.cart.total || '$0.00'}`,
                data: call.cart,
                layout: 'vertical-stack',
                priority: 2,
                actions: [
                  { type: 'button', label: 'Checkout', action: 'checkout', style: 'primary' }
                ]
              });
            }
            break;

          case 'getOrders':
            if (call.orders && call.orders.length > 0) {
              cards.push({
                type: 'order',
                title: 'Your Recent Orders',
                data: { orders: call.orders.slice(0, 3) },
                layout: 'vertical-stack',
                priority: 3
              });
            }
            break;

          case 'webResearch':
            if (call.content) {
              cards.push({
                type: 'status',
                title: 'Research Results',
                data: { 
                  status: 'info',
                  message: call.content.substring(0, 200) + (call.content.length > 200 ? '...' : '')
                },
                layout: 'compact',
                priority: 4
              });
            }
            break;
        }
      } catch (error) {
        console.warn('[EnhancedNLU] Error generating card for tool call:', call.tool, error);
      }
    });

    // Add product cards from background search if no tool-generated product cards exist
    if (backgroundProducts.length > 0 && !cards.some(card => card.type === 'product')) {
      cards.push({
        type: 'product',
        title: `Related Products`,
        data: { 
          products: backgroundProducts.slice(0, 3),
          showAddToCart: true,
          showQuickView: true,
          maxItems: 3
        },
        layout: 'horizontal-scroll',
        priority: 5
      });
    }

    return cards.sort((a, b) => (a.priority || 999) - (b.priority || 999));
  }

  /**
   * Fetch personalised recommendations from GraphMemory Durable Object
   */
  private async fetchRecommendations(userId: string, limit = 5): Promise<ProductSuggestion[]> {
    if (!this.env.GRAPH_MEMORY_URL) {
      throw new Error('GRAPH_MEMORY_URL not configured');
    }
    const url = `${this.env.GRAPH_MEMORY_URL}/getRecommendations?userId=${encodeURIComponent(userId)}&limit=${limit}`;
    const resp = await fetch(url);
    if (!resp.ok) {
      throw new Error(`GraphMemory request failed ${resp.status}`);
    }
    const data = await resp.json();
    return data.recommendations || [];
  }
}
