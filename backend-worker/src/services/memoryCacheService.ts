/**
 * Memory Cache Service - Performance Optimization Implementation
 * 
 * Implements in-memory caching with TTL for user memory facts to reduce
 * Neo4j query latency from 4.6s to <300ms for cached queries.
 */

import { MemoryFact } from '../durable-objects/graphMemory';

interface CacheEntry {
  data: MemoryFact[];
  expires: number;
  lastAccessed: number;
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  totalQueries: number;
}

export class MemoryCacheService {
  private cache = new Map<string, CacheEntry>();
  private stats: CacheStats = { hits: 0, misses: 0, evictions: 0, totalQueries: 0 };
  
  // Configuration
  private readonly TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 1000; // Maximum cached users
  private readonly CLEANUP_INTERVAL = 60 * 1000; // Cleanup every minute
  
  private cleanupTimer?: NodeJS.Timeout;
  private env: any;

  constructor(env: any) {
    this.env = env;
    this.startCleanupTimer();
  }

  /**
   * Get user memory facts with caching
   * Returns cached data if available, otherwise fetches from Neo4j
   */
  async getUserFacts(userId: string, maxFacts: number = 5): Promise<MemoryFact[]> {
    this.stats.totalQueries++;
    
    // Check cache first
    const cached = this.cache.get(userId);
    const now = Date.now();
    
    if (cached && cached.expires > now) {
      // Cache hit - update access time
      cached.lastAccessed = now;
      this.stats.hits++;
      
      console.log(`[MemoryCache] Cache HIT for user ${userId} (${cached.data.length} facts)`);
      
      // Apply limit to cached results
      return maxFacts ? cached.data.slice(0, maxFacts) : cached.data;
    }
    
    // Cache miss - fetch from Neo4j
    this.stats.misses++;
    console.log(`[MemoryCache] Cache MISS for user ${userId}, fetching from Neo4j...`);
    
    const startTime = Date.now();
    const facts = await this.fetchFromNeo4j(userId);
    const fetchTime = Date.now() - startTime;
    
    console.log(`[MemoryCache] Neo4j fetch completed in ${fetchTime}ms (${facts.length} facts)`);
    
    // Store in cache
    this.setCachedFacts(userId, facts);
    
    // Apply limit and return
    return maxFacts ? facts.slice(0, maxFacts) : facts;
  }

  /**
   * Get user facts for a specific query with semantic caching
   */
  async getRelevantFacts(userId: string, query: string, maxFacts: number = 5): Promise<MemoryFact[]> {
    // Create cache key that includes query context
    const cacheKey = `${userId}:${this.hashQuery(query)}`;
    
    const cached = this.cache.get(cacheKey);
    const now = Date.now();
    
    if (cached && cached.expires > now) {
      cached.lastAccessed = now;
      this.stats.hits++;
      console.log(`[MemoryCache] Semantic cache HIT for query: "${query}"`);
      return cached.data.slice(0, maxFacts);
    }
    
    // Cache miss - get all facts and filter relevantly
    const allFacts = await this.getUserFacts(userId);
    const relevantFacts = this.filterRelevantFacts(allFacts, query, maxFacts);
    
    // Cache the query-specific results
    this.setCachedFacts(cacheKey, relevantFacts);
    
    return relevantFacts;
  }

  /**
   * Invalidate cache for a user (call when new facts are added)
   */
  invalidateUser(userId: string): void {
    // Remove user-specific cache entries
    const keysToDelete = Array.from(this.cache.keys()).filter(key => 
      key.startsWith(userId + ':') || key === userId
    );
    
    keysToDelete.forEach(key => this.cache.delete(key));
    
    console.log(`[MemoryCache] Invalidated ${keysToDelete.length} cache entries for user ${userId}`);
  }

  /**
   * Preload user facts for faster access (call during login/session start)
   */
  async preloadUser(userId: string): Promise<void> {
    console.log(`[MemoryCache] Preloading facts for user ${userId}...`);
    await this.getUserFacts(userId);
  }

  /**
   * Get cache statistics for monitoring
   */
  getCacheStats(): CacheStats & { hitRate: number; cacheSize: number } {
    const hitRate = this.stats.totalQueries > 0 
      ? (this.stats.hits / this.stats.totalQueries) * 100 
      : 0;
    
    return {
      ...this.stats,
      hitRate: Math.round(hitRate * 100) / 100,
      cacheSize: this.cache.size
    };
  }

  /**
   * Clear all cached data
   */
  clearCache(): void {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0, evictions: 0, totalQueries: 0 };
    console.log('[MemoryCache] Cache cleared');
  }

  // Private methods

  private setCachedFacts(key: string, facts: MemoryFact[]): void {
    const now = Date.now();
    
    // Check if we need to evict entries to stay under size limit
    if (this.cache.size >= this.MAX_CACHE_SIZE) {
      this.evictOldestEntries();
    }
    
    this.cache.set(key, {
      data: facts,
      expires: now + this.TTL,
      lastAccessed: now
    });
  }

  private async fetchFromNeo4j(userId: string): Promise<MemoryFact[]> {
    try {
      if (!this.env.GRAPH_MEMORY_DO) {
        console.warn('[MemoryCache] Graph Memory DO not available');
        return [];
      }

      const id = this.env.GRAPH_MEMORY_DO.idFromName(userId);
      const stub = this.env.GRAPH_MEMORY_DO.get(id);

      // Query all facts for the user
      const response = await stub.fetch(new Request(`https://do/facts?userId=${userId}&limit=20`));
      
      if (!response.ok) {
        console.error('[MemoryCache] Neo4j query failed:', response.status);
        return [];
      }

      const data = await response.json();
      return data.facts || [];

    } catch (error: any) {
      console.error('[MemoryCache] Error fetching from Neo4j:', error);
      return [];
    }
  }

  private filterRelevantFacts(facts: MemoryFact[], query: string, maxFacts: number): MemoryFact[] {
    // Simple relevance scoring based on keyword matching
    const queryWords = query.toLowerCase().split(' ');
    
    const scoredFacts = facts.map(fact => {
      const factText = `${fact.factType} ${fact.value}`.toLowerCase();
      const score = queryWords.reduce((acc, word) => {
        return acc + (factText.includes(word) ? 1 : 0);
      }, 0);
      
      return { fact, score };
    });
    
    // Sort by relevance score and return top results
    return scoredFacts
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, maxFacts)
      .map(item => item.fact);
  }

  private hashQuery(query: string): string {
    // Simple hash function for query caching
    let hash = 0;
    for (let i = 0; i < query.length; i++) {
      const char = query.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(36);
  }

  private evictOldestEntries(): void {
    // Remove 10% of entries, prioritizing oldest last accessed
    const entriesToRemove = Math.ceil(this.cache.size * 0.1);
    
    const entries = Array.from(this.cache.entries())
      .sort((a, b) => a[1].lastAccessed - b[1].lastAccessed)
      .slice(0, entriesToRemove);
    
    entries.forEach(([key]) => this.cache.delete(key));
    this.stats.evictions += entries.length;
    
    console.log(`[MemoryCache] Evicted ${entries.length} old cache entries`);
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpiredEntries();
    }, this.CLEANUP_INTERVAL);
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    let removedCount = 0;
    
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expires <= now) {
        this.cache.delete(key);
        removedCount++;
      }
    }
    
    if (removedCount > 0) {
      console.log(`[MemoryCache] Cleaned up ${removedCount} expired cache entries`);
    }
  }

  /**
   * Cleanup resources when service is destroyed
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clearCache();
  }
} 