// Knowledge Retrieval Service for Cloudflare AutoRAG
// Handles vector search and document management

export interface KnowledgeChunk {
  content: string;
  source?: string;
  score?: number;
  metadata?: Record<string, any>;
}

export interface KnowledgeSearchOptions {
  query: string;
  maxResults?: number;
  storeId?: string;
  filters?: Record<string, any>;
}

export class KnowledgeRetrievalService {
  private env: any;
  private autoragInstanceName: string;

  constructor(env: any, autoragInstanceName: string = 'aura-store-rag') {
    this.env = env;
    this.autoragInstanceName = autoragInstanceName;
  }

  /**
   * Search knowledge base using AutoRAG vector search
   */
  async searchKnowledge(options: KnowledgeSearchOptions): Promise<KnowledgeChunk[]> {
    try {
      console.log('[KnowledgeRetrieval] Searching with query:', options.query);

      if (!this.env.AI) {
        console.warn('[KnowledgeRetrieval] AI binding not available');
        return [];
      }

      // Prepare search parameters
      const searchParams: any = {
        query: options.query,
        max_num_results: options.maxResults || 5
      };

      // Add store-specific filtering if storeId provided
      if (options.storeId) {
        searchParams.filters = {
          folder: `store-${options.storeId}`,
          ...options.filters
        };
      }

      console.log('[KnowledgeRetrieval] AutoRAG search params:', searchParams);

      // Execute AutoRAG search
      const results = await this.env.AI.autorag(this.autoragInstanceName).search(searchParams);
      
      console.log('[KnowledgeRetrieval] AutoRAG raw results:', results);

      if (!results || !results.search_results) {
        console.warn('[KnowledgeRetrieval] No search results returned');
        return [];
      }

      // Transform results to our format
      const chunks: KnowledgeChunk[] = results.search_results.map((result: any) => ({
        content: result.text || result.content || '',
        source: this.extractSourceFromMetadata(result.metadata),
        score: result.score,
        metadata: result.metadata || {}
      }));

      console.log('[KnowledgeRetrieval] Processed chunks:', chunks.length);
      return chunks;

    } catch (error: any) {
      console.error('[KnowledgeRetrieval] Search error:', error);
      return [];
    }
  }

  /**
   * Upload document to R2 bucket for AutoRAG indexing
   */
  async uploadDocument(file: File, storeId: string, filename?: string): Promise<{ success: boolean; key?: string; error?: string }> {
    try {
      if (!this.env.KNOWLEDGE_BUCKET) {
        return { success: false, error: 'Knowledge bucket not configured' };
      }

      // Generate unique filename if not provided
      const fileExtension = file.name.split('.').pop() || 'txt';
      const sanitizedName = filename || file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
      const key = `store-${storeId}/uploads/${Date.now()}-${sanitizedName}`;

      console.log('[KnowledgeRetrieval] Uploading file:', key);

      // Upload to R2
      await this.env.KNOWLEDGE_BUCKET.put(key, file.stream());

      console.log('[KnowledgeRetrieval] File uploaded successfully:', key);
      return { success: true, key };

    } catch (error: any) {
      console.error('[KnowledgeRetrieval] Upload error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * List documents in R2 bucket for a specific store
   */
  async listDocuments(storeId: string): Promise<{ files: Array<{ key: string; size: number; lastModified: Date }> }> {
    try {
      if (!this.env.KNOWLEDGE_BUCKET) {
        return { files: [] };
      }

      const prefix = `store-${storeId}/`;
      const list = await this.env.KNOWLEDGE_BUCKET.list({ prefix });

      const files = list.objects.map((obj: any) => ({
        key: obj.key,
        size: obj.size,
        lastModified: obj.uploaded
      }));

      console.log('[KnowledgeRetrieval] Listed files for store:', storeId, 'count:', files.length);
      return { files };

    } catch (error: any) {
      console.error('[KnowledgeRetrieval] List error:', error);
      return { files: [] };
    }
  }

  /**
   * Delete document from R2 bucket
   */
  async deleteDocument(key: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.env.KNOWLEDGE_BUCKET) {
        return { success: false, error: 'Knowledge bucket not configured' };
      }

      await this.env.KNOWLEDGE_BUCKET.delete(key);
      console.log('[KnowledgeRetrieval] File deleted:', key);
      return { success: true };

    } catch (error: any) {
      console.error('[KnowledgeRetrieval] Delete error:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Sync Shopify pages and policies to R2 bucket
   */
  async syncShopifyContent(storeId: string, shopifyDomain: string, accessToken: string): Promise<{ success: boolean; synced: number; error?: string }> {
    try {
      console.log('[KnowledgeRetrieval] Syncing Shopify content for store:', storeId);

      let syncedCount = 0;

      // Fetch and sync pages
      const pages = await this.fetchShopifyPages(shopifyDomain, accessToken);
      for (const page of pages) {
        const key = `store-${storeId}/pages/${page.handle}.html`;
        const content = `# ${page.title}\n\n${page.body_html}`;
        
        await this.env.KNOWLEDGE_BUCKET.put(key, content);
        syncedCount++;
      }

      // Fetch and sync policies
      const policies = await this.fetchShopifyPolicies(shopifyDomain, accessToken);
      for (const policy of policies) {
        if (policy.body) {
          const key = `store-${storeId}/policies/${policy.handle}.md`;
          const content = `# ${policy.title}\n\n${policy.body}`;
          
          await this.env.KNOWLEDGE_BUCKET.put(key, content);
          syncedCount++;
        }
      }

      console.log('[KnowledgeRetrieval] Synced content count:', syncedCount);
      return { success: true, synced: syncedCount };

    } catch (error: any) {
      console.error('[KnowledgeRetrieval] Sync error:', error);
      return { success: false, synced: 0, error: error.message };
    }
  }

  /**
   * Extract source information from AutoRAG metadata
   */
  private extractSourceFromMetadata(metadata: any): string | undefined {
    if (!metadata) return undefined;
    
    // Try to extract filename or source from various metadata fields
    if (metadata.source) return metadata.source;
    if (metadata.file) return metadata.file;
    if (metadata.filename) return metadata.filename;
    if (metadata.path) {
      // Extract just the filename from path
      return metadata.path.split('/').pop();
    }
    
    return undefined;
  }

  /**
   * Fetch Shopify pages via Admin API
   */
  private async fetchShopifyPages(domain: string, accessToken: string): Promise<any[]> {
    try {
      const response = await fetch(`https://${domain}/admin/api/2024-01/pages.json`, {
        headers: {
          'X-Shopify-Access-Token': accessToken
        }
      });

      if (!response.ok) {
        throw new Error(`Shopify API error: ${response.status}`);
      }

      const data = await response.json();
      return data.pages || [];
    } catch (error: any) {
      console.error('[KnowledgeRetrieval] Error fetching Shopify pages:', error);
      return [];
    }
  }

  /**
   * Fetch Shopify policies via Admin API
   */
  private async fetchShopifyPolicies(domain: string, accessToken: string): Promise<any[]> {
    try {
      const response = await fetch(`https://${domain}/admin/api/2024-01/policies.json`, {
        headers: {
          'X-Shopify-Access-Token': accessToken
        }
      });

      if (!response.ok) {
        throw new Error(`Shopify API error: ${response.status}`);
      }

      const data = await response.json();
      return data.policies || [];
    } catch (error: any) {
      console.error('[KnowledgeRetrieval] Error fetching Shopify policies:', error);
      return [];
    }
  }
}