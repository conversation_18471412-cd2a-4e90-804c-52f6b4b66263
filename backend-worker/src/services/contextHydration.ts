// Unified Context Hydration Service
// Combines graph memory and knowledge retrieval for LLM prompt construction

import { MemoryFact } from '../durable-objects/graphMemory';
import { KnowledgeChunk, KnowledgeRetrievalService } from './knowledgeRetrieval';

export interface ConversationMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface ContextHydrationOptions {
  userId: string;
  userQuery: string;
  storeId?: string;
  includeMemory?: boolean;
  includeKnowledge?: boolean;
  maxMemoryFacts?: number;
  maxKnowledgeChunks?: number;
}

export interface HydratedContext {
  memoryFacts: MemoryFact[];
  knowledgeChunks: KnowledgeChunk[];
  formattedContext: string;
  messages: ConversationMessage[];
}

export class ContextHydrationService {
  private env: any;
  private knowledgeService: KnowledgeRetrievalService;
  private profileCache: Map<string, { data: MemoryFact[]; timestamp: number; }> = new Map();
  private readonly CACHE_TTL = 60000; // 1 minute cache

  constructor(env: any) {
    this.env = env;
    this.knowledgeService = new KnowledgeRetrievalService(env);
  }

  /**
   * Gather and format context from memory and knowledge sources
   */
  async gatherContext(options: ContextHydrationOptions): Promise<HydratedContext> {
    console.log('[ContextHydration] Gathering context for user:', options.userId, 'query:', options.userQuery);

    try {
      // Fetch memory and knowledge in parallel for performance
      const [memoryFacts, knowledgeChunks] = await Promise.all([
        options.includeMemory !== false ? this.getRelevantMemoryFacts(options) : Promise.resolve([]),
        options.includeKnowledge !== false ? this.getRelevantKnowledge(options) : Promise.resolve([])
      ]);

      // Format context for LLM prompt
      const formattedContext = this.formatContextForPrompt(memoryFacts, knowledgeChunks);

      // Construct messages array
      const messages = this.constructMessages(formattedContext, options.userQuery);

      console.log('[ContextHydration] Context gathered:', {
        memoryFactsCount: memoryFacts.length,
        knowledgeChunksCount: knowledgeChunks.length,
        contextLength: formattedContext.length
      });

      return {
        memoryFacts,
        knowledgeChunks,
        formattedContext,
        messages
      };

    } catch (error: any) {
      console.error('[ContextHydration] Error gathering context:', error);
      
      // Return minimal context on error
      return {
        memoryFacts: [],
        knowledgeChunks: [],
        formattedContext: '',
        messages: [{ role: 'user', content: options.userQuery }]
      };
    }
  }

  /**
   * Get relevant memory facts for the user and query
   */
  private async getRelevantMemoryFacts(options: ContextHydrationOptions): Promise<MemoryFact[]> {
    try {
      if (!this.env.GRAPH_MEMORY_DO) {
        console.warn('[ContextHydration] Graph Memory DO not available');
        return [];
      }

      // Check cache first
      const cacheKey = `${options.userId}-${options.userQuery}`;
      const cached = this.profileCache.get(cacheKey);
      const now = Date.now();
      
      if (cached && (now - cached.timestamp) < this.CACHE_TTL) {
        console.log('[ContextHydration] Using cached memory facts for user:', options.userId);
        const facts = cached.data;
        
        // Apply limit to cached results
        if (options.maxMemoryFacts && facts.length > options.maxMemoryFacts) {
          return facts.slice(0, options.maxMemoryFacts);
        }
        return facts;
      }

      // Get DO stub for the user
      const id = this.env.GRAPH_MEMORY_DO.idFromName(options.userId);
      const stub = this.env.GRAPH_MEMORY_DO.get(id);

      // Query relevant facts
      const response = await stub.fetch(new Request(`https://do/queryFacts?userId=${options.userId}&query=${encodeURIComponent(options.userQuery)}`));
      
      if (!response.ok) {
        console.error('[ContextHydration] Memory query failed:', response.status);
        return [];
      }

      const data = await response.json();
      const facts = data.facts || [];

      // Cache the results
      this.profileCache.set(cacheKey, { data: facts, timestamp: now });
      
              // Clean up old cache entries (simple LRU approximation)
        if (this.profileCache.size > 100) {
          const oldestKey = this.profileCache.keys().next().value;
          if (oldestKey) {
            this.profileCache.delete(oldestKey);
          }
        }

      // Limit facts if specified
      if (options.maxMemoryFacts && facts.length > options.maxMemoryFacts) {
        return facts.slice(0, options.maxMemoryFacts);
      }

      return facts;

    } catch (error: any) {
      console.error('[ContextHydration] Error fetching memory facts:', error);
      return [];
    }
  }

  /**
   * Get relevant knowledge chunks for the query
   */
  private async getRelevantKnowledge(options: ContextHydrationOptions): Promise<KnowledgeChunk[]> {
    try {
      const chunks = await this.knowledgeService.searchKnowledge({
        query: options.userQuery,
        maxResults: options.maxKnowledgeChunks || 3,
        storeId: options.storeId
      });

      return chunks;

    } catch (error: any) {
      console.error('[ContextHydration] Error fetching knowledge:', error);
      return [];
    }
  }

  /**
   * Format memory facts and knowledge chunks for LLM prompt
   */
  private formatContextForPrompt(memoryFacts: MemoryFact[], knowledgeChunks: KnowledgeChunk[]): string {
    const sections: string[] = [];

    // Format memory facts
    if (memoryFacts.length > 0) {
      sections.push('[Memory] User facts and preferences:');
      memoryFacts.forEach(fact => {
        const factText = this.formatMemoryFact(fact);
        if (factText) {
          sections.push(`- ${factText}`);
        }
      });
    }

    // Format knowledge chunks
    if (knowledgeChunks.length > 0) {
      sections.push('\n[Knowledge] Store information:');
      knowledgeChunks.forEach((chunk, index) => {
        const source = chunk.source ? ` (Source: ${chunk.source})` : '';
        sections.push(`- ${chunk.content.trim()}${source}`);
      });
    }

    return sections.join('\n');
  }

  /**
   * Convert memory fact to human-readable text
   */
  private formatMemoryFact(fact: MemoryFact): string {
    switch (fact.factType) {
      case 'LIKES':
        return `User likes ${fact.value}`;
      case 'DISLIKES':
        return `User dislikes ${fact.value}`;
      case 'HAS_PERSONA':
        return `User is a ${fact.value}`;
      case 'GOAL':
        return `User's current goal: ${fact.value}`;
      case 'ASKED_ABOUT':
        return `User previously asked about ${fact.value}`;
      case 'HAS_POLICY':
        return `Store policy: ${fact.value}`;
      default:
        return `${fact.factType}: ${fact.value}`;
    }
  }

  /**
   * Construct messages array for LLM with context and user query
   */
  private constructMessages(formattedContext: string, userQuery: string): ConversationMessage[] {
    const messages: ConversationMessage[] = [];

    // System message with instructions
    messages.push({
      role: 'system',
      content: `You are a helpful AI shopping assistant. Use the provided context to give personalized and accurate responses. Always prioritize facts from the knowledge base for store information and use memory facts to personalize your responses.`
    });

    // Context as assistant message if available
    if (formattedContext.trim()) {
      messages.push({
        role: 'assistant',
        content: formattedContext
      });
    }

    // User query
    messages.push({
      role: 'user',
      content: userQuery
    });

    return messages;
  }

  /**
   * Add memory fact based on user interaction
   */
  async addMemoryFact(userId: string, factType: string, value: string, metadata?: Record<string, any>): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.env.GRAPH_MEMORY_DO) {
        return { success: false, error: 'Graph Memory DO not available' };
      }

      const id = this.env.GRAPH_MEMORY_DO.idFromName(userId);
      const stub = this.env.GRAPH_MEMORY_DO.get(id);

      const response = await stub.fetch(new Request('https://do/addFact', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ userId, factType, value, metadata })
      }));

      if (!response.ok) {
        const errorText = await response.text();
        return { success: false, error: `Memory update failed: ${errorText}` };
      }

      const result = await response.json();
      return result;

    } catch (error: any) {
      console.error('[ContextHydration] Error adding memory fact:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Extract facts from user utterance using simple rule-based approach
   */
  async extractAndStoreFacts(userId: string, userMessage: string): Promise<void> {
    try {
      console.log('[ContextHydration] Attempting to extract facts from:', userMessage);
      
      // Filter out assistant responses to prevent extracting facts from them
      if (this.isAssistantResponse(userMessage)) {
        console.log('[ContextHydration] ⚠️ Skipping fact extraction - detected assistant response');
        return;
      }
      
      // Get existing facts to prevent duplicates
      const existingFacts = await this.getExistingUserFacts(userId);
      const existingFactsSet = new Set(existingFacts.map(f => `${f.factType}:${f.value.toLowerCase()}`));
      
      // First try Gemini-powered fact extraction (more intelligent)
      const geminiFactsExtracted = await this.extractFactsWithGemini(userId, userMessage, existingFactsSet);
      
      // Then run regex patterns as backup/additional extraction
      const regexFactsExtracted = await this.extractFactsWithRegex(userId, userMessage, existingFactsSet);
      
      const totalFacts = geminiFactsExtracted + regexFactsExtracted;
      console.log('[ContextHydration] Total facts extracted:', totalFacts, '(Gemini:', geminiFactsExtracted, 'Regex:', regexFactsExtracted, ')');

    } catch (error: any) {
      console.error('[ContextHydration] Error extracting facts:', error);
    }
  }

  /**
   * Get existing facts for a user to prevent duplicates
   */
  private async getExistingUserFacts(userId: string): Promise<MemoryFact[]> {
    try {
      const options: ContextHydrationOptions = {
        userId,
        userQuery: '',
        includeMemory: true,
        maxMemoryFacts: 50
      };
      return await this.getRelevantMemoryFacts(options);
    } catch (error) {
      console.error('[ContextHydration] Error getting existing facts:', error);
      return [];
    }
  }

  /**
   * Extract facts using Gemini AI for better intent understanding
   */
  private async extractFactsWithGemini(userId: string, userMessage: string, existingFacts?: Set<string>): Promise<number> {
    try {
      console.log('[ContextHydration] 🤖 Starting Gemini fact extraction...');
      
      if (!this.env.GEMINI_API_KEY) {
        console.log('[ContextHydration] ❌ Gemini API key not available, skipping AI extraction');
        return 0;
      }

      console.log('[ContextHydration] 🤖 Gemini API key available, proceeding with extraction');
      
      const prompt = `User said: "${userMessage}"

Extract facts as JSON:
{"facts": [{"factType": "HAS_PERSONA", "value": "William", "confidence": 0.9}]}

Types: LIKES, DISLIKES, HAS_PERSONA, GOAL, ASKED_ABOUT, HAS_POLICY
Only confident facts (>0.6). Return {"facts": []} if none.`;

      console.log('[ContextHydration] 🤖 Making Gemini API call for fact extraction...');
      
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${this.env.GEMINI_API_KEY}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          contents: [{ parts: [{ text: prompt }] }],
          generationConfig: {
            temperature: 0.1,
            maxOutputTokens: 800,
            // Remove stopSequences to allow complete JSON generation
          }
        })
      });

      console.log('[ContextHydration] 🤖 Gemini API response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('[ContextHydration] ❌ Gemini API error:', response.status, errorText);
        return 0;
      }

      const data = await response.json();
      console.log('[ContextHydration] 🤖 Gemini API response received:', JSON.stringify(data, null, 2));
      
      const text = data.candidates?.[0]?.content?.parts?.[0]?.text;
      
      if (!text) {
        console.log('[ContextHydration] ❌ No response text from Gemini:', JSON.stringify(data, null, 2));
        return 0;
      }

      console.log('[ContextHydration] 🤖 Gemini response text:', text);

      // Extract JSON with more robust parsing
      let jsonText = text.trim();
      
      // Remove markdown code block markers if present
      if (jsonText.startsWith('```json')) {
        jsonText = jsonText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (jsonText.startsWith('```')) {
        jsonText = jsonText.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }
      
      // Find JSON object bounds
      const jsonStart = jsonText.indexOf('{');
      const jsonEnd = jsonText.lastIndexOf('}');
      
      if (jsonStart === -1 || jsonEnd === -1 || jsonEnd <= jsonStart) {
        console.log('[ContextHydration] ❌ No valid JSON brackets found in response:', text);
        return 0;
      }
      
      const extractedJson = jsonText.substring(jsonStart, jsonEnd + 1);
      console.log('[ContextHydration] 🤖 Extracted JSON:', extractedJson);

      let factData;
      try {
        factData = JSON.parse(extractedJson);
      } catch (parseError: any) {
        console.error('[ContextHydration] ❌ Failed to parse JSON:', parseError.message);
        console.error('[ContextHydration] ❌ Problematic JSON string:', extractedJson);
        
        // Try to fix common JSON issues
        try {
          // Fix missing quotes around property names
          let fixedJson = extractedJson
            .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":')
            // Fix missing "value" property by wrapping the value after colon
            .replace(/("factType":\s*"[^"]+",\s*)"([^"]+)"(\s*,\s*"confidence")/g, '$1"value": "$2"$3')
            // Remove trailing commas
            .replace(/,(\s*[}\]])/g, '$1');
            
          console.log('[ContextHydration] 🔧 Attempting to fix JSON:', fixedJson);
          factData = JSON.parse(fixedJson);
          console.log('[ContextHydration] ✅ JSON fixed successfully');
        } catch (fixError) {
          console.error('[ContextHydration] ❌ JSON fix failed:', fixError);
          return 0;
        }
      }
      
      const facts = factData.facts || [];
      console.log('[ContextHydration] 🤖 Parsed facts from Gemini:', facts.length, 'facts found');
      
      let storedCount = 0;
      for (const fact of facts) {
        console.log('[ContextHydration] 🤖 Processing fact:', fact);
        
        // Validate fact structure
        if (!fact.factType || !fact.value || typeof fact.confidence !== 'number') {
          console.log('[ContextHydration] ⚠️ Invalid fact structure:', fact);
          continue;
        }
        
        if (fact.confidence > 0.6 && fact.value && fact.factType) {
          // Check for duplicates
          const factKey = `${fact.factType}:${fact.value.toLowerCase()}`;
          if (existingFacts && existingFacts.has(factKey)) {
            console.log('[ContextHydration] ⚠️ Skipping duplicate fact:', fact.factType, fact.value);
            continue;
          }
          
          console.log('[ContextHydration] 🤖 Fact meets criteria, storing...');
          
          const result = await this.addMemoryFact(userId, fact.factType, fact.value, { 
            confidence: fact.confidence, 
            extractedBy: 'gemini' 
          });
          
          if (result.success) {
            console.log('[ContextHydration] ✅ Gemini extracted fact:', fact.factType, fact.value, `(confidence: ${fact.confidence})`);
            if (existingFacts) existingFacts.add(factKey); // Add to prevent duplicates in same session
            storedCount++;
          } else {
            console.error('[ContextHydration] ❌ Failed to store Gemini fact:', result.error);
          }
        } else {
          console.log('[ContextHydration] ⚠️ Fact rejected - confidence:', fact.confidence, 'value:', fact.value, 'factType:', fact.factType);
        }
      }

      console.log('[ContextHydration] 🤖 Gemini extraction complete. Stored:', storedCount, 'facts');
      return storedCount;

    } catch (error: any) {
      console.error('[ContextHydration] ❌ Error in Gemini fact extraction:', error);
      console.error('[ContextHydration] ❌ Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
      return 0;
    }
  }

  /**
   * Extract facts using regex patterns (backup method)
   */
  private async extractFactsWithRegex(userId: string, userMessage: string, existingFacts?: Set<string>): Promise<number> {
    try {
      console.log('[ContextHydration] 📝 Starting regex fact extraction...');
      console.log('[ContextHydration] 📝 Input message:', userMessage);
      
      const lowercaseMessage = userMessage.toLowerCase();

      // Comprehensive pattern matching for fact extraction
      const patterns = [
        // === PREFERENCES & LIKES ===
        { regex: /i (?:love|like|prefer|enjoy|adore) (.+)/i, factType: 'LIKES' },
        { regex: /(?:love|like|enjoy) (.+) (?:a lot|so much)/i, factType: 'LIKES' },
        { regex: /(.+) (?:is|are) (?:my favorite|amazing|great|awesome)/i, factType: 'LIKES' },
        { regex: /i'm (?:into|obsessed with|a fan of) (.+)/i, factType: 'LIKES' },
        { regex: /interested in (.+)/i, factType: 'LIKES' },
        { regex: /passionate about (.+)/i, factType: 'LIKES' },
        
        // === DISLIKES ===
        { regex: /i (?:hate|don't like|dislike|can't stand|avoid) (.+)/i, factType: 'DISLIKES' },
        { regex: /not (?:a fan of|into|interested in) (.+)/i, factType: 'DISLIKES' },
        { regex: /(.+) (?:is|are) (?:terrible|awful|not for me)/i, factType: 'DISLIKES' },
        
        // === IDENTITY & PERSONA ===
        { regex: /my name is ([A-Z][a-z]+)/i, factType: 'HAS_PERSONA' },
        { regex: /call me ([A-Z][a-z]+)/i, factType: 'HAS_PERSONA' },
        { regex: /(?:okay|hi|hello),?\s+([A-Z][a-z]+)(?:\.|!|$)/i, factType: 'HAS_PERSONA' },
        { regex: /this is ([A-Z][a-z]+)/i, factType: 'HAS_PERSONA' },
        { regex: /([A-Z][a-z]+) here/i, factType: 'HAS_PERSONA' },
        { regex: /i work (?:as|in) (?:a |an )?(.+)/i, factType: 'HAS_PERSONA' },
        { regex: /i am (?:a |an )?(.+)(?:engineer|developer|designer|teacher|student|manager)/i, factType: 'HAS_PERSONA' },
        { regex: /i'm (?:from|in) ([A-Z][a-z\s]+)/i, factType: 'HAS_PERSONA' },
        { regex: /i live in ([A-Z][a-z\s]+)/i, factType: 'HAS_PERSONA' },
        
        // === GOALS & NEEDS ===
        { regex: /i need (?:to )?(.+)/i, factType: 'GOAL' },
        { regex: /looking for (.+)/i, factType: 'GOAL' },
        { regex: /want to (?:buy|purchase|get|find) (.+)/i, factType: 'GOAL' },
        { regex: /trying to (?:find|buy|get) (.+)/i, factType: 'GOAL' },
        { regex: /searching for (.+)/i, factType: 'GOAL' },
        { regex: /hoping to (?:find|buy|get) (.+)/i, factType: 'GOAL' },
        { regex: /my goal is (?:to )?(.+)/i, factType: 'GOAL' },
        { regex: /planning to (?:buy|get) (.+)/i, factType: 'GOAL' },
        { regex: /in the market for (.+)/i, factType: 'GOAL' },
        
        // === QUESTIONS & INTERESTS ===
        { regex: /(?:tell me about|what (?:is|are)) (.+)/i, factType: 'ASKED_ABOUT' },
        { regex: /how (?:much|does) (.+)/i, factType: 'ASKED_ABOUT' },
        { regex: /where can i (?:find|buy|get) (.+)/i, factType: 'ASKED_ABOUT' },
        { regex: /do you have (?:any )?(.+)/i, factType: 'ASKED_ABOUT' },
        { regex: /what's (?:the best|good) (.+)/i, factType: 'ASKED_ABOUT' },
        { regex: /show me (?:some |the )?(.+)/i, factType: 'ASKED_ABOUT' },
        
        // === SHOPPING BEHAVIOR ===
        { regex: /i usually (?:buy|shop for|get) (.+)/i, factType: 'LIKES' },
        { regex: /i always (?:buy|get|use) (.+)/i, factType: 'LIKES' },
        { regex: /my budget is (.+)/i, factType: 'HAS_POLICY' },
        { regex: /i don't want to spend more than (.+)/i, factType: 'HAS_POLICY' },
        { regex: /price range (?:is|of) (.+)/i, factType: 'HAS_POLICY' },
        
        // === PERSONAL DETAILS ===
        { regex: /i have (?:a |an )?(.+)/i, factType: 'HAS_PERSONA' },
        { regex: /my (.+) is (.+)/i, factType: 'HAS_PERSONA' },
        { regex: /i'm (\d+) (?:years old)?/i, factType: 'HAS_PERSONA' },
        { regex: /born in (\d{4})/i, factType: 'HAS_PERSONA' }
      ];

      let factsExtracted = 0;
      console.log('[ContextHydration] 📝 Testing', patterns.length, 'regex patterns...');
      
      for (const pattern of patterns) {
        const match = userMessage.match(pattern.regex);
        if (match && match[1]) {
          const value = match[1].trim();
          console.log('[ContextHydration] ✅ Pattern matched:', pattern.factType, 'value:', value, 'pattern:', pattern.regex.toString());
          
          if (value.length > 1 && value.length < 100) { // Basic validation (reduced from 2 to 1 for names)
            // Check for duplicates
            const factKey = `${pattern.factType}:${value.toLowerCase()}`;
            if (existingFacts && existingFacts.has(factKey)) {
              console.log('[ContextHydration] ⚠️ Skipping duplicate regex fact:', pattern.factType, value);
              continue;
            }
            
            console.log('[ContextHydration] 📝 Value passes validation, storing fact...');
            const result = await this.addMemoryFact(userId, pattern.factType, value, { extractedBy: 'regex' });
            if (result.success) {
              console.log('[ContextHydration] ✅ Regex extracted fact:', pattern.factType, value);
              if (existingFacts) existingFacts.add(factKey); // Add to prevent duplicates in same session
              factsExtracted++;
            } else {
              console.error('[ContextHydration] ❌ Failed to store regex fact:', result.error);
            }
          } else {
            console.log('[ContextHydration] ⚠️ Value rejected (length):', value, 'length:', value.length);
          }
        }
      }

      console.log('[ContextHydration] 📝 Regex extraction complete. Found:', factsExtracted, 'facts');
      
      if (factsExtracted === 0) {
        console.log('[ContextHydration] ❌ No regex facts extracted from message:', userMessage);
      }

      return factsExtracted;

    } catch (error: any) {
      console.error('[ContextHydration] Error in regex fact extraction:', error);
      return 0;
    }
  }

  /**
   * Handle special case of name extraction from conversational context
   */
  private async extractNamesFromContext(userId: string, userMessage: string): Promise<string[]> {
    const names: string[] = [];
    
    // Look for proper names in various contexts
    const namePatterns = [
      /(?:okay|hi|hello),?\s+([A-Z][a-z]{2,15})(?:\.|!|,|$)/gi,
      /this is ([A-Z][a-z]{2,15})/gi,
      /([A-Z][a-z]{2,15}) here/gi,
      /my name is ([A-Z][a-z]{2,15})/gi,
      /call me ([A-Z][a-z]{2,15})/gi,
      /i'm ([A-Z][a-z]{2,15})/gi
    ];
    
    for (const pattern of namePatterns) {
      let match;
      while ((match = pattern.exec(userMessage)) !== null) {
        const name = match[1].trim();
        // Filter out common words that aren't names
        const commonWords = ['Good', 'Nice', 'Great', 'Sure', 'Okay', 'Hello', 'Thanks', 'Sorry', 'Please'];
        if (!commonWords.includes(name) && name.length >= 2) {
          names.push(name);
        }
      }
    }
    
    return [...new Set(names)]; // Remove duplicates
  }

  /**
   * Get user profile summary from memory
   */
  async getUserProfile(userId: string): Promise<{ preferences: string[]; persona: string[]; goals: string[] }> {
    try {
      if (!this.env.GRAPH_MEMORY_DO) {
        return { preferences: [], persona: [], goals: [] };
      }

      const id = this.env.GRAPH_MEMORY_DO.idFromName(userId);
      const stub = this.env.GRAPH_MEMORY_DO.get(id);

      const response = await stub.fetch(new Request(`https://do/getUserProfile?userId=${userId}`));
      
      if (!response.ok) {
        console.error('[ContextHydration] Profile query failed:', response.status);
        return { preferences: [], persona: [], goals: [] };
      }

      const profile = await response.json();
      return profile;

    } catch (error: any) {
      console.error('[ContextHydration] Error getting user profile:', error);
      return { preferences: [], persona: [], goals: [] };
    }
  }

  /**
   * Detect if a message is from an AI assistant to prevent fact extraction
   */
  private isAssistantResponse(message: string): boolean {
    const assistantPatterns = [
      // Common assistant self-identification
      /i am (?:a |an )?(?:ai |artificial intelligence |)?(?:shopping |voice |virtual )?assistant/i,
      /i'm (?:a |an )?(?:ai |artificial intelligence |)?(?:shopping |voice |virtual )?assistant/i,
      /you can call me (?:claude|gemini|gpt|assistant)/i,
      
      // Common assistant disclaimers
      /i don't have access to (?:your )?personal information/i,
      /i (?:don't|can't) (?:have access|access) (?:to )?(?:your )?(?:personal )?(?:data|information)/i,
      /i cannot (?:access|see|view) (?:your )?(?:personal )?(?:data|information)/i,
      
      // Common assistant responses
      /how can i help you (?:with )?(?:shopping )?today/i,
      /what can i (?:do for you|help you with) today/i,
      /i'm here to help (?:you )?(?:with )?(?:your )?shopping/i,
      
      // Typical assistant behavior patterns
      /^(?:hello|hi|hey),? (?:there|user)/i,
      /^i'm (?:here to )?help/i,
      /^welcome to (?:our|the) (?:store|shop)/i,
      
      // Error/limitation messages
      /i (?:cannot|can't) process (?:that|this)/i,
      /i'm (?:sorry|afraid) (?:i )?(?:cannot|can't)/i,
      /(?:sorry|unfortunately),? i (?:cannot|can't|don't)/i,
      
      // Generic assistant language
      /as an? (?:ai |artificial intelligence )?assistant/i,
      /i'm designed to help/i,
      /my purpose is to/i
    ];

    // Check if message matches any assistant pattern
    for (const pattern of assistantPatterns) {
      if (pattern.test(message)) {
        return true;
      }
    }

    // Additional heuristics
    const messageLength = message.length;
    const hasAssistantKeywords = /\b(?:assistant|ai|artificial|intelligence|gemini|claude|gpt|chatbot|bot)\b/i.test(message);
    const hasHelpOffer = /\b(?:help|assist|support)\b/i.test(message);
    const isPolite = /\b(?:please|thank you|you're welcome|my pleasure)\b/i.test(message);
    
    // If message has assistant keywords and is offering help, likely an assistant response
    if (hasAssistantKeywords && hasHelpOffer && messageLength > 20) {
      return true;
    }

    // If message starts with "I am" or "I'm" and mentions being an assistant
    if (/^i (?:am|'m) /.test(message.toLowerCase()) && hasAssistantKeywords) {
      return true;
    }

    return false;
  }
}